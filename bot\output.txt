 * Running on http://127.0.0.1:5000
Press CTRL+C to quit
 * Restarting with stat
2025-06-06 18:00:57.025655: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE SSE2 SSE3 SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
 * Debugger is active!
 * Debugger PIN: 111-625-259
127.0.0.1 - - [06/Jun/2025 18:00:58] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [06/Jun/2025 18:01:05] "GET /favicon.ico HTTP/1.1" 404 -
Received train_ai request
Received new_game request
Sending response: {'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'}
127.0.0.1 - - [06/Jun/2025 18:01:53] "POST /new_game HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/ppppppp1/7p/8/1P6/8/P1PPPPPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h7h6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:02:01] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/ppp1ppp1/3p3p/8/1P6/2N5/P1PPPPPP/R1BQKBNR w KQkq - 0 1', 'ai_move': 'd7d6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:02:11] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p1p1ppp1/1p1p3p/8/1P6/2NP4/P1P1PPPP/R1BQKBNR w KQkq - 0 1', 'ai_move': 'b7b6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:04:53] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p1p1ppp1/3p3p/1p6/1P6/2NP1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'b6b5', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:05:02] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p3ppp1/2pp3p/1N6/1P6/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'c7c6', 'game_over': False, 'result': None, 'evaluation': -1}
127.0.0.1 - - [06/Jun/2025 18:05:06] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p4pp1/2pNp2p/8/1P6/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'e7e6', 'game_over': False, 'result': None, 'evaluation': -2}
127.0.0.1 - - [06/Jun/2025 18:05:10] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqNbnr/p4pp1/4p2p/2p5/1P6/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'c6c5', 'game_over': False, 'result': None, 'evaluation': -2}
127.0.0.1 - - [06/Jun/2025 18:05:13] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqNbnr/p4pp1/7p/2P1p3/8/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'e6e5', 'game_over': False, 'result': None, 'evaluation': -3}
127.0.0.1 - - [06/Jun/2025 18:05:17] "POST /make_move HTTP/1.1" 200 -
Received train_ai request