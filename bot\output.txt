 * Running on http://127.0.0.1:5000
Press CTRL+C to quit
 * Restarting with stat
2025-06-06 18:00:57.025655: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE SSE2 SSE3 SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
 * Debugger is active!
 * Debugger PIN: 111-625-259
127.0.0.1 - - [06/Jun/2025 18:00:58] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [06/Jun/2025 18:01:05] "GET /favicon.ico HTTP/1.1" 404 -
Received train_ai request
Received new_game request
Sending response: {'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'}
127.0.0.1 - - [06/Jun/2025 18:01:53] "POST /new_game HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/ppppppp1/7p/8/1P6/8/P1PPPPPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h7h6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:02:01] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/ppp1ppp1/3p3p/8/1P6/2N5/P1PPPPPP/R1BQKBNR w KQkq - 0 1', 'ai_move': 'd7d6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:02:11] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p1p1ppp1/1p1p3p/8/1P6/2NP4/P1P1PPPP/R1BQKBNR w KQkq - 0 1', 'ai_move': 'b7b6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:04:53] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p1p1ppp1/3p3p/1p6/1P6/2NP1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'b6b5', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [06/Jun/2025 18:05:02] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p3ppp1/2pp3p/1N6/1P6/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'c7c6', 'game_over': False, 'result': None, 'evaluation': -1}
127.0.0.1 - - [06/Jun/2025 18:05:06] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqkbnr/p4pp1/2pNp2p/8/1P6/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'e7e6', 'game_over': False, 'result': None, 'evaluation': -2}
127.0.0.1 - - [06/Jun/2025 18:05:10] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqNbnr/p4pp1/4p2p/2p5/1P6/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'c6c5', 'game_over': False, 'result': None, 'evaluation': -2}
127.0.0.1 - - [06/Jun/2025 18:05:13] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Sending response: {'fen': 'rnbqNbnr/p4pp1/7p/2P1p3/8/3P1N2/P1P1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'e6e5', 'game_over': False, 'result': None, 'evaluation': -3}
127.0.0.1 - - [06/Jun/2025 18:05:17] "POST /make_move HTTP/1.1" 200 -
Received train_ai request







------------------------------------




127.0.0.1 - - [07/Jun/2025 00:11:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:41] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: g2g4
Getting AI move...
AI thinking at depth 3...
127.0.0.1 - - [07/Jun/2025 00:11:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:44] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:45] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:48] "GET /training_status HTTP/1.1" 200 -
AI selected move: d7d5 (nodes: 511, time: 7.27s)
AI move: d7d5
Sending response: {'fen': '1rbqkbr1/ppp1pppp/2n5/1B1p4/2P1nPP1/5N2/PP1P3P/RNBQK2R w KQkq - 0 1', 'ai_move': 'd7d5', 'game_over': False, 'result': None, 'evaluation': -47995}
127.0.0.1 - - [07/Jun/2025 00:11:49] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:51] "GET /training_status HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '1rbqkbr1/pppppppp/2n5/1B6/2P1nP2/5N2/PP1P2PP/RNBQK2R w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -47939}
127.0.0.1 - - [07/Jun/2025 00:11:52] "POST /undo_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:56] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: g2g4
Getting AI move...
AI thinking at depth 3...
127.0.0.1 - - [07/Jun/2025 00:11:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:11:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:01] "GET /training_status HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '1rbqkbr1/pppppppp/2n2n2/1B6/2P1PP2/5N2/PP1P2PP/RNBQK2R b KQkq - 0 1', 'undone_moves': 2, 'evaluation': -47993}
127.0.0.1 - - [07/Jun/2025 00:12:02] "POST /undo_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:05] "GET /training_status HTTP/1.1" 200 -
AI selected move: d7d5 (nodes: 506, time: 8.56s)
AI move: d7d5
Sending response: {'fen': '1rbqkbr1/ppp1pppp/2n2n2/1B1p4/2P1PP2/5N2/PP1P2PP/RNBQK2R w KQkq - 0 1', 'ai_move': 'd7d5', 'game_over': False, 'result': None, 'evaluation': -48039}
127.0.0.1 - - [07/Jun/2025 00:12:05] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:15] "GET /training_status HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '1rbqkbr1/pppppppp/2n2n2/1B6/2P1PP2/8/PP1P2PP/RNBQK1NR w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -48045}
127.0.0.1 - - [07/Jun/2025 00:12:15] "POST /undo_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:30] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:32] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:33] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:34] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:35] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:44] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:46] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:48] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:56] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:12:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:13] "GET /training_status HTTP/1.1" 200 -
Received new_game request
Sending response: {'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'}
127.0.0.1 - - [07/Jun/2025 00:13:13] "POST /new_game HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:15] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d2d4
Getting AI move...
AI thinking at depth 3...
127.0.0.1 - - [07/Jun/2025 00:13:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:22] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:26] "GET /training_status HTTP/1.1" 200 -
AI selected move: b8c6 (nodes: 303, time: 10.40s)
AI move: b8c6
Sending response: {'fen': 'r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'b8c6', 'game_over': False, 'result': None, 'evaluation': -47998}
127.0.0.1 - - [07/Jun/2025 00:13:26] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:34] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:35] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:56] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:13:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:30] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:14:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:15:45] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:32] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:49] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e2e4
Getting AI move...
AI thinking at depth 3...
127.0.0.1 - - [07/Jun/2025 00:16:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:52] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:56] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:57] "GET /training_status HTTP/1.1" 200 -
AI selected move: g8f6 (nodes: 604, time: 7.77s)
AI move: g8f6
Sending response: {'fen': 'r1bqkb1r/pppppppp/2n2n2/8/3PP3/8/PPP2PPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'g8f6', 'game_over': False, 'result': None, 'evaluation': -47992}
127.0.0.1 - - [07/Jun/2025 00:16:58] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:16:59] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f2f4
Getting AI move...
AI thinking at depth 3...
127.0.0.1 - - [07/Jun/2025 00:17:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:08] "GET /training_status HTTP/1.1" 200 -
AI selected move: h8g8 (nodes: 683, time: 7.85s)
AI move: h8g8
Sending response: {'fen': 'r1bqkbr1/pppppppp/2n2n2/8/3PPP2/8/PPP3PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h8g8', 'game_over': False, 'result': None, 'evaluation': -48006}
127.0.0.1 - - [07/Jun/2025 00:17:08] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:20] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: c2c4
Getting AI move...
AI thinking at depth 1...
127.0.0.1 - - [07/Jun/2025 00:17:21] "GET /training_status HTTP/1.1" 200 -
AI selected move: d7d5 (nodes: 15, time: 0.16s)
AI move: d7d5
Sending response: {'fen': 'r1bqkbr1/ppp1pppp/2n2n2/3p4/2PPPP2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd7d5', 'game_over': False, 'result': None, 'evaluation': -48072}
127.0.0.1 - - [07/Jun/2025 00:17:21] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:23] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: c4d5
Getting AI move...
AI thinking at depth 1...
AI selected move: c8f5 (nodes: 15, time: 0.21s)
AI move: c8f5
Sending response: {'fen': 'r2qkbr1/ppp1pppp/2n2n2/3P1b2/3PPP2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c8f5', 'game_over': False, 'result': None, 'evaluation': -47953}
127.0.0.1 - - [07/Jun/2025 00:17:24] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:25] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d5c6
Getting AI move...
AI thinking at depth 1...
AI selected move: d8d5 (nodes: 15, time: 0.65s)
AI move: d8d5
Sending response: {'fen': 'r3kbr1/ppp1pppp/2P2n2/3q1b2/3PPP2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd8d5', 'game_over': False, 'result': None, 'evaluation': -47648}
127.0.0.1 - - [07/Jun/2025 00:17:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:26] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c6b7
Getting AI move...
AI thinking at depth 1...
127.0.0.1 - - [07/Jun/2025 00:17:28] "GET /training_status HTTP/1.1" 200 -
AI selected move: a7a6 (nodes: 15, time: 0.23s)
AI move: a7a6
Sending response: {'fen': 'r3kbr1/1Pp1pppp/p4n2/3q1b2/3PPP2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'a7a6', 'game_over': False, 'result': None, 'evaluation': -47508}
127.0.0.1 - - [07/Jun/2025 00:17:28] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:29] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e4d5
Getting AI move...
AI thinking at depth 1...
AI selected move: e7e5 (nodes: 15, time: 0.51s)
AI move: e7e5
Sending response: {'fen': 'r3kbr1/1Pp2ppp/p4n2/3Ppb2/3P1P2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'e7e5', 'game_over': False, 'result': None, 'evaluation': -46630}
127.0.0.1 - - [07/Jun/2025 00:17:30] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:30] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: b7a8
Getting AI move...
AI thinking at depth 1...
AI selected move: e8d7 (nodes: 3, time: 0.05s)
AI move: e8d7
Sending response: {'fen': 'Q4br1/2pk1ppp/p4n2/3Ppb2/3P1P2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'e8d7', 'game_over': False, 'result': None, 'evaluation': -46972}
127.0.0.1 - - [07/Jun/2025 00:17:31] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:33] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:34] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: a8f8
Getting AI move...
AI thinking at depth 1...
AI selected move: h7h6 (nodes: 15, time: 0.17s)
AI move: h7h6
Sending response: {'fen': '5Qr1/2pk1pp1/p4n1p/3Ppb2/3P1P2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h7h6', 'game_over': False, 'result': None, 'evaluation': -46624}
127.0.0.1 - - [07/Jun/2025 00:17:34] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:35] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f8g8
Getting AI move...
AI thinking at depth 1...
127.0.0.1 - - [07/Jun/2025 00:17:37] "GET /training_status HTTP/1.1" 200 -
AI selected move: d7d6 (nodes: 15, time: 0.70s)
AI move: d7d6
Sending response: {'fen': '6Q1/2p2pp1/p2k1n1p/3Ppb2/3P1P2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd7d6', 'game_over': False, 'result': None, 'evaluation': -46122}
127.0.0.1 - - [07/Jun/2025 00:17:37] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g8g7
Getting AI move...
127.0.0.1 - - [07/Jun/2025 00:17:38] "GET /training_status HTTP/1.1" 200 -
AI thinking at depth 1...
AI selected move: a6a5 (nodes: 15, time: 0.49s)
AI move: a6a5
Sending response: {'fen': '8/2p2pQ1/3k1n1p/p2Ppb2/3P1P2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'a6a5', 'game_over': False, 'result': None, 'evaluation': -45991}
127.0.0.1 - - [07/Jun/2025 00:17:38] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g7f7
127.0.0.1 - - [07/Jun/2025 00:17:39] "GET /training_status HTTP/1.1" 200 -
Getting AI move...
AI thinking at depth 1...
AI selected move: f6e4 (nodes: 15, time: 0.52s)
AI move: f6e4
Sending response: {'fen': '8/2p2Q2/3k3p/p2Ppb2/3PnP2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'f6e4', 'game_over': False, 'result': None, 'evaluation': -45873}
127.0.0.1 - - [07/Jun/2025 00:17:40] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:40] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f7f5
Getting AI move...
AI thinking at depth 1...
127.0.0.1 - - [07/Jun/2025 00:17:41] "GET /training_status HTTP/1.1" 200 -
AI selected move: a5a4 (nodes: 15, time: 0.24s)
AI move: a5a4
Sending response: {'fen': '8/2p5/3k3p/3PpQ2/p2PnP2/8/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'a5a4', 'game_over': False, 'result': None, 'evaluation': -45523}
127.0.0.1 - - [07/Jun/2025 00:17:42] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f5e4
Getting AI move...
AI thinking at depth 1...
127.0.0.1 - - [07/Jun/2025 00:17:43] "GET /training_status HTTP/1.1" 200 -
AI selected move: a4a3 (nodes: 8, time: 0.77s)
AI move: a4a3
Sending response: {'fen': '8/2p5/3k3p/3Pp3/3PQP2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'a4a3', 'game_over': False, 'result': None, 'evaluation': -45178}
127.0.0.1 - - [07/Jun/2025 00:17:44] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e4e5
Getting AI move...
AI thinking at depth 1...
AI selected move: d6d7 (nodes: 1, time: 0.01s)
AI move: d6d7
Sending response: {'fen': '8/2pk4/7p/3PQ3/3P1P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd6d7', 'game_over': False, 'result': None, 'evaluation': -45066}
127.0.0.1 - - [07/Jun/2025 00:17:45] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:45] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e5e6
Getting AI move...
127.0.0.1 - - [07/Jun/2025 00:17:46] "GET /training_status HTTP/1.1" 200 -
AI thinking at depth 1...
AI selected move: d7d8 (nodes: 1, time: 0.01s)
AI move: d7d8
Sending response: {'fen': '3k4/2p5/4Q2p/3P4/3P1P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd7d8', 'game_over': False, 'result': None, 'evaluation': -45050}
127.0.0.1 - - [07/Jun/2025 00:17:46] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e6e7
Getting AI move...
AI thinking at depth 1...
AI selected move: d8c8 (nodes: 2, time: 0.03s)
AI move: d8c8
Sending response: {'fen': '2k5/2p1Q3/7p/3P4/3P1P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd8c8', 'game_over': False, 'result': None, 'evaluation': -45069}
127.0.0.1 - - [07/Jun/2025 00:17:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:47] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:48] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e7c7
Getting AI move...
AI thinking at depth 1...
AI selected move: c8c7 (nodes: 1, time: 0.01s)
AI move: c8c7
Sending response: {'fen': '8/2k5/7p/3P4/3P1P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c8c7', 'game_over': False, 'result': None, 'evaluation': -44095}
127.0.0.1 - - [07/Jun/2025 00:17:48] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:50] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d5d6
Getting AI move...
AI thinking at depth 1...
AI selected move: c7c6 (nodes: 8, time: 0.12s)
AI move: c7c6
Sending response: {'fen': '8/8/2kP3p/8/3P1P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c7c6', 'game_over': False, 'result': None, 'evaluation': -44064}
127.0.0.1 - - [07/Jun/2025 00:17:51] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:51] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d6d7
Getting AI move...
AI thinking at depth 1...
AI selected move: c6d7 (nodes: 8, time: 0.06s)
AI move: c6d7
Sending response: {'fen': '8/3k4/7p/8/3P1P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c6d7', 'game_over': False, 'result': None, 'evaluation': -44022}
127.0.0.1 - - [07/Jun/2025 00:17:52] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:56] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d4d5
Getting AI move...
AI thinking at depth 1...
AI selected move: d7d6 (nodes: 8, time: 0.63s)
AI move: d7d6
Sending response: {'fen': '8/8/3k3p/3P4/5P2/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd7d6', 'game_over': False, 'result': None, 'evaluation': -43989}
127.0.0.1 - - [07/Jun/2025 00:17:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:58] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:17:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:00] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f4f5
Getting AI move...
AI thinking at depth 1...
AI selected move: h6h5 (nodes: 7, time: 0.06s)
AI move: h6h5
Sending response: {'fen': '8/8/3k4/3P1P1p/8/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h6h5', 'game_over': False, 'result': None, 'evaluation': -43970}
127.0.0.1 - - [07/Jun/2025 00:18:00] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:01] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f5f6
Getting AI move...
AI thinking at depth 1...
AI selected move: h5h4 (nodes: 6, time: 0.07s)
AI move: h5h4
Sending response: {'fen': '8/8/3k1P2/3P4/7p/p7/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h5h4', 'game_over': False, 'result': None, 'evaluation': -43965}
127.0.0.1 - - [07/Jun/2025 00:18:01] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:02] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f6f7
Getting AI move...
AI thinking at depth 1...
AI selected move: h4h3 (nodes: 7, time: 0.32s)
AI move: h4h3
Sending response: {'fen': '8/5P2/3k4/3P4/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'h4h3', 'game_over': False, 'result': None, 'evaluation': -43942}
127.0.0.1 - - [07/Jun/2025 00:18:03] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:03] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f7f8
Getting AI move...
AI thinking at depth 1...
AI selected move: d6e5 (nodes: 3, time: 0.03s)
AI move: d6e5
Sending response: {'fen': '5Q2/8/8/3Pk3/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd6e5', 'game_over': False, 'result': None, 'evaluation': -44734}
127.0.0.1 - - [07/Jun/2025 00:18:04] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:04] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f8e8
Getting AI move...
AI thinking at depth 1...
AI selected move: e5d6 (nodes: 3, time: 0.03s)
AI move: e5d6
Sending response: {'fen': '4Q3/8/3k4/3P4/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'e5d6', 'game_over': False, 'result': None, 'evaluation': -44753}
127.0.0.1 - - [07/Jun/2025 00:18:05] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:07] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e8d8
Getting AI move...
AI thinking at depth 1...
AI selected move: d6c5 (nodes: 2, time: 0.02s)
AI move: d6c5
Sending response: {'fen': '3Q4/8/8/2kP4/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd6c5', 'game_over': False, 'result': None, 'evaluation': -44747}
127.0.0.1 - - [07/Jun/2025 00:18:08] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:08] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d5d6
Getting AI move...
AI thinking at depth 1...
AI selected move: c5c6 (nodes: 4, time: 0.14s)
AI move: c5c6
Sending response: {'fen': '3Q4/8/2kP4/8/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c5c6', 'game_over': False, 'result': None, 'evaluation': -44754}
127.0.0.1 - - [07/Jun/2025 00:18:10] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:10] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d6d7
Getting AI move...
AI thinking at depth 1...
AI selected move: c6c5 (nodes: 4, time: 0.03s)
AI move: c6c5
Sending response: {'fen': '3Q4/3P4/8/2k5/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c6c5', 'game_over': False, 'result': None, 'evaluation': -44726}
127.0.0.1 - - [07/Jun/2025 00:18:11] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:11] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d8e8
Getting AI move...
AI thinking at depth 1...
AI selected move: c5b6 (nodes: 5, time: 0.08s)
AI move: c5b6
Sending response: {'fen': '4Q3/3P4/1k6/8/8/p6p/PP4PP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'c5b6', 'game_over': False, 'result': None, 'evaluation': -44738}
127.0.0.1 - - [07/Jun/2025 00:18:12] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:16] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d1b3
Getting AI move...
AI thinking at depth 1...
AI selected move: b6a5 (nodes: 5, time: 0.24s)
AI move: b6a5
Sending response: {'fen': '4Q3/3P4/8/k7/8/pQ5p/PP4PP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'b6a5', 'game_over': False, 'result': None, 'evaluation': -44702}
127.0.0.1 - - [07/Jun/2025 00:18:17] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:20] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e8a8
Game over after player move! Result: 1-0
Sending response: {'fen': 'Q7/3P4/8/k7/8/pQ5p/PP4PP/RNB1KBNR b KQkq - 0 1', 'ai_move': None, 'game_over': True, 'result': '1-0', 'evaluation': -44215}
127.0.0.1 - - [07/Jun/2025 00:18:20] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:22] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:27] "GET /training_status HTTP/1.1" 200 -
Received new_game request
Sending response: {'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'}
127.0.0.1 - - [07/Jun/2025 00:18:27] "POST /new_game HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:18:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:30] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:19:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:20:22] "GET /training_status HTTP/1.1" 200 -
Background training completed!
127.0.0.1 - - [07/Jun/2025 00:20:28] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d2d3
Getting AI move...
AI thinking at depth 1...
AI selected move: e7e5 (nodes: 15, time: 0.12s)
AI move: e7e5
Sending response: {'fen': 'rnbqkbnr/pppp1ppp/8/4p3/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'e7e5', 'game_over': False, 'result': None, 'evaluation': -48026}
127.0.0.1 - - [07/Jun/2025 00:29:20] "POST /make_move HTTP/1.1" 200 -
 * Detected change in 'C:\\Users\\<USER>\\OneDrive - University of Suffolk (1)\\projects\\AI\\Programming AI\\chess bot\\bot\\chess_ai.py', reloading
 * Restarting with stat
2025-06-07 00:34:23.542370: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE SSE2 SSE3 SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
 * Debugger is active!
 * Debugger PIN: 111-625-259
 * Detected change in 'C:\\Users\\<USER>\\OneDrive - University of Suffolk (1)\\projects\\AI\\Programming AI\\chess bot\\bot\\chess_ai.py', reloading
 * Restarting with stat
2025-06-07 00:35:26.592009: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE SSE2 SSE3 SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
 * Debugger is active!
 * Debugger PIN: 111-625-259
127.0.0.1 - - [07/Jun/2025 00:35:48] "GET / HTTP/1.1" 200 -
Received make_move request
Player move: d2d4
Getting AI move...
AI evaluating 20 legal moves...
AI selected move: d7d5 (time: 0.02s, score: 80.0)
AI move: d7d5
Sending response: {'fen': 'rnbqkbnr/ppp1pppp/8/3p4/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd7d5', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [07/Jun/2025 00:35:55] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: b1c3
Getting AI move...
AI evaluating 27 legal moves...
AI selected move: b8c6 (time: 0.03s, score: 290.0)
AI move: b8c6
Sending response: {'fen': 'r1bqkbnr/ppp1pppp/2n5/3p4/3P4/2N5/PPP1PPPP/R1BQKBNR w KQkq - 0 1', 'ai_move': 'b8c6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [07/Jun/2025 00:36:00] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g1f3
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: g8f6 (time: 0.03s, score: 290.0)
AI move: g8f6
Sending response: {'fen': 'r1bqkb1r/ppp1pppp/2n2n2/3p4/3P4/2N2N2/PPP1PPPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'g8f6', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [07/Jun/2025 00:36:21] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e2e4
Getting AI move...
AI evaluating 31 legal moves...
AI selected move: c8e6 (time: 0.02s, score: 110.0)
AI move: c8e6
Sending response: {'fen': 'r2qkb1r/ppp1pppp/2n1bn2/3p4/3PP3/2N2N2/PPP2PPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'c8e6', 'game_over': False, 'result': None, 'evaluation': 30}
127.0.0.1 - - [07/Jun/2025 00:36:24] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e4d5
Getting AI move...
AI evaluating 34 legal moves...
AI selected move: d8d5 (time: 0.07s, score: -330.0)
AI move: d8d5
Sending response: {'fen': 'r3kb1r/ppp1pppp/2n1bn2/3q4/3P4/2N2N2/PPP2PPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'd8d5', 'game_over': False, 'result': None, 'evaluation': 10}
127.0.0.1 - - [07/Jun/2025 00:36:27] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f3e5
Getting AI move...
AI evaluating 43 legal moves...
AI selected move: a7a6 (time: 0.05s, score: -1105.0)
AI move: a7a6
Sending response: {'fen': 'r3kb1r/1pp1pppp/p1n1bn2/3qN3/3P4/2N5/PPP2PPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'a7a6', 'game_over': False, 'result': None, 'evaluation': 30}
127.0.0.1 - - [07/Jun/2025 00:36:31] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e5c6
Getting AI move...
AI evaluating 42 legal moves...
AI selected move: b7c6 (time: 0.03s, score: -1530.0)
AI move: b7c6
Sending response: {'fen': 'r3kb1r/2p1pppp/p1p1bn2/3q4/3P4/2N5/PPP2PPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'b7c6', 'game_over': False, 'result': None, 'evaluation': 30}
127.0.0.1 - - [07/Jun/2025 00:36:33] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c3d5
Getting AI move...
AI evaluating 26 legal moves...
AI selected move: c6d5 (time: 0.03s, score: -1095.0)
AI move: c6d5
Sending response: {'fen': 'r3kb1r/2p1pppp/p3bn2/3p4/3P4/8/PPP2PPP/R1BQKB1R w KQkq - 0 1', 'ai_move': 'c6d5', 'game_over': False, 'result': None, 'evaluation': 575}
127.0.0.1 - - [07/Jun/2025 00:36:36] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f1b5
Getting AI move...
AI evaluating 5 legal moves...
AI selected move: a6b5 (time: 0.01s, score: -1600.0)
AI move: a6b5
Sending response: {'fen': 'r3kb1r/2p1pppp/4bn2/1p1p4/3P4/8/PPP2PPP/R1BQK2R w KQkq - 0 1', 'ai_move': 'a6b5', 'game_over': False, 'result': None, 'evaluation': 260}
127.0.0.1 - - [07/Jun/2025 00:36:39] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: d1d3
Getting AI move...
AI evaluating 29 legal moves...
AI selected move: a8a2 (time: 0.03s, score: -75.0)
AI move: a8a2
Sending response: {'fen': '4kb1r/2p1pppp/4bn2/1p1p4/3P4/3Q4/rPP2PPP/R1B1K2R w KQkq - 0 1', 'ai_move': 'a8a2', 'game_over': False, 'result': None, 'evaluation': 160}
127.0.0.1 - - [07/Jun/2025 00:36:42] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: d3a3
Getting AI move...
AI evaluating 23 legal moves...
AI selected move: h7h6 (time: 0.03s, score: -490.0)
AI move: h7h6
Sending response: {'fen': '4kb1r/2p1ppp1/4bn1p/1p1p4/3P4/Q7/rPP2PPP/R1B1K2R w KQkq - 0 1', 'ai_move': 'h7h6', 'game_over': False, 'result': None, 'evaluation': 145}
127.0.0.1 - - [07/Jun/2025 00:36:44] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: a3a2
Getting AI move...
AI evaluating 21 legal moves...
AI selected move: e6f5 (time: 0.20s, score: -90.0)
AI move: e6f5
Sending response: {'fen': '4kb1r/2p1ppp1/5n1p/1p1p1b2/3P4/8/QPP2PPP/R1B1K2R w KQkq - 0 1', 'ai_move': 'e6f5', 'game_over': False, 'result': None, 'evaluation': 650}
127.0.0.1 - - [07/Jun/2025 00:36:52] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: a2d5
Getting AI move...
AI evaluating 27 legal moves...
AI selected move: f6d5 (time: 0.04s, score: -1665.0)
AI move: f6d5
Sending response: {'fen': '4kb1r/2p1ppp1/7p/1p1n1b2/3P4/8/1PP2PPP/R1B1K2R w KQkq - 0 1', 'ai_move': 'f6d5', 'game_over': False, 'result': None, 'evaluation': -130}
127.0.0.1 - - [07/Jun/2025 00:36:53] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f2f4
Getting AI move...
AI evaluating 29 legal moves...
AI selected move: e7e5 (time: 0.03s, score: -715.0)
AI move: e7e5
Sending response: {'fen': '4kb1r/2p2pp1/7p/1p1npb2/3P1P2/8/1PP3PP/R1B1K2R w KQkq - 0 1', 'ai_move': 'e7e5', 'game_over': False, 'result': None, 'evaluation': -190}
127.0.0.1 - - [07/Jun/2025 00:36:56] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g2g4
Getting AI move...
AI evaluating 36 legal moves...
AI selected move: f8d6 (time: 0.02s, score: -715.0)
AI move: f8d6
Sending response: {'fen': '4k2r/2p2pp1/3b3p/1p1npb2/3P1PP1/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'f8d6', 'game_over': False, 'result': None, 'evaluation': -220}
127.0.0.1 - - [07/Jun/2025 00:36:57] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g4f5
Getting AI move...
AI evaluating 29 legal moves...
AI selected move: d6c5 (time: 0.04s, score: -685.0)
AI move: d6c5
Sending response: {'fen': '4k2r/2p2pp1/7p/1pbnpP2/3P1P2/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'd6c5', 'game_over': False, 'result': None, 'evaluation': 130}
127.0.0.1 - - [07/Jun/2025 00:36:59] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f5f6
Getting AI move...
AI evaluating 30 legal moves...
AI selected move: c5d4 (time: 0.08s, score: -915.0)
AI move: c5d4
Sending response: {'fen': '4k2r/2p2pp1/5P1p/1p1np3/3b1P2/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'c5d4', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [07/Jun/2025 00:37:00] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f6g7
Getting AI move...
AI evaluating 29 legal moves...
AI selected move: b5b4 (time: 0.16s, score: -700.0)
AI move: b5b4
Sending response: {'fen': '4k2r/2p2pP1/7p/3np3/1p1b1P2/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'b5b4', 'game_over': False, 'result': None, 'evaluation': 115}
127.0.0.1 - - [07/Jun/2025 00:37:01] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g7h8
Getting AI move...
AI evaluating 2 legal moves...
AI selected move: e8d7 (time: 0.01s, score: 20.0)
AI move: e8d7
Sending response: {'fen': '7Q/2pk1p2/7p/3np3/1p1b1P2/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'e8d7', 'game_over': False, 'result': None, 'evaluation': 1365}
127.0.0.1 - - [07/Jun/2025 00:37:03] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: h8d8
Getting AI move...
AI evaluating 3 legal moves...
AI selected move: d7d8 (time: 0.01s, score: -1365.0)
AI move: d7d8
Sending response: {'fen': '3k4/2p2p2/7p/3np3/1p1b1P2/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'd7d8', 'game_over': False, 'result': None, 'evaluation': 485}
127.0.0.1 - - [07/Jun/2025 00:37:04] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f4f5
Getting AI move...
AI evaluating 24 legal moves...
AI selected move: e5e4 (time: 0.02s, score: -40.0)
AI move: e5e4
Sending response: {'fen': '3k4/2p2p2/7p/3n1P2/1p1bp3/8/1PP4P/R1B1K2R w KQkq - 0 1', 'ai_move': 'e5e4', 'game_over': False, 'result': None, 'evaluation': 490}
127.0.0.1 - - [07/Jun/2025 00:37:06] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: h1g1
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: b4b3 (time: 0.04s, score: 165.0)
AI move: b4b3
Sending response: {'fen': '3k4/2p2p2/7p/3n1P2/3bp3/1p6/1PP4P/R1B1K1R1 w KQkq - 0 1', 'ai_move': 'b4b3', 'game_over': False, 'result': None, 'evaluation': 485}
127.0.0.1 - - [07/Jun/2025 00:37:08] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g1g7
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: d8e8 (time: 0.03s, score: 575.0)
AI move: d8e8
Sending response: {'fen': '4k3/2p2pR1/7p/3n1P2/3bp3/1p6/1PP4P/R1B1K3 w KQkq - 0 1', 'ai_move': 'd8e8', 'game_over': False, 'result': None, 'evaluation': 495}
127.0.0.1 - - [07/Jun/2025 00:37:09] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: g7f7
Getting AI move...
AI evaluating 26 legal moves...
AI selected move: e8f7 (time: 0.11s, score: -1330.0)
AI move: e8f7
Sending response: {'fen': '8/2p2k2/7p/3n1P2/3bp3/1p6/1PP4P/R1B1K3 w KQkq - 0 1', 'ai_move': 'e8f7', 'game_over': False, 'result': None, 'evaluation': 95}
127.0.0.1 - - [07/Jun/2025 00:37:10] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: f5f6
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: f7f6 (time: 0.03s, score: -1020.0)
AI move: f7f6
Sending response: {'fen': '8/2p5/5k1p/3n4/3bp3/1p6/1PP4P/R1B1K3 w KQkq - 0 1', 'ai_move': 'f7f6', 'game_over': False, 'result': None, 'evaluation': 5}
127.0.0.1 - - [07/Jun/2025 00:37:12] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c2c3
Getting AI move...
AI evaluating 25 legal moves...
AI selected move: d5c3 (time: 0.02s, score: -1110.0)
AI move: d5c3
Sending response: {'fen': '8/2p5/5k1p/8/3bp3/1pn5/1P5P/R1B1K3 w KQkq - 0 1', 'ai_move': 'd5c3', 'game_over': False, 'result': None, 'evaluation': -85}
127.0.0.1 - - [07/Jun/2025 00:37:14] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: b2c3
Getting AI move...
AI evaluating 20 legal moves...
AI selected move: d4g1 (time: 0.03s, score: -850.0)
AI move: d4g1
Sending response: {'fen': '8/2p5/5k1p/8/4p3/1pP5/7P/R1B1K1b1 w KQkq - 0 1', 'ai_move': 'd4g1', 'game_over': False, 'result': None, 'evaluation': 255}
127.0.0.1 - - [07/Jun/2025 00:37:16] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: a1a6
Getting AI move...
AI evaluating 7 legal moves...
AI selected move: g1b6 (time: 0.01s, score: -875.0)
AI move: g1b6
Sending response: {'fen': '8/2p5/Rb3k1p/8/4p3/1pP5/7P/2B1K3 w KQkq - 0 1', 'ai_move': 'g1b6', 'game_over': False, 'result': None, 'evaluation': 230}
127.0.0.1 - - [07/Jun/2025 00:37:18] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: a6b6
Getting AI move...
AI evaluating 7 legal moves...
AI selected move: c7b6 (time: 0.05s, score: -1225.0)
AI move: c7b6
Sending response: {'fen': '8/8/1p3k1p/8/4p3/1pP5/7P/2B1K3 w KQkq - 0 1', 'ai_move': 'c7b6', 'game_over': False, 'result': None, 'evaluation': 90}
127.0.0.1 - - [07/Jun/2025 00:37:19] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c1g5
Getting AI move...
AI evaluating 8 legal moves...
AI selected move: h6g5 (time: 0.03s, score: -1160.0)
AI move: h6g5
Sending response: {'fen': '8/8/1p3k2/6p1/4p3/1pP5/7P/4K3 w KQkq - 0 1', 'ai_move': 'h6g5', 'game_over': False, 'result': None, 'evaluation': -225}
127.0.0.1 - - [07/Jun/2025 00:37:23] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c3c4
Getting AI move...
AI evaluating 11 legal moves...
AI selected move: b3b2 (time: 0.01s, score: -450.0)
AI move: b3b2
Sending response: {'fen': '8/8/1p3k2/6p1/2P1p3/8/1p5P/4K3 w KQkq - 0 1', 'ai_move': 'b3b2', 'game_over': False, 'result': None, 'evaluation': -235}
127.0.0.1 - - [07/Jun/2025 00:37:26] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c4c5
Getting AI move...
AI evaluating 12 legal moves...
AI selected move: b2b1 (time: 0.01s, score: -1490.0)
AI move: b2b1
Sending response: {'fen': '8/8/1p3k2/2P3p1/4p3/8/7P/1q2K3 w KQkq - 0 1', 'ai_move': 'b2b1', 'game_over': False, 'result': None, 'evaluation': -1035}
127.0.0.1 - - [07/Jun/2025 00:37:28] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e1e2
Getting AI move...
AI evaluating 25 legal moves...
AI selected move: b1d1 (time: 0.02s, score: -2100.0)
AI move: b1d1
Sending response: {'fen': '8/8/1p3k2/2P3p1/4p3/8/4K2P/3q4 w KQkq - 0 1', 'ai_move': 'b1d1', 'game_over': False, 'result': None, 'evaluation': -1040}
127.0.0.1 - - [07/Jun/2025 00:37:30] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e2e3
Getting AI move...
AI evaluating 31 legal moves...
AI selected move: b6c5 (time: 0.04s, score: -2100.0)
AI move: b6c5
Sending response: {'fen': '8/8/5k2/2p3p1/4p3/4K3/7P/3q4 w KQkq - 0 1', 'ai_move': 'b6c5', 'game_over': False, 'result': None, 'evaluation': -1125}
127.0.0.1 - - [07/Jun/2025 00:37:31] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e3e4
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: c5c4 (time: 0.05s, score: -2000.0)
AI move: c5c4
Sending response: {'fen': '8/8/5k2/6p1/2p1K3/8/7P/3q4 w KQkq - 0 1', 'ai_move': 'c5c4', 'game_over': False, 'result': None, 'evaluation': -1000}
127.0.0.1 - - [07/Jun/2025 00:37:32] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: h2h3
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: d1f1 (time: 0.06s, score: -2000.0)
AI move: d1f1
Sending response: {'fen': '8/8/5k2/6p1/2p1K3/7P/8/5q2 w KQkq - 0 1', 'ai_move': 'd1f1', 'game_over': False, 'result': None, 'evaluation': -995}
127.0.0.1 - - [07/Jun/2025 00:37:36] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: h3h4
Getting AI move...
AI evaluating 23 legal moves...
AI selected move: f1d1 (time: 0.02s, score: -2000.0)
AI move: f1d1
Sending response: {'fen': '8/8/5k2/6p1/2p1K2P/8/8/3q4 w KQkq - 0 1', 'ai_move': 'f1d1', 'game_over': False, 'result': None, 'evaluation': -1005}
127.0.0.1 - - [07/Jun/2025 00:37:37] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: h4h5
Getting AI move...
AI evaluating 27 legal moves...
AI selected move: d1d3 (time: 0.02s, score: -2055.0)
AI move: d1d3
Sending response: {'fen': '8/8/5k2/6pP/2p1K3/3q4/8/8 w KQkq - 0 1', 'ai_move': 'd1d3', 'game_over': True, 'result': '0-1', 'evaluation': -1060}
127.0.0.1 - - [07/Jun/2025 00:37:39] "POST /make_move HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '8/8/5k2/6p1/2p1K2P/8/8/3q4 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -1005}
127.0.0.1 - - [07/Jun/2025 00:39:26] "POST /undo_move HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '8/8/5k2/6p1/2p1K3/7P/8/5q2 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -995}
127.0.0.1 - - [07/Jun/2025 00:39:26] "POST /undo_move HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '8/8/5k2/6p1/2p1K3/8/7P/3q4 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -1000}
127.0.0.1 - - [07/Jun/2025 00:39:27] "POST /undo_move HTTP/1.1" 200 -
Received undo_move request
Received undo_move request
Sending response: {'fen': '8/8/5k2/2p3p1/4p3/4K3/7P/3q4 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -1125}
127.0.0.1 - - [07/Jun/2025 00:39:27] "POST /undo_move HTTP/1.1" 200 -
Sending response: {'fen': '8/8/1p3k2/2P3p1/4p3/8/4K2P/3q4 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -1040}
127.0.0.1 - - [07/Jun/2025 00:39:27] "POST /undo_move HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '8/8/1p3k2/2P3p1/4p3/8/7P/1q2K3 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -1035}
127.0.0.1 - - [07/Jun/2025 00:39:28] "POST /undo_move HTTP/1.1" 200 -
Received undo_move request
Sending response: {'fen': '8/8/1p3k2/6p1/2P1p3/8/1p5P/4K3 w KQkq - 0 1', 'undone_moves': 2, 'evaluation': -235}
127.0.0.1 - - [07/Jun/2025 00:39:29] "POST /undo_move HTTP/1.1" 200 -
Received make_move request
Player move: c4c5
Getting AI move...
AI evaluating 12 legal moves...
AI selected move: b2b1 (time: 0.01s, score: -1490.0)
AI move: b2b1
Sending response: {'fen': '8/8/1p3k2/2P3p1/4p3/8/7P/1q2K3 w KQkq - 0 1', 'ai_move': 'b2b1', 'game_over': False, 'result': None, 'evaluation': -1035}
127.0.0.1 - - [07/Jun/2025 00:39:31] "POST /make_move HTTP/1.1" 200 -
Received new_game request
Sending response: {'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'}
127.0.0.1 - - [07/Jun/2025 00:39:33] "POST /new_game HTTP/1.1" 200 -
Received train_ai request
Starting background training with 50 games...
Sending response: {'message': 'Training started with 50 games. Check status for progress.'}
127.0.0.1 - - [07/Jun/2025 00:39:36] "POST /train_ai HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:44] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:45] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:46] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:48] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:52] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 00:39:53] "GET /training_status HTTP/1.1" 200 -





----------------------------------------------




AI evaluating 7 legal moves...
AI selected move: g2h1 (time: 0.05s, score: -1270.0)
AI move: g2h1
Sending response: {'fen': '8/8/8/1B1kp2R/8/8/1PP2P1P/2B1K2q w KQkq - 0 1', 'ai_move': 'g2h1', 'game_over': False, 'result': None, 'evaluation': 545}
127.0.0.1 - - [07/Jun/2025 03:17:18] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:20] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e1e2
Getting AI move...
AI evaluating 14 legal moves...
AI selected move: h1d1 (time: 0.03s, score: -1800.0)
AI move: h1d1
Sending response: {'fen': '8/8/8/1B1kp2R/8/8/1PP1KP1P/2Bq4 w KQkq - 0 1', 'ai_move': 'h1d1', 'game_over': False, 'result': None, 'evaluation': 530}
127.0.0.1 - - [07/Jun/2025 03:17:21] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:23] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e2d1
Getting AI move...
AI evaluating 5 legal moves...
AI selected move: d5d6 (time: 0.04s, score: -175.0)
AI move: d5d6
Sending response: {'fen': '8/8/3k4/1B2p2R/8/8/1PP2P1P/2BK4 w KQkq - 0 1', 'ai_move': 'd5d6', 'game_over': False, 'result': None, 'evaluation': 1475}
127.0.0.1 - - [07/Jun/2025 03:17:24] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:28] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: b5d7
Getting AI move...
AI evaluating 6 legal moves...
AI selected move: d6d7 (time: 0.05s, score: -780.0)
AI move: d6d7
Sending response: {'fen': '8/3k4/8/4p2R/8/8/1PP2P1P/2BK4 w KQkq - 0 1', 'ai_move': 'd6d7', 'game_over': False, 'result': None, 'evaluation': 1120}
127.0.0.1 - - [07/Jun/2025 03:17:28] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:29] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: h5e5
Getting AI move...
AI evaluating 5 legal moves...
127.0.0.1 - - [07/Jun/2025 03:17:30] "GET /training_status HTTP/1.1" 200 -
AI selected move: d7c6 (time: 0.02s, score: -285.0)
AI move: d7c6
Sending response: {'fen': '8/8/2k5/4R3/8/8/1PP2P1P/2BK4 w KQkq - 0 1', 'ai_move': 'd7c6', 'game_over': False, 'result': None, 'evaluation': 1285}
127.0.0.1 - - [07/Jun/2025 03:17:30] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:32] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:33] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f2f4
Getting AI move...
AI evaluating 5 legal moves...
AI selected move: c6b6 (time: 0.01s, score: -265.0)
AI move: c6b6
Sending response: {'fen': '8/8/1k6/4R3/5P2/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'c6b6', 'game_over': False, 'result': None, 'evaluation': 1275}
127.0.0.1 - - [07/Jun/2025 03:17:33] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:34] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f4f5
Getting AI move...
AI evaluating 5 legal moves...
AI selected move: b6a6 (time: 0.01s, score: -265.0)
AI move: b6a6
Sending response: {'fen': '8/8/k7/4RP2/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'b6a6', 'game_over': False, 'result': None, 'evaluation': 1275}
127.0.0.1 - - [07/Jun/2025 03:17:35] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:35] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f5f6
Getting AI move...
AI evaluating 3 legal moves...
AI selected move: a6b6 (time: 0.02s, score: -245.0)
AI move: a6b6
Sending response: {'fen': '8/8/1k3P2/4R3/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'a6b6', 'game_over': False, 'result': None, 'evaluation': 1295}
127.0.0.1 - - [07/Jun/2025 03:17:36] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:36] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f6f7
Getting AI move...
AI evaluating 5 legal moves...
AI selected move: b6a6 (time: 0.02s, score: -85.0)
AI move: b6a6
Sending response: {'fen': '8/5P2/k7/4R3/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'b6a6', 'game_over': False, 'result': None, 'evaluation': 1295}
127.0.0.1 - - [07/Jun/2025 03:17:37] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:37] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f7f8
Getting AI move...
AI evaluating 3 legal moves...
AI selected move: a6b6 (time: 0.00s, score: 895.0)
AI move: a6b6
Sending response: {'fen': '5Q2/8/1k6/4R3/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'a6b6', 'game_over': False, 'result': None, 'evaluation': 2000}
127.0.0.1 - - [07/Jun/2025 03:17:38] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:44] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e5e6
Getting AI move...
AI evaluating 5 legal moves...
AI selected move: b6a5 (time: 0.02s, score: 885.0)
AI move: b6a5
Sending response: {'fen': '5Q2/8/4R3/k7/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'b6a5', 'game_over': False, 'result': None, 'evaluation': 2000}
127.0.0.1 - - [07/Jun/2025 03:17:45] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:45] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:46] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f8f4
Getting AI move...
AI evaluating 1 legal moves...
AI selected move: a5b5 (time: 0.02s, score: 540.0)
AI move: a5b5
Sending response: {'fen': '8/8/4R3/1k6/5Q2/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'a5b5', 'game_over': False, 'result': None, 'evaluation': 2000}
127.0.0.1 - - [07/Jun/2025 03:17:47] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:48] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e6e5
Getting AI move...
AI evaluating 3 legal moves...
AI selected move: b5a6 (time: 0.05s, score: 30.0)
AI move: b5a6
Sending response: {'fen': '8/8/k7/4R3/5Q2/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'b5a6', 'game_over': False, 'result': None, 'evaluation': 2000}
127.0.0.1 - - [07/Jun/2025 03:17:49] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:49] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f4f6
Getting AI move...
AI evaluating 2 legal moves...
AI selected move: a6a7 (time: 0.00s, score: 105.0)
AI move: a6a7
Sending response: {'fen': '8/k7/5Q2/4R3/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'a6a7', 'game_over': False, 'result': None, 'evaluation': 2000}
127.0.0.1 - - [07/Jun/2025 03:17:50] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:51] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e5e7
Getting AI move...
AI evaluating 2 legal moves...
AI selected move: a7b8 (time: 0.02s, score: 465.0)
AI move: a7b8
Sending response: {'fen': '1k6/4R3/5Q2/8/8/8/1PP4P/2BK4 w KQkq - 0 1', 'ai_move': 'a7b8', 'game_over': False, 'result': None, 'evaluation': 2000}
127.0.0.1 - - [07/Jun/2025 03:17:52] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:53] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f6f8
Game over after player move! Result: 1-0
Sending response: {'fen': '1k3Q2/4R3/8/8/8/8/1PP4P/2BK4 b KQkq - 0 1', 'ai_move': None, 'game_over': True, 'result': '1-0', 'evaluation': -2000}
127.0.0.1 - - [07/Jun/2025 03:17:53] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:56] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:17:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:22] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:32] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:33] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:34] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:44] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:46] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:48] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:52] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:55] "GET /training_status HTTP/1.1" 200 -
Received new_game request
Sending response: {'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'}
127.0.0.1 - - [07/Jun/2025 03:18:55] "POST /new_game HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:58] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d2d4
Getting AI move...
AI evaluating 20 legal moves...
AI selected move: d7d5 (time: 0.02s, score: 80.0)
AI move: d7d5
Sending response: {'fen': 'rnbqkbnr/ppp1pppp/8/3p4/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 0 1', 'ai_move': 'd7d5', 'game_over': False, 'result': None, 'evaluation': 0}
127.0.0.1 - - [07/Jun/2025 03:18:58] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:18:59] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d1d3
Getting AI move...
AI evaluating 27 legal moves...
AI selected move: b8c6 (time: 0.05s, score: 865.0)
AI move: b8c6
Sending response: {'fen': 'r1bqkbnr/ppp1pppp/2n5/3p4/3P4/3Q4/PPP1PPPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'b8c6', 'game_over': False, 'result': None, 'evaluation': -30}
127.0.0.1 - - [07/Jun/2025 03:19:00] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:03] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d3c3
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: g8f6 (time: 0.02s, score: 535.0)
AI move: g8f6
Sending response: {'fen': 'r1bqkb1r/ppp1pppp/2n2n2/3p4/3P4/2Q5/PPP1PPPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'g8f6', 'game_over': False, 'result': None, 'evaluation': -70}
127.0.0.1 - - [07/Jun/2025 03:19:03] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:06] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: c3f3
Getting AI move...
AI evaluating 30 legal moves...
AI selected move: c8e6 (time: 0.04s, score: 225.0)
AI move: c8e6
Sending response: {'fen': 'r2qkb1r/ppp1pppp/2n1bn2/3p4/3P4/5Q2/PPP1PPPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'c8e6', 'game_over': False, 'result': None, 'evaluation': -90}
127.0.0.1 - - [07/Jun/2025 03:19:06] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:09] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f3f5
Getting AI move...
AI evaluating 29 legal moves...
AI selected move: d8d6 (time: 0.04s, score: -105.0)
AI move: d8d6
Sending response: {'fen': 'r3kb1r/ppp1pppp/2nqbn2/3p1Q2/3P4/8/PPP1PPPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'd8d6', 'game_over': False, 'result': None, 'evaluation': -100}
127.0.0.1 - - [07/Jun/2025 03:19:10] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:15] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: f5d5
Getting AI move...
AI evaluating 42 legal moves...
AI selected move: a7a6 (time: 0.09s, score: -860.0)
AI move: a7a6
Sending response: {'fen': 'r3kb1r/1pp1pppp/p1nqbn2/3Q4/3P4/8/PPP1PPPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'a7a6', 'game_over': False, 'result': None, 'evaluation': 40}
127.0.0.1 - - [07/Jun/2025 03:19:16] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:17] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: d5d6
Getting AI move...
AI evaluating 36 legal moves...
AI selected move: e7d6 (time: 0.22s, score: -1895.0)
AI move: e7d6
Sending response: {'fen': 'r3kb1r/1pp2ppp/p1npbn2/8/3P4/8/PPP1PPPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'e7d6', 'game_over': False, 'result': None, 'evaluation': 10}
127.0.0.1 - - [07/Jun/2025 03:19:18] "POST /make_move HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:44] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:52] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:19:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:38] "GET /training_status HTTP/1.1" 2127.0.0.1 - - [07/Jun/2025 03:20:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:20:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:30] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:21:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:22:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:34] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:23:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:24:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:52] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:25:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:21] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:33] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:34] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:26:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:27:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:27:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:27:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:27:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 03:27:19] "GET /training_status HTTP/1.1" 200 -
00 -
00 -
00 -
Background training completed!
127.0.0.1 - - [07/Jun/2025 03:27:26] "GET /training_status HTTP/1.1" 200 -
Received make_move request
Player move: e2e4
Getting AI move...
AI evaluating 40 legal moves...
AI selected move: h7h6 (time: 0.04s, score: -945.0)
AI move: h7h6
Sending response: {'fen': 'r3kb1r/1pp2pp1/p1npbn1p/8/3PP3/8/PPP2PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'h7h6', 'game_over': False, 'result': None, 'evaluation': 60}
127.0.0.1 - - [07/Jun/2025 03:51:38] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: e4e5
Getting AI move...
AI evaluating 42 legal moves...
AI selected move: a6a5 (time: 0.03s, score: -1040.0)
AI move: a6a5
Sending response: {'fen': 'r3kb1r/1pp2pp1/2npbn1p/p3P3/3P4/8/PPP2PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'a6a5', 'game_over': False, 'result': None, 'evaluation': 70}
127.0.0.1 - - [07/Jun/2025 03:51:40] "POST /make_move HTTP/1.1" 200 - 
Received make_move request
Player move: e5f6
Getting AI move...
AI evaluating 34 legal moves...
AI selected move: g7f6 (time: 0.03s, score: -925.0)
AI move: g7f6
Sending response: {'fen': 'r3kb1r/1pp2p2/2npbp1p/p7/3P4/8/PPP2PPP/RNB1Sending response: {'fen': 'r3kb1r/1pp2p2/2npbp1Sending response: {'fenSending response: {'fen': 'r3kb1r/1pp2p2/2npbp1p/p7/3P4/8/PPP2PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'g7f6', 'game_over': False, 'result': None, 'evaluation': 285}
127.0.0.1 - - [07/Jun/2025 03:51:41] "POST /make_move HTTP/1.1" 200 - 
Received make_move request
Player move: c2c4
Getting AI move...
AI evaluating 32 legal moves...
AI selected move: a8a6 (time: 0.06s, score: -830.0)
AI move: a8a6
Sending response: {'fen': '4kb1r/1pp2p2/r1npbp1p/p7/2PP4/8/PP3PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'a8a6', 'game_over': False, 'result': None, 'evaluation': 280}
127.0.0.1 - - [07/Jun/2025 04:07:52] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c4c5
Getting AI move...
AI evaluating 33 legal moves...
AI selected move: a6b6 (time: 0.04s, score: -1325.0)
AI move: a6b6
Sending response: {'fen': '4kb1r/1pp2p2/1rnpbp1p/p1P5/3P4/8/PP3PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'a6b6', 'game_over': False, 'result': None, 'evaluation': 285}
127.0.0.1 - - [07/Jun/2025 04:07:55] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c5b6
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: c6d4 (time: 0.02s, score: -1075.0)
AI move: c6d4
Sending response: {'fen': '4kb1r/1pp2p2/1P1pbp1p/p7/3n4/8/PP3PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'c6d4', 'game_over': False, 'result': None, 'evaluation': 635}
127.0.0.1 - - [07/Jun/2025 04:07:57] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: b6c7
Getting AI move...
AI evaluating 28 legal moves...
AI selected move: d6d5 (time: 0.05s, score: -875.0)
AI move: d6d5
Sending response: {'fen': '4kb1r/1pP2p2/4bp1p/p2p4/3n4/8/PP3PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'd6d5', 'game_over': False, 'result': None, 'evaluation': 735}
127.0.0.1 - - [07/Jun/2025 04:07:59] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: c7c8
Getting AI move...
AI evaluating 2 legal moves...
AI selected move: e6c8 (time: 0.01s, score: -2175.0)
AI move: e6c8
Sending response: {'fen': '2b1kb1r/1p3p2/5p1p/p2p4/3n4/8/PP3PPP/RNB1KBNR w KQkq - 0 1', 'ai_move': 'e6c8', 'game_over': False, 'result': None, 'evaluation': 625}
127.0.0.1 - - [07/Jun/2025 04:08:00] "POST /make_move HTTP/1.1" 200 - 
Received make_move request
Player move: f1a6
Getting AI move...
AI evaluating 30 legal moves...
AI selected move: c8e6 (time: 0.11s, score: -475.0)
AI move: c8e6
Sending response: {'fen': '4kb1r/1p3p2/B3bp1p/p2p4/3n4/8/PP3PPP/RNB1K1NR w KQkq - 0 1', 'ai_move': 'c8e6', 'game_over': False, 'result': None, 'evaluation': 605}
127.0.0.1 - - [07/Jun/2025 04:08:05] "POST /make_move HTTP/1.1" 200 -
Received make_move request
Player move: a6b7
Getting AI move...
AI evaluating 26 legal moves...
AI selected move: f8d6 (time: 0.06s, score: -695.0)
AI move: f8d6
Sending response: {'fen': '4k2r/1B3p2/3bbp1p/p2p4/3n4/8/PP3PPP/RNB1K1NR w KQkq - 0 1', 'ai_move': 'f8d6', 'game_over': False, 'result': None, 'evaluation': 705}
127.0.0.1 - - [07/Jun/2025 04:08:07] "POST /make_move HTTP/1.1" 200 -
Received train_ai request
Starting background training with 50 games...
Sending response: {'message': 'Training started with 50 games. Check status for progress.'}
127.0.0.1 - - [07/Jun/2025 04:08:11] "POST /train_ai HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:52] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:56] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:08:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:00] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:02] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:04] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:10] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:22] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:24] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:30] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:32] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:34] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:36] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:44] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:46] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:48] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:09:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:35] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:10:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:11:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:18] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:27] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:37] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:51] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:12:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:06] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:19] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:38] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:50] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:13:53] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:07] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:09] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:11] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:22] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:40] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:49] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:55] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:57] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:14:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:08] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:13] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:17] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:20] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:31] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:33] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:39] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:41] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:15:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:15] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:23] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:32] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:43] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:45] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:47] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:16:59] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:01] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:12] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:14] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:16] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:25] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:26] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:28] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:29] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:30] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:42] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:54] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:56] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:17:58] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:18:03] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:18:05] "GET /training_status HTTP/1.1" 200 -
127.0.0.1 - - [07/Jun/2025 04:18:07] "GET /training_status HTTP/1.1" 200 -
00 -
00 -
00 -
00 -
00 -
Background training completed!
127.0.0.1 - - [07/Jun/2025 04:18:18] "GET /training_status HTTP/1.1" 200 -
