// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/dataset_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/model.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
namespace tensorflow {
namespace data {
class AutotuneOptions;
struct AutotuneOptionsDefaultTypeInternal;
extern AutotuneOptionsDefaultTypeInternal _AutotuneOptions_default_instance_;
class CardinalityOptions;
struct CardinalityOptionsDefaultTypeInternal;
extern CardinalityOptionsDefaultTypeInternal _CardinalityOptions_default_instance_;
class DistributeOptions;
struct DistributeOptionsDefaultTypeInternal;
extern DistributeOptionsDefaultTypeInternal _DistributeOptions_default_instance_;
class OptimizationOptions;
struct OptimizationOptionsDefaultTypeInternal;
extern OptimizationOptionsDefaultTypeInternal _OptimizationOptions_default_instance_;
class Options;
struct OptionsDefaultTypeInternal;
extern OptionsDefaultTypeInternal _Options_default_instance_;
class ThreadingOptions;
struct ThreadingOptionsDefaultTypeInternal;
extern ThreadingOptionsDefaultTypeInternal _ThreadingOptions_default_instance_;
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::AutotuneOptions* Arena::CreateMaybeMessage<::tensorflow::data::AutotuneOptions>(Arena*);
template<> ::tensorflow::data::CardinalityOptions* Arena::CreateMaybeMessage<::tensorflow::data::CardinalityOptions>(Arena*);
template<> ::tensorflow::data::DistributeOptions* Arena::CreateMaybeMessage<::tensorflow::data::DistributeOptions>(Arena*);
template<> ::tensorflow::data::OptimizationOptions* Arena::CreateMaybeMessage<::tensorflow::data::OptimizationOptions>(Arena*);
template<> ::tensorflow::data::Options* Arena::CreateMaybeMessage<::tensorflow::data::Options>(Arena*);
template<> ::tensorflow::data::ThreadingOptions* Arena::CreateMaybeMessage<::tensorflow::data::ThreadingOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {

enum CardinalityOptions_ComputeLevel : int {
  CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_UNSPECIFIED = 0,
  CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_LOW = 1,
  CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_MODERATE = 2,
  CardinalityOptions_ComputeLevel_CardinalityOptions_ComputeLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CardinalityOptions_ComputeLevel_CardinalityOptions_ComputeLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CardinalityOptions_ComputeLevel_IsValid(int value);
constexpr CardinalityOptions_ComputeLevel CardinalityOptions_ComputeLevel_ComputeLevel_MIN = CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_UNSPECIFIED;
constexpr CardinalityOptions_ComputeLevel CardinalityOptions_ComputeLevel_ComputeLevel_MAX = CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_MODERATE;
constexpr int CardinalityOptions_ComputeLevel_ComputeLevel_ARRAYSIZE = CardinalityOptions_ComputeLevel_ComputeLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CardinalityOptions_ComputeLevel_descriptor();
template<typename T>
inline const std::string& CardinalityOptions_ComputeLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CardinalityOptions_ComputeLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CardinalityOptions_ComputeLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CardinalityOptions_ComputeLevel_descriptor(), enum_t_value);
}
inline bool CardinalityOptions_ComputeLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CardinalityOptions_ComputeLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CardinalityOptions_ComputeLevel>(
    CardinalityOptions_ComputeLevel_descriptor(), name, value);
}
enum AutoShardPolicy : int {
  AUTO = 0,
  FILE = 1,
  DATA = 2,
  HINT = 3,
  OFF = -1,
  AutoShardPolicy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AutoShardPolicy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AutoShardPolicy_IsValid(int value);
constexpr AutoShardPolicy AutoShardPolicy_MIN = OFF;
constexpr AutoShardPolicy AutoShardPolicy_MAX = HINT;
constexpr int AutoShardPolicy_ARRAYSIZE = AutoShardPolicy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AutoShardPolicy_descriptor();
template<typename T>
inline const std::string& AutoShardPolicy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AutoShardPolicy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AutoShardPolicy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AutoShardPolicy_descriptor(), enum_t_value);
}
inline bool AutoShardPolicy_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AutoShardPolicy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AutoShardPolicy>(
    AutoShardPolicy_descriptor(), name, value);
}
enum ExternalStatePolicy : int {
  POLICY_WARN = 0,
  POLICY_IGNORE = 1,
  POLICY_FAIL = 2,
  ExternalStatePolicy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ExternalStatePolicy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ExternalStatePolicy_IsValid(int value);
constexpr ExternalStatePolicy ExternalStatePolicy_MIN = POLICY_WARN;
constexpr ExternalStatePolicy ExternalStatePolicy_MAX = POLICY_FAIL;
constexpr int ExternalStatePolicy_ARRAYSIZE = ExternalStatePolicy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ExternalStatePolicy_descriptor();
template<typename T>
inline const std::string& ExternalStatePolicy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ExternalStatePolicy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ExternalStatePolicy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ExternalStatePolicy_descriptor(), enum_t_value);
}
inline bool ExternalStatePolicy_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ExternalStatePolicy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ExternalStatePolicy>(
    ExternalStatePolicy_descriptor(), name, value);
}
// ===================================================================

class AutotuneOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.AutotuneOptions) */ {
 public:
  inline AutotuneOptions() : AutotuneOptions(nullptr) {}
  ~AutotuneOptions() override;
  explicit PROTOBUF_CONSTEXPR AutotuneOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneOptions(const AutotuneOptions& from);
  AutotuneOptions(AutotuneOptions&& from) noexcept
    : AutotuneOptions() {
    *this = ::std::move(from);
  }

  inline AutotuneOptions& operator=(const AutotuneOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneOptions& operator=(AutotuneOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneOptions& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalEnabledCase {
    kEnabled = 1,
    OPTIONAL_ENABLED_NOT_SET = 0,
  };

  enum OptionalCpuBudgetCase {
    kCpuBudget = 2,
    OPTIONAL_CPU_BUDGET_NOT_SET = 0,
  };

  enum OptionalRamBudgetCase {
    kRamBudget = 3,
    OPTIONAL_RAM_BUDGET_NOT_SET = 0,
  };

  enum OptionalAutotuneAlgorithmCase {
    kAutotuneAlgorithm = 4,
    OPTIONAL_AUTOTUNE_ALGORITHM_NOT_SET = 0,
  };

  static inline const AutotuneOptions* internal_default_instance() {
    return reinterpret_cast<const AutotuneOptions*>(
               &_AutotuneOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AutotuneOptions& a, AutotuneOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneOptions& from) {
    AutotuneOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.AutotuneOptions";
  }
  protected:
  explicit AutotuneOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
    kCpuBudgetFieldNumber = 2,
    kRamBudgetFieldNumber = 3,
    kAutotuneAlgorithmFieldNumber = 4,
  };
  // bool enabled = 1;
  bool has_enabled() const;
  private:
  bool _internal_has_enabled() const;
  public:
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // int32 cpu_budget = 2;
  bool has_cpu_budget() const;
  private:
  bool _internal_has_cpu_budget() const;
  public:
  void clear_cpu_budget();
  int32_t cpu_budget() const;
  void set_cpu_budget(int32_t value);
  private:
  int32_t _internal_cpu_budget() const;
  void _internal_set_cpu_budget(int32_t value);
  public:

  // int64 ram_budget = 3;
  bool has_ram_budget() const;
  private:
  bool _internal_has_ram_budget() const;
  public:
  void clear_ram_budget();
  int64_t ram_budget() const;
  void set_ram_budget(int64_t value);
  private:
  int64_t _internal_ram_budget() const;
  void _internal_set_ram_budget(int64_t value);
  public:

  // .tensorflow.data.model.AutotuneAlgorithm autotune_algorithm = 4;
  bool has_autotune_algorithm() const;
  private:
  bool _internal_has_autotune_algorithm() const;
  public:
  void clear_autotune_algorithm();
  ::tensorflow::data::model::AutotuneAlgorithm autotune_algorithm() const;
  void set_autotune_algorithm(::tensorflow::data::model::AutotuneAlgorithm value);
  private:
  ::tensorflow::data::model::AutotuneAlgorithm _internal_autotune_algorithm() const;
  void _internal_set_autotune_algorithm(::tensorflow::data::model::AutotuneAlgorithm value);
  public:

  void clear_optional_enabled();
  OptionalEnabledCase optional_enabled_case() const;
  void clear_optional_cpu_budget();
  OptionalCpuBudgetCase optional_cpu_budget_case() const;
  void clear_optional_ram_budget();
  OptionalRamBudgetCase optional_ram_budget_case() const;
  void clear_optional_autotune_algorithm();
  OptionalAutotuneAlgorithmCase optional_autotune_algorithm_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.AutotuneOptions)
 private:
  class _Internal;
  void set_has_enabled();
  void set_has_cpu_budget();
  void set_has_ram_budget();
  void set_has_autotune_algorithm();

  inline bool has_optional_enabled() const;
  inline void clear_has_optional_enabled();

  inline bool has_optional_cpu_budget() const;
  inline void clear_has_optional_cpu_budget();

  inline bool has_optional_ram_budget() const;
  inline void clear_has_optional_ram_budget();

  inline bool has_optional_autotune_algorithm() const;
  inline void clear_has_optional_autotune_algorithm();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union OptionalEnabledUnion {
      constexpr OptionalEnabledUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool enabled_;
    } optional_enabled_;
    union OptionalCpuBudgetUnion {
      constexpr OptionalCpuBudgetUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int32_t cpu_budget_;
    } optional_cpu_budget_;
    union OptionalRamBudgetUnion {
      constexpr OptionalRamBudgetUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int64_t ram_budget_;
    } optional_ram_budget_;
    union OptionalAutotuneAlgorithmUnion {
      constexpr OptionalAutotuneAlgorithmUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int autotune_algorithm_;
    } optional_autotune_algorithm_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[4];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class CardinalityOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.CardinalityOptions) */ {
 public:
  inline CardinalityOptions() : CardinalityOptions(nullptr) {}
  ~CardinalityOptions() override;
  explicit PROTOBUF_CONSTEXPR CardinalityOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CardinalityOptions(const CardinalityOptions& from);
  CardinalityOptions(CardinalityOptions&& from) noexcept
    : CardinalityOptions() {
    *this = ::std::move(from);
  }

  inline CardinalityOptions& operator=(const CardinalityOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CardinalityOptions& operator=(CardinalityOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CardinalityOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const CardinalityOptions* internal_default_instance() {
    return reinterpret_cast<const CardinalityOptions*>(
               &_CardinalityOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CardinalityOptions& a, CardinalityOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CardinalityOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CardinalityOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CardinalityOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CardinalityOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CardinalityOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CardinalityOptions& from) {
    CardinalityOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CardinalityOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.CardinalityOptions";
  }
  protected:
  explicit CardinalityOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CardinalityOptions_ComputeLevel ComputeLevel;
  static constexpr ComputeLevel CARDINALITY_COMPUTE_UNSPECIFIED =
    CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_UNSPECIFIED;
  static constexpr ComputeLevel CARDINALITY_COMPUTE_LOW =
    CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_LOW;
  static constexpr ComputeLevel CARDINALITY_COMPUTE_MODERATE =
    CardinalityOptions_ComputeLevel_CARDINALITY_COMPUTE_MODERATE;
  static inline bool ComputeLevel_IsValid(int value) {
    return CardinalityOptions_ComputeLevel_IsValid(value);
  }
  static constexpr ComputeLevel ComputeLevel_MIN =
    CardinalityOptions_ComputeLevel_ComputeLevel_MIN;
  static constexpr ComputeLevel ComputeLevel_MAX =
    CardinalityOptions_ComputeLevel_ComputeLevel_MAX;
  static constexpr int ComputeLevel_ARRAYSIZE =
    CardinalityOptions_ComputeLevel_ComputeLevel_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ComputeLevel_descriptor() {
    return CardinalityOptions_ComputeLevel_descriptor();
  }
  template<typename T>
  static inline const std::string& ComputeLevel_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ComputeLevel>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ComputeLevel_Name.");
    return CardinalityOptions_ComputeLevel_Name(enum_t_value);
  }
  static inline bool ComputeLevel_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ComputeLevel* value) {
    return CardinalityOptions_ComputeLevel_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kComputeLevelFieldNumber = 1,
  };
  // .tensorflow.data.CardinalityOptions.ComputeLevel compute_level = 1;
  void clear_compute_level();
  ::tensorflow::data::CardinalityOptions_ComputeLevel compute_level() const;
  void set_compute_level(::tensorflow::data::CardinalityOptions_ComputeLevel value);
  private:
  ::tensorflow::data::CardinalityOptions_ComputeLevel _internal_compute_level() const;
  void _internal_set_compute_level(::tensorflow::data::CardinalityOptions_ComputeLevel value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.CardinalityOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int compute_level_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class DistributeOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.DistributeOptions) */ {
 public:
  inline DistributeOptions() : DistributeOptions(nullptr) {}
  ~DistributeOptions() override;
  explicit PROTOBUF_CONSTEXPR DistributeOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DistributeOptions(const DistributeOptions& from);
  DistributeOptions(DistributeOptions&& from) noexcept
    : DistributeOptions() {
    *this = ::std::move(from);
  }

  inline DistributeOptions& operator=(const DistributeOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline DistributeOptions& operator=(DistributeOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DistributeOptions& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalNumDevicesCase {
    kNumDevices = 2,
    OPTIONAL_NUM_DEVICES_NOT_SET = 0,
  };

  static inline const DistributeOptions* internal_default_instance() {
    return reinterpret_cast<const DistributeOptions*>(
               &_DistributeOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DistributeOptions& a, DistributeOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(DistributeOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DistributeOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DistributeOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DistributeOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DistributeOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DistributeOptions& from) {
    DistributeOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DistributeOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.DistributeOptions";
  }
  protected:
  explicit DistributeOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAutoShardPolicyFieldNumber = 1,
    kNumDevicesFieldNumber = 2,
  };
  // .tensorflow.data.AutoShardPolicy auto_shard_policy = 1;
  void clear_auto_shard_policy();
  ::tensorflow::data::AutoShardPolicy auto_shard_policy() const;
  void set_auto_shard_policy(::tensorflow::data::AutoShardPolicy value);
  private:
  ::tensorflow::data::AutoShardPolicy _internal_auto_shard_policy() const;
  void _internal_set_auto_shard_policy(::tensorflow::data::AutoShardPolicy value);
  public:

  // int32 num_devices = 2;
  bool has_num_devices() const;
  private:
  bool _internal_has_num_devices() const;
  public:
  void clear_num_devices();
  int32_t num_devices() const;
  void set_num_devices(int32_t value);
  private:
  int32_t _internal_num_devices() const;
  void _internal_set_num_devices(int32_t value);
  public:

  void clear_optional_num_devices();
  OptionalNumDevicesCase optional_num_devices_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.DistributeOptions)
 private:
  class _Internal;
  void set_has_num_devices();

  inline bool has_optional_num_devices() const;
  inline void clear_has_optional_num_devices();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int auto_shard_policy_;
    union OptionalNumDevicesUnion {
      constexpr OptionalNumDevicesUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int32_t num_devices_;
    } optional_num_devices_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class OptimizationOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.OptimizationOptions) */ {
 public:
  inline OptimizationOptions() : OptimizationOptions(nullptr) {}
  ~OptimizationOptions() override;
  explicit PROTOBUF_CONSTEXPR OptimizationOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OptimizationOptions(const OptimizationOptions& from);
  OptimizationOptions(OptimizationOptions&& from) noexcept
    : OptimizationOptions() {
    *this = ::std::move(from);
  }

  inline OptimizationOptions& operator=(const OptimizationOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptimizationOptions& operator=(OptimizationOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OptimizationOptions& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalApplyDefaultOptimizationsCase {
    kApplyDefaultOptimizations = 1,
    OPTIONAL_APPLY_DEFAULT_OPTIMIZATIONS_NOT_SET = 0,
  };

  enum OptionalFilterFusionCase {
    kFilterFusion = 6,
    OPTIONAL_FILTER_FUSION_NOT_SET = 0,
  };

  enum OptionalMapAndBatchFusionCase {
    kMapAndBatchFusion = 9,
    OPTIONAL_MAP_AND_BATCH_FUSION_NOT_SET = 0,
  };

  enum OptionalMapAndFilterFusionCase {
    kMapAndFilterFusion = 10,
    OPTIONAL_MAP_AND_FILTER_FUSION_NOT_SET = 0,
  };

  enum OptionalMapFusionCase {
    kMapFusion = 11,
    OPTIONAL_MAP_FUSION_NOT_SET = 0,
  };

  enum OptionalMapParallelizationCase {
    kMapParallelization = 12,
    OPTIONAL_MAP_PARALLELIZATION_NOT_SET = 0,
  };

  enum OptionalNoopEliminationCase {
    kNoopElimination = 14,
    OPTIONAL_NOOP_ELIMINATION_NOT_SET = 0,
  };

  enum OptionalParallelBatchCase {
    kParallelBatch = 15,
    OPTIONAL_PARALLEL_BATCH_NOT_SET = 0,
  };

  enum OptionalShuffleAndRepeatFusionCase {
    kShuffleAndRepeatFusion = 17,
    OPTIONAL_SHUFFLE_AND_REPEAT_FUSION_NOT_SET = 0,
  };

  enum OptionalFilterParallelizationCase {
    kFilterParallelization = 18,
    OPTIONAL_FILTER_PARALLELIZATION_NOT_SET = 0,
  };

  enum OptionalInjectPrefetchCase {
    kInjectPrefetch = 19,
    OPTIONAL_INJECT_PREFETCH_NOT_SET = 0,
  };

  enum OptionalWarmStartCase {
    kWarmStart = 20,
    OPTIONAL_WARM_START_NOT_SET = 0,
  };

  static inline const OptimizationOptions* internal_default_instance() {
    return reinterpret_cast<const OptimizationOptions*>(
               &_OptimizationOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OptimizationOptions& a, OptimizationOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(OptimizationOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OptimizationOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OptimizationOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OptimizationOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OptimizationOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OptimizationOptions& from) {
    OptimizationOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizationOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.OptimizationOptions";
  }
  protected:
  explicit OptimizationOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kApplyDefaultOptimizationsFieldNumber = 1,
    kFilterFusionFieldNumber = 6,
    kMapAndBatchFusionFieldNumber = 9,
    kMapAndFilterFusionFieldNumber = 10,
    kMapFusionFieldNumber = 11,
    kMapParallelizationFieldNumber = 12,
    kNoopEliminationFieldNumber = 14,
    kParallelBatchFieldNumber = 15,
    kShuffleAndRepeatFusionFieldNumber = 17,
    kFilterParallelizationFieldNumber = 18,
    kInjectPrefetchFieldNumber = 19,
    kWarmStartFieldNumber = 20,
  };
  // bool apply_default_optimizations = 1;
  bool has_apply_default_optimizations() const;
  private:
  bool _internal_has_apply_default_optimizations() const;
  public:
  void clear_apply_default_optimizations();
  bool apply_default_optimizations() const;
  void set_apply_default_optimizations(bool value);
  private:
  bool _internal_apply_default_optimizations() const;
  void _internal_set_apply_default_optimizations(bool value);
  public:

  // bool filter_fusion = 6;
  bool has_filter_fusion() const;
  private:
  bool _internal_has_filter_fusion() const;
  public:
  void clear_filter_fusion();
  bool filter_fusion() const;
  void set_filter_fusion(bool value);
  private:
  bool _internal_filter_fusion() const;
  void _internal_set_filter_fusion(bool value);
  public:

  // bool map_and_batch_fusion = 9;
  bool has_map_and_batch_fusion() const;
  private:
  bool _internal_has_map_and_batch_fusion() const;
  public:
  void clear_map_and_batch_fusion();
  bool map_and_batch_fusion() const;
  void set_map_and_batch_fusion(bool value);
  private:
  bool _internal_map_and_batch_fusion() const;
  void _internal_set_map_and_batch_fusion(bool value);
  public:

  // bool map_and_filter_fusion = 10;
  bool has_map_and_filter_fusion() const;
  private:
  bool _internal_has_map_and_filter_fusion() const;
  public:
  void clear_map_and_filter_fusion();
  bool map_and_filter_fusion() const;
  void set_map_and_filter_fusion(bool value);
  private:
  bool _internal_map_and_filter_fusion() const;
  void _internal_set_map_and_filter_fusion(bool value);
  public:

  // bool map_fusion = 11;
  bool has_map_fusion() const;
  private:
  bool _internal_has_map_fusion() const;
  public:
  void clear_map_fusion();
  bool map_fusion() const;
  void set_map_fusion(bool value);
  private:
  bool _internal_map_fusion() const;
  void _internal_set_map_fusion(bool value);
  public:

  // bool map_parallelization = 12;
  bool has_map_parallelization() const;
  private:
  bool _internal_has_map_parallelization() const;
  public:
  void clear_map_parallelization();
  bool map_parallelization() const;
  void set_map_parallelization(bool value);
  private:
  bool _internal_map_parallelization() const;
  void _internal_set_map_parallelization(bool value);
  public:

  // bool noop_elimination = 14;
  bool has_noop_elimination() const;
  private:
  bool _internal_has_noop_elimination() const;
  public:
  void clear_noop_elimination();
  bool noop_elimination() const;
  void set_noop_elimination(bool value);
  private:
  bool _internal_noop_elimination() const;
  void _internal_set_noop_elimination(bool value);
  public:

  // bool parallel_batch = 15;
  bool has_parallel_batch() const;
  private:
  bool _internal_has_parallel_batch() const;
  public:
  void clear_parallel_batch();
  bool parallel_batch() const;
  void set_parallel_batch(bool value);
  private:
  bool _internal_parallel_batch() const;
  void _internal_set_parallel_batch(bool value);
  public:

  // bool shuffle_and_repeat_fusion = 17;
  bool has_shuffle_and_repeat_fusion() const;
  private:
  bool _internal_has_shuffle_and_repeat_fusion() const;
  public:
  void clear_shuffle_and_repeat_fusion();
  bool shuffle_and_repeat_fusion() const;
  void set_shuffle_and_repeat_fusion(bool value);
  private:
  bool _internal_shuffle_and_repeat_fusion() const;
  void _internal_set_shuffle_and_repeat_fusion(bool value);
  public:

  // bool filter_parallelization = 18;
  bool has_filter_parallelization() const;
  private:
  bool _internal_has_filter_parallelization() const;
  public:
  void clear_filter_parallelization();
  bool filter_parallelization() const;
  void set_filter_parallelization(bool value);
  private:
  bool _internal_filter_parallelization() const;
  void _internal_set_filter_parallelization(bool value);
  public:

  // bool inject_prefetch = 19;
  bool has_inject_prefetch() const;
  private:
  bool _internal_has_inject_prefetch() const;
  public:
  void clear_inject_prefetch();
  bool inject_prefetch() const;
  void set_inject_prefetch(bool value);
  private:
  bool _internal_inject_prefetch() const;
  void _internal_set_inject_prefetch(bool value);
  public:

  // bool warm_start = 20;
  bool has_warm_start() const;
  private:
  bool _internal_has_warm_start() const;
  public:
  void clear_warm_start();
  bool warm_start() const;
  void set_warm_start(bool value);
  private:
  bool _internal_warm_start() const;
  void _internal_set_warm_start(bool value);
  public:

  void clear_optional_apply_default_optimizations();
  OptionalApplyDefaultOptimizationsCase optional_apply_default_optimizations_case() const;
  void clear_optional_filter_fusion();
  OptionalFilterFusionCase optional_filter_fusion_case() const;
  void clear_optional_map_and_batch_fusion();
  OptionalMapAndBatchFusionCase optional_map_and_batch_fusion_case() const;
  void clear_optional_map_and_filter_fusion();
  OptionalMapAndFilterFusionCase optional_map_and_filter_fusion_case() const;
  void clear_optional_map_fusion();
  OptionalMapFusionCase optional_map_fusion_case() const;
  void clear_optional_map_parallelization();
  OptionalMapParallelizationCase optional_map_parallelization_case() const;
  void clear_optional_noop_elimination();
  OptionalNoopEliminationCase optional_noop_elimination_case() const;
  void clear_optional_parallel_batch();
  OptionalParallelBatchCase optional_parallel_batch_case() const;
  void clear_optional_shuffle_and_repeat_fusion();
  OptionalShuffleAndRepeatFusionCase optional_shuffle_and_repeat_fusion_case() const;
  void clear_optional_filter_parallelization();
  OptionalFilterParallelizationCase optional_filter_parallelization_case() const;
  void clear_optional_inject_prefetch();
  OptionalInjectPrefetchCase optional_inject_prefetch_case() const;
  void clear_optional_warm_start();
  OptionalWarmStartCase optional_warm_start_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.OptimizationOptions)
 private:
  class _Internal;
  void set_has_apply_default_optimizations();
  void set_has_filter_fusion();
  void set_has_map_and_batch_fusion();
  void set_has_map_and_filter_fusion();
  void set_has_map_fusion();
  void set_has_map_parallelization();
  void set_has_noop_elimination();
  void set_has_parallel_batch();
  void set_has_shuffle_and_repeat_fusion();
  void set_has_filter_parallelization();
  void set_has_inject_prefetch();
  void set_has_warm_start();

  inline bool has_optional_apply_default_optimizations() const;
  inline void clear_has_optional_apply_default_optimizations();

  inline bool has_optional_filter_fusion() const;
  inline void clear_has_optional_filter_fusion();

  inline bool has_optional_map_and_batch_fusion() const;
  inline void clear_has_optional_map_and_batch_fusion();

  inline bool has_optional_map_and_filter_fusion() const;
  inline void clear_has_optional_map_and_filter_fusion();

  inline bool has_optional_map_fusion() const;
  inline void clear_has_optional_map_fusion();

  inline bool has_optional_map_parallelization() const;
  inline void clear_has_optional_map_parallelization();

  inline bool has_optional_noop_elimination() const;
  inline void clear_has_optional_noop_elimination();

  inline bool has_optional_parallel_batch() const;
  inline void clear_has_optional_parallel_batch();

  inline bool has_optional_shuffle_and_repeat_fusion() const;
  inline void clear_has_optional_shuffle_and_repeat_fusion();

  inline bool has_optional_filter_parallelization() const;
  inline void clear_has_optional_filter_parallelization();

  inline bool has_optional_inject_prefetch() const;
  inline void clear_has_optional_inject_prefetch();

  inline bool has_optional_warm_start() const;
  inline void clear_has_optional_warm_start();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union OptionalApplyDefaultOptimizationsUnion {
      constexpr OptionalApplyDefaultOptimizationsUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool apply_default_optimizations_;
    } optional_apply_default_optimizations_;
    union OptionalFilterFusionUnion {
      constexpr OptionalFilterFusionUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool filter_fusion_;
    } optional_filter_fusion_;
    union OptionalMapAndBatchFusionUnion {
      constexpr OptionalMapAndBatchFusionUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool map_and_batch_fusion_;
    } optional_map_and_batch_fusion_;
    union OptionalMapAndFilterFusionUnion {
      constexpr OptionalMapAndFilterFusionUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool map_and_filter_fusion_;
    } optional_map_and_filter_fusion_;
    union OptionalMapFusionUnion {
      constexpr OptionalMapFusionUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool map_fusion_;
    } optional_map_fusion_;
    union OptionalMapParallelizationUnion {
      constexpr OptionalMapParallelizationUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool map_parallelization_;
    } optional_map_parallelization_;
    union OptionalNoopEliminationUnion {
      constexpr OptionalNoopEliminationUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool noop_elimination_;
    } optional_noop_elimination_;
    union OptionalParallelBatchUnion {
      constexpr OptionalParallelBatchUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool parallel_batch_;
    } optional_parallel_batch_;
    union OptionalShuffleAndRepeatFusionUnion {
      constexpr OptionalShuffleAndRepeatFusionUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool shuffle_and_repeat_fusion_;
    } optional_shuffle_and_repeat_fusion_;
    union OptionalFilterParallelizationUnion {
      constexpr OptionalFilterParallelizationUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool filter_parallelization_;
    } optional_filter_parallelization_;
    union OptionalInjectPrefetchUnion {
      constexpr OptionalInjectPrefetchUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool inject_prefetch_;
    } optional_inject_prefetch_;
    union OptionalWarmStartUnion {
      constexpr OptionalWarmStartUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool warm_start_;
    } optional_warm_start_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[12];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class ThreadingOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.ThreadingOptions) */ {
 public:
  inline ThreadingOptions() : ThreadingOptions(nullptr) {}
  ~ThreadingOptions() override;
  explicit PROTOBUF_CONSTEXPR ThreadingOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ThreadingOptions(const ThreadingOptions& from);
  ThreadingOptions(ThreadingOptions&& from) noexcept
    : ThreadingOptions() {
    *this = ::std::move(from);
  }

  inline ThreadingOptions& operator=(const ThreadingOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThreadingOptions& operator=(ThreadingOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ThreadingOptions& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalMaxIntraOpParallelismCase {
    kMaxIntraOpParallelism = 1,
    OPTIONAL_MAX_INTRA_OP_PARALLELISM_NOT_SET = 0,
  };

  enum OptionalPrivateThreadpoolSizeCase {
    kPrivateThreadpoolSize = 2,
    OPTIONAL_PRIVATE_THREADPOOL_SIZE_NOT_SET = 0,
  };

  static inline const ThreadingOptions* internal_default_instance() {
    return reinterpret_cast<const ThreadingOptions*>(
               &_ThreadingOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ThreadingOptions& a, ThreadingOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ThreadingOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ThreadingOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ThreadingOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ThreadingOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ThreadingOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ThreadingOptions& from) {
    ThreadingOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThreadingOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.ThreadingOptions";
  }
  protected:
  explicit ThreadingOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMaxIntraOpParallelismFieldNumber = 1,
    kPrivateThreadpoolSizeFieldNumber = 2,
  };
  // int32 max_intra_op_parallelism = 1;
  bool has_max_intra_op_parallelism() const;
  private:
  bool _internal_has_max_intra_op_parallelism() const;
  public:
  void clear_max_intra_op_parallelism();
  int32_t max_intra_op_parallelism() const;
  void set_max_intra_op_parallelism(int32_t value);
  private:
  int32_t _internal_max_intra_op_parallelism() const;
  void _internal_set_max_intra_op_parallelism(int32_t value);
  public:

  // int32 private_threadpool_size = 2;
  bool has_private_threadpool_size() const;
  private:
  bool _internal_has_private_threadpool_size() const;
  public:
  void clear_private_threadpool_size();
  int32_t private_threadpool_size() const;
  void set_private_threadpool_size(int32_t value);
  private:
  int32_t _internal_private_threadpool_size() const;
  void _internal_set_private_threadpool_size(int32_t value);
  public:

  void clear_optional_max_intra_op_parallelism();
  OptionalMaxIntraOpParallelismCase optional_max_intra_op_parallelism_case() const;
  void clear_optional_private_threadpool_size();
  OptionalPrivateThreadpoolSizeCase optional_private_threadpool_size_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.ThreadingOptions)
 private:
  class _Internal;
  void set_has_max_intra_op_parallelism();
  void set_has_private_threadpool_size();

  inline bool has_optional_max_intra_op_parallelism() const;
  inline void clear_has_optional_max_intra_op_parallelism();

  inline bool has_optional_private_threadpool_size() const;
  inline void clear_has_optional_private_threadpool_size();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union OptionalMaxIntraOpParallelismUnion {
      constexpr OptionalMaxIntraOpParallelismUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int32_t max_intra_op_parallelism_;
    } optional_max_intra_op_parallelism_;
    union OptionalPrivateThreadpoolSizeUnion {
      constexpr OptionalPrivateThreadpoolSizeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int32_t private_threadpool_size_;
    } optional_private_threadpool_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[2];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class Options final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.Options) */ {
 public:
  inline Options() : Options(nullptr) {}
  ~Options() override;
  explicit PROTOBUF_CONSTEXPR Options(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Options(const Options& from);
  Options(Options&& from) noexcept
    : Options() {
    *this = ::std::move(from);
  }

  inline Options& operator=(const Options& from) {
    CopyFrom(from);
    return *this;
  }
  inline Options& operator=(Options&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Options& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalDeterministicCase {
    kDeterministic = 1,
    OPTIONAL_DETERMINISTIC_NOT_SET = 0,
  };

  enum OptionalSlackCase {
    kSlack = 4,
    OPTIONAL_SLACK_NOT_SET = 0,
  };

  enum OptionalExternalStatePolicyCase {
    kExternalStatePolicy = 6,
    OPTIONAL_EXTERNAL_STATE_POLICY_NOT_SET = 0,
  };

  enum OptionalSymbolicCheckpointCase {
    kSymbolicCheckpoint = 8,
    OPTIONAL_SYMBOLIC_CHECKPOINT_NOT_SET = 0,
  };

  static inline const Options* internal_default_instance() {
    return reinterpret_cast<const Options*>(
               &_Options_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Options& a, Options& b) {
    a.Swap(&b);
  }
  inline void Swap(Options* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Options* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Options* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Options>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Options& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Options& from) {
    Options::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Options* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.Options";
  }
  protected:
  explicit Options(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDistributeOptionsFieldNumber = 2,
    kOptimizationOptionsFieldNumber = 3,
    kThreadingOptionsFieldNumber = 5,
    kAutotuneOptionsFieldNumber = 7,
    kDeterministicFieldNumber = 1,
    kSlackFieldNumber = 4,
    kExternalStatePolicyFieldNumber = 6,
    kSymbolicCheckpointFieldNumber = 8,
  };
  // .tensorflow.data.DistributeOptions distribute_options = 2;
  bool has_distribute_options() const;
  private:
  bool _internal_has_distribute_options() const;
  public:
  void clear_distribute_options();
  const ::tensorflow::data::DistributeOptions& distribute_options() const;
  PROTOBUF_NODISCARD ::tensorflow::data::DistributeOptions* release_distribute_options();
  ::tensorflow::data::DistributeOptions* mutable_distribute_options();
  void set_allocated_distribute_options(::tensorflow::data::DistributeOptions* distribute_options);
  private:
  const ::tensorflow::data::DistributeOptions& _internal_distribute_options() const;
  ::tensorflow::data::DistributeOptions* _internal_mutable_distribute_options();
  public:
  void unsafe_arena_set_allocated_distribute_options(
      ::tensorflow::data::DistributeOptions* distribute_options);
  ::tensorflow::data::DistributeOptions* unsafe_arena_release_distribute_options();

  // .tensorflow.data.OptimizationOptions optimization_options = 3;
  bool has_optimization_options() const;
  private:
  bool _internal_has_optimization_options() const;
  public:
  void clear_optimization_options();
  const ::tensorflow::data::OptimizationOptions& optimization_options() const;
  PROTOBUF_NODISCARD ::tensorflow::data::OptimizationOptions* release_optimization_options();
  ::tensorflow::data::OptimizationOptions* mutable_optimization_options();
  void set_allocated_optimization_options(::tensorflow::data::OptimizationOptions* optimization_options);
  private:
  const ::tensorflow::data::OptimizationOptions& _internal_optimization_options() const;
  ::tensorflow::data::OptimizationOptions* _internal_mutable_optimization_options();
  public:
  void unsafe_arena_set_allocated_optimization_options(
      ::tensorflow::data::OptimizationOptions* optimization_options);
  ::tensorflow::data::OptimizationOptions* unsafe_arena_release_optimization_options();

  // .tensorflow.data.ThreadingOptions threading_options = 5;
  bool has_threading_options() const;
  private:
  bool _internal_has_threading_options() const;
  public:
  void clear_threading_options();
  const ::tensorflow::data::ThreadingOptions& threading_options() const;
  PROTOBUF_NODISCARD ::tensorflow::data::ThreadingOptions* release_threading_options();
  ::tensorflow::data::ThreadingOptions* mutable_threading_options();
  void set_allocated_threading_options(::tensorflow::data::ThreadingOptions* threading_options);
  private:
  const ::tensorflow::data::ThreadingOptions& _internal_threading_options() const;
  ::tensorflow::data::ThreadingOptions* _internal_mutable_threading_options();
  public:
  void unsafe_arena_set_allocated_threading_options(
      ::tensorflow::data::ThreadingOptions* threading_options);
  ::tensorflow::data::ThreadingOptions* unsafe_arena_release_threading_options();

  // .tensorflow.data.AutotuneOptions autotune_options = 7;
  bool has_autotune_options() const;
  private:
  bool _internal_has_autotune_options() const;
  public:
  void clear_autotune_options();
  const ::tensorflow::data::AutotuneOptions& autotune_options() const;
  PROTOBUF_NODISCARD ::tensorflow::data::AutotuneOptions* release_autotune_options();
  ::tensorflow::data::AutotuneOptions* mutable_autotune_options();
  void set_allocated_autotune_options(::tensorflow::data::AutotuneOptions* autotune_options);
  private:
  const ::tensorflow::data::AutotuneOptions& _internal_autotune_options() const;
  ::tensorflow::data::AutotuneOptions* _internal_mutable_autotune_options();
  public:
  void unsafe_arena_set_allocated_autotune_options(
      ::tensorflow::data::AutotuneOptions* autotune_options);
  ::tensorflow::data::AutotuneOptions* unsafe_arena_release_autotune_options();

  // bool deterministic = 1;
  bool has_deterministic() const;
  private:
  bool _internal_has_deterministic() const;
  public:
  void clear_deterministic();
  bool deterministic() const;
  void set_deterministic(bool value);
  private:
  bool _internal_deterministic() const;
  void _internal_set_deterministic(bool value);
  public:

  // bool slack = 4;
  bool has_slack() const;
  private:
  bool _internal_has_slack() const;
  public:
  void clear_slack();
  bool slack() const;
  void set_slack(bool value);
  private:
  bool _internal_slack() const;
  void _internal_set_slack(bool value);
  public:

  // .tensorflow.data.ExternalStatePolicy external_state_policy = 6;
  bool has_external_state_policy() const;
  private:
  bool _internal_has_external_state_policy() const;
  public:
  void clear_external_state_policy();
  ::tensorflow::data::ExternalStatePolicy external_state_policy() const;
  void set_external_state_policy(::tensorflow::data::ExternalStatePolicy value);
  private:
  ::tensorflow::data::ExternalStatePolicy _internal_external_state_policy() const;
  void _internal_set_external_state_policy(::tensorflow::data::ExternalStatePolicy value);
  public:

  // bool symbolic_checkpoint = 8;
  bool has_symbolic_checkpoint() const;
  private:
  bool _internal_has_symbolic_checkpoint() const;
  public:
  void clear_symbolic_checkpoint();
  bool symbolic_checkpoint() const;
  void set_symbolic_checkpoint(bool value);
  private:
  bool _internal_symbolic_checkpoint() const;
  void _internal_set_symbolic_checkpoint(bool value);
  public:

  void clear_optional_deterministic();
  OptionalDeterministicCase optional_deterministic_case() const;
  void clear_optional_slack();
  OptionalSlackCase optional_slack_case() const;
  void clear_optional_external_state_policy();
  OptionalExternalStatePolicyCase optional_external_state_policy_case() const;
  void clear_optional_symbolic_checkpoint();
  OptionalSymbolicCheckpointCase optional_symbolic_checkpoint_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.Options)
 private:
  class _Internal;
  void set_has_deterministic();
  void set_has_slack();
  void set_has_external_state_policy();
  void set_has_symbolic_checkpoint();

  inline bool has_optional_deterministic() const;
  inline void clear_has_optional_deterministic();

  inline bool has_optional_slack() const;
  inline void clear_has_optional_slack();

  inline bool has_optional_external_state_policy() const;
  inline void clear_has_optional_external_state_policy();

  inline bool has_optional_symbolic_checkpoint() const;
  inline void clear_has_optional_symbolic_checkpoint();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::data::DistributeOptions* distribute_options_;
    ::tensorflow::data::OptimizationOptions* optimization_options_;
    ::tensorflow::data::ThreadingOptions* threading_options_;
    ::tensorflow::data::AutotuneOptions* autotune_options_;
    union OptionalDeterministicUnion {
      constexpr OptionalDeterministicUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool deterministic_;
    } optional_deterministic_;
    union OptionalSlackUnion {
      constexpr OptionalSlackUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool slack_;
    } optional_slack_;
    union OptionalExternalStatePolicyUnion {
      constexpr OptionalExternalStatePolicyUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int external_state_policy_;
    } optional_external_state_policy_;
    union OptionalSymbolicCheckpointUnion {
      constexpr OptionalSymbolicCheckpointUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool symbolic_checkpoint_;
    } optional_symbolic_checkpoint_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[4];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AutotuneOptions

// bool enabled = 1;
inline bool AutotuneOptions::_internal_has_enabled() const {
  return optional_enabled_case() == kEnabled;
}
inline bool AutotuneOptions::has_enabled() const {
  return _internal_has_enabled();
}
inline void AutotuneOptions::set_has_enabled() {
  _impl_._oneof_case_[0] = kEnabled;
}
inline void AutotuneOptions::clear_enabled() {
  if (_internal_has_enabled()) {
    _impl_.optional_enabled_.enabled_ = false;
    clear_has_optional_enabled();
  }
}
inline bool AutotuneOptions::_internal_enabled() const {
  if (_internal_has_enabled()) {
    return _impl_.optional_enabled_.enabled_;
  }
  return false;
}
inline void AutotuneOptions::_internal_set_enabled(bool value) {
  if (!_internal_has_enabled()) {
    clear_optional_enabled();
    set_has_enabled();
  }
  _impl_.optional_enabled_.enabled_ = value;
}
inline bool AutotuneOptions::enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.AutotuneOptions.enabled)
  return _internal_enabled();
}
inline void AutotuneOptions::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.AutotuneOptions.enabled)
}

// int32 cpu_budget = 2;
inline bool AutotuneOptions::_internal_has_cpu_budget() const {
  return optional_cpu_budget_case() == kCpuBudget;
}
inline bool AutotuneOptions::has_cpu_budget() const {
  return _internal_has_cpu_budget();
}
inline void AutotuneOptions::set_has_cpu_budget() {
  _impl_._oneof_case_[1] = kCpuBudget;
}
inline void AutotuneOptions::clear_cpu_budget() {
  if (_internal_has_cpu_budget()) {
    _impl_.optional_cpu_budget_.cpu_budget_ = 0;
    clear_has_optional_cpu_budget();
  }
}
inline int32_t AutotuneOptions::_internal_cpu_budget() const {
  if (_internal_has_cpu_budget()) {
    return _impl_.optional_cpu_budget_.cpu_budget_;
  }
  return 0;
}
inline void AutotuneOptions::_internal_set_cpu_budget(int32_t value) {
  if (!_internal_has_cpu_budget()) {
    clear_optional_cpu_budget();
    set_has_cpu_budget();
  }
  _impl_.optional_cpu_budget_.cpu_budget_ = value;
}
inline int32_t AutotuneOptions::cpu_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.AutotuneOptions.cpu_budget)
  return _internal_cpu_budget();
}
inline void AutotuneOptions::set_cpu_budget(int32_t value) {
  _internal_set_cpu_budget(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.AutotuneOptions.cpu_budget)
}

// int64 ram_budget = 3;
inline bool AutotuneOptions::_internal_has_ram_budget() const {
  return optional_ram_budget_case() == kRamBudget;
}
inline bool AutotuneOptions::has_ram_budget() const {
  return _internal_has_ram_budget();
}
inline void AutotuneOptions::set_has_ram_budget() {
  _impl_._oneof_case_[2] = kRamBudget;
}
inline void AutotuneOptions::clear_ram_budget() {
  if (_internal_has_ram_budget()) {
    _impl_.optional_ram_budget_.ram_budget_ = int64_t{0};
    clear_has_optional_ram_budget();
  }
}
inline int64_t AutotuneOptions::_internal_ram_budget() const {
  if (_internal_has_ram_budget()) {
    return _impl_.optional_ram_budget_.ram_budget_;
  }
  return int64_t{0};
}
inline void AutotuneOptions::_internal_set_ram_budget(int64_t value) {
  if (!_internal_has_ram_budget()) {
    clear_optional_ram_budget();
    set_has_ram_budget();
  }
  _impl_.optional_ram_budget_.ram_budget_ = value;
}
inline int64_t AutotuneOptions::ram_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.AutotuneOptions.ram_budget)
  return _internal_ram_budget();
}
inline void AutotuneOptions::set_ram_budget(int64_t value) {
  _internal_set_ram_budget(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.AutotuneOptions.ram_budget)
}

// .tensorflow.data.model.AutotuneAlgorithm autotune_algorithm = 4;
inline bool AutotuneOptions::_internal_has_autotune_algorithm() const {
  return optional_autotune_algorithm_case() == kAutotuneAlgorithm;
}
inline bool AutotuneOptions::has_autotune_algorithm() const {
  return _internal_has_autotune_algorithm();
}
inline void AutotuneOptions::set_has_autotune_algorithm() {
  _impl_._oneof_case_[3] = kAutotuneAlgorithm;
}
inline void AutotuneOptions::clear_autotune_algorithm() {
  if (_internal_has_autotune_algorithm()) {
    _impl_.optional_autotune_algorithm_.autotune_algorithm_ = 0;
    clear_has_optional_autotune_algorithm();
  }
}
inline ::tensorflow::data::model::AutotuneAlgorithm AutotuneOptions::_internal_autotune_algorithm() const {
  if (_internal_has_autotune_algorithm()) {
    return static_cast< ::tensorflow::data::model::AutotuneAlgorithm >(_impl_.optional_autotune_algorithm_.autotune_algorithm_);
  }
  return static_cast< ::tensorflow::data::model::AutotuneAlgorithm >(0);
}
inline ::tensorflow::data::model::AutotuneAlgorithm AutotuneOptions::autotune_algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.AutotuneOptions.autotune_algorithm)
  return _internal_autotune_algorithm();
}
inline void AutotuneOptions::_internal_set_autotune_algorithm(::tensorflow::data::model::AutotuneAlgorithm value) {
  if (!_internal_has_autotune_algorithm()) {
    clear_optional_autotune_algorithm();
    set_has_autotune_algorithm();
  }
  _impl_.optional_autotune_algorithm_.autotune_algorithm_ = value;
}
inline void AutotuneOptions::set_autotune_algorithm(::tensorflow::data::model::AutotuneAlgorithm value) {
  _internal_set_autotune_algorithm(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.AutotuneOptions.autotune_algorithm)
}

inline bool AutotuneOptions::has_optional_enabled() const {
  return optional_enabled_case() != OPTIONAL_ENABLED_NOT_SET;
}
inline void AutotuneOptions::clear_has_optional_enabled() {
  _impl_._oneof_case_[0] = OPTIONAL_ENABLED_NOT_SET;
}
inline bool AutotuneOptions::has_optional_cpu_budget() const {
  return optional_cpu_budget_case() != OPTIONAL_CPU_BUDGET_NOT_SET;
}
inline void AutotuneOptions::clear_has_optional_cpu_budget() {
  _impl_._oneof_case_[1] = OPTIONAL_CPU_BUDGET_NOT_SET;
}
inline bool AutotuneOptions::has_optional_ram_budget() const {
  return optional_ram_budget_case() != OPTIONAL_RAM_BUDGET_NOT_SET;
}
inline void AutotuneOptions::clear_has_optional_ram_budget() {
  _impl_._oneof_case_[2] = OPTIONAL_RAM_BUDGET_NOT_SET;
}
inline bool AutotuneOptions::has_optional_autotune_algorithm() const {
  return optional_autotune_algorithm_case() != OPTIONAL_AUTOTUNE_ALGORITHM_NOT_SET;
}
inline void AutotuneOptions::clear_has_optional_autotune_algorithm() {
  _impl_._oneof_case_[3] = OPTIONAL_AUTOTUNE_ALGORITHM_NOT_SET;
}
inline AutotuneOptions::OptionalEnabledCase AutotuneOptions::optional_enabled_case() const {
  return AutotuneOptions::OptionalEnabledCase(_impl_._oneof_case_[0]);
}
inline AutotuneOptions::OptionalCpuBudgetCase AutotuneOptions::optional_cpu_budget_case() const {
  return AutotuneOptions::OptionalCpuBudgetCase(_impl_._oneof_case_[1]);
}
inline AutotuneOptions::OptionalRamBudgetCase AutotuneOptions::optional_ram_budget_case() const {
  return AutotuneOptions::OptionalRamBudgetCase(_impl_._oneof_case_[2]);
}
inline AutotuneOptions::OptionalAutotuneAlgorithmCase AutotuneOptions::optional_autotune_algorithm_case() const {
  return AutotuneOptions::OptionalAutotuneAlgorithmCase(_impl_._oneof_case_[3]);
}
// -------------------------------------------------------------------

// CardinalityOptions

// .tensorflow.data.CardinalityOptions.ComputeLevel compute_level = 1;
inline void CardinalityOptions::clear_compute_level() {
  _impl_.compute_level_ = 0;
}
inline ::tensorflow::data::CardinalityOptions_ComputeLevel CardinalityOptions::_internal_compute_level() const {
  return static_cast< ::tensorflow::data::CardinalityOptions_ComputeLevel >(_impl_.compute_level_);
}
inline ::tensorflow::data::CardinalityOptions_ComputeLevel CardinalityOptions::compute_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CardinalityOptions.compute_level)
  return _internal_compute_level();
}
inline void CardinalityOptions::_internal_set_compute_level(::tensorflow::data::CardinalityOptions_ComputeLevel value) {
  
  _impl_.compute_level_ = value;
}
inline void CardinalityOptions::set_compute_level(::tensorflow::data::CardinalityOptions_ComputeLevel value) {
  _internal_set_compute_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.CardinalityOptions.compute_level)
}

// -------------------------------------------------------------------

// DistributeOptions

// .tensorflow.data.AutoShardPolicy auto_shard_policy = 1;
inline void DistributeOptions::clear_auto_shard_policy() {
  _impl_.auto_shard_policy_ = 0;
}
inline ::tensorflow::data::AutoShardPolicy DistributeOptions::_internal_auto_shard_policy() const {
  return static_cast< ::tensorflow::data::AutoShardPolicy >(_impl_.auto_shard_policy_);
}
inline ::tensorflow::data::AutoShardPolicy DistributeOptions::auto_shard_policy() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DistributeOptions.auto_shard_policy)
  return _internal_auto_shard_policy();
}
inline void DistributeOptions::_internal_set_auto_shard_policy(::tensorflow::data::AutoShardPolicy value) {
  
  _impl_.auto_shard_policy_ = value;
}
inline void DistributeOptions::set_auto_shard_policy(::tensorflow::data::AutoShardPolicy value) {
  _internal_set_auto_shard_policy(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.DistributeOptions.auto_shard_policy)
}

// int32 num_devices = 2;
inline bool DistributeOptions::_internal_has_num_devices() const {
  return optional_num_devices_case() == kNumDevices;
}
inline bool DistributeOptions::has_num_devices() const {
  return _internal_has_num_devices();
}
inline void DistributeOptions::set_has_num_devices() {
  _impl_._oneof_case_[0] = kNumDevices;
}
inline void DistributeOptions::clear_num_devices() {
  if (_internal_has_num_devices()) {
    _impl_.optional_num_devices_.num_devices_ = 0;
    clear_has_optional_num_devices();
  }
}
inline int32_t DistributeOptions::_internal_num_devices() const {
  if (_internal_has_num_devices()) {
    return _impl_.optional_num_devices_.num_devices_;
  }
  return 0;
}
inline void DistributeOptions::_internal_set_num_devices(int32_t value) {
  if (!_internal_has_num_devices()) {
    clear_optional_num_devices();
    set_has_num_devices();
  }
  _impl_.optional_num_devices_.num_devices_ = value;
}
inline int32_t DistributeOptions::num_devices() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DistributeOptions.num_devices)
  return _internal_num_devices();
}
inline void DistributeOptions::set_num_devices(int32_t value) {
  _internal_set_num_devices(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.DistributeOptions.num_devices)
}

inline bool DistributeOptions::has_optional_num_devices() const {
  return optional_num_devices_case() != OPTIONAL_NUM_DEVICES_NOT_SET;
}
inline void DistributeOptions::clear_has_optional_num_devices() {
  _impl_._oneof_case_[0] = OPTIONAL_NUM_DEVICES_NOT_SET;
}
inline DistributeOptions::OptionalNumDevicesCase DistributeOptions::optional_num_devices_case() const {
  return DistributeOptions::OptionalNumDevicesCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// OptimizationOptions

// bool apply_default_optimizations = 1;
inline bool OptimizationOptions::_internal_has_apply_default_optimizations() const {
  return optional_apply_default_optimizations_case() == kApplyDefaultOptimizations;
}
inline bool OptimizationOptions::has_apply_default_optimizations() const {
  return _internal_has_apply_default_optimizations();
}
inline void OptimizationOptions::set_has_apply_default_optimizations() {
  _impl_._oneof_case_[0] = kApplyDefaultOptimizations;
}
inline void OptimizationOptions::clear_apply_default_optimizations() {
  if (_internal_has_apply_default_optimizations()) {
    _impl_.optional_apply_default_optimizations_.apply_default_optimizations_ = false;
    clear_has_optional_apply_default_optimizations();
  }
}
inline bool OptimizationOptions::_internal_apply_default_optimizations() const {
  if (_internal_has_apply_default_optimizations()) {
    return _impl_.optional_apply_default_optimizations_.apply_default_optimizations_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_apply_default_optimizations(bool value) {
  if (!_internal_has_apply_default_optimizations()) {
    clear_optional_apply_default_optimizations();
    set_has_apply_default_optimizations();
  }
  _impl_.optional_apply_default_optimizations_.apply_default_optimizations_ = value;
}
inline bool OptimizationOptions::apply_default_optimizations() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.apply_default_optimizations)
  return _internal_apply_default_optimizations();
}
inline void OptimizationOptions::set_apply_default_optimizations(bool value) {
  _internal_set_apply_default_optimizations(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.apply_default_optimizations)
}

// bool filter_fusion = 6;
inline bool OptimizationOptions::_internal_has_filter_fusion() const {
  return optional_filter_fusion_case() == kFilterFusion;
}
inline bool OptimizationOptions::has_filter_fusion() const {
  return _internal_has_filter_fusion();
}
inline void OptimizationOptions::set_has_filter_fusion() {
  _impl_._oneof_case_[1] = kFilterFusion;
}
inline void OptimizationOptions::clear_filter_fusion() {
  if (_internal_has_filter_fusion()) {
    _impl_.optional_filter_fusion_.filter_fusion_ = false;
    clear_has_optional_filter_fusion();
  }
}
inline bool OptimizationOptions::_internal_filter_fusion() const {
  if (_internal_has_filter_fusion()) {
    return _impl_.optional_filter_fusion_.filter_fusion_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_filter_fusion(bool value) {
  if (!_internal_has_filter_fusion()) {
    clear_optional_filter_fusion();
    set_has_filter_fusion();
  }
  _impl_.optional_filter_fusion_.filter_fusion_ = value;
}
inline bool OptimizationOptions::filter_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.filter_fusion)
  return _internal_filter_fusion();
}
inline void OptimizationOptions::set_filter_fusion(bool value) {
  _internal_set_filter_fusion(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.filter_fusion)
}

// bool map_and_batch_fusion = 9;
inline bool OptimizationOptions::_internal_has_map_and_batch_fusion() const {
  return optional_map_and_batch_fusion_case() == kMapAndBatchFusion;
}
inline bool OptimizationOptions::has_map_and_batch_fusion() const {
  return _internal_has_map_and_batch_fusion();
}
inline void OptimizationOptions::set_has_map_and_batch_fusion() {
  _impl_._oneof_case_[2] = kMapAndBatchFusion;
}
inline void OptimizationOptions::clear_map_and_batch_fusion() {
  if (_internal_has_map_and_batch_fusion()) {
    _impl_.optional_map_and_batch_fusion_.map_and_batch_fusion_ = false;
    clear_has_optional_map_and_batch_fusion();
  }
}
inline bool OptimizationOptions::_internal_map_and_batch_fusion() const {
  if (_internal_has_map_and_batch_fusion()) {
    return _impl_.optional_map_and_batch_fusion_.map_and_batch_fusion_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_map_and_batch_fusion(bool value) {
  if (!_internal_has_map_and_batch_fusion()) {
    clear_optional_map_and_batch_fusion();
    set_has_map_and_batch_fusion();
  }
  _impl_.optional_map_and_batch_fusion_.map_and_batch_fusion_ = value;
}
inline bool OptimizationOptions::map_and_batch_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_and_batch_fusion)
  return _internal_map_and_batch_fusion();
}
inline void OptimizationOptions::set_map_and_batch_fusion(bool value) {
  _internal_set_map_and_batch_fusion(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_and_batch_fusion)
}

// bool map_and_filter_fusion = 10;
inline bool OptimizationOptions::_internal_has_map_and_filter_fusion() const {
  return optional_map_and_filter_fusion_case() == kMapAndFilterFusion;
}
inline bool OptimizationOptions::has_map_and_filter_fusion() const {
  return _internal_has_map_and_filter_fusion();
}
inline void OptimizationOptions::set_has_map_and_filter_fusion() {
  _impl_._oneof_case_[3] = kMapAndFilterFusion;
}
inline void OptimizationOptions::clear_map_and_filter_fusion() {
  if (_internal_has_map_and_filter_fusion()) {
    _impl_.optional_map_and_filter_fusion_.map_and_filter_fusion_ = false;
    clear_has_optional_map_and_filter_fusion();
  }
}
inline bool OptimizationOptions::_internal_map_and_filter_fusion() const {
  if (_internal_has_map_and_filter_fusion()) {
    return _impl_.optional_map_and_filter_fusion_.map_and_filter_fusion_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_map_and_filter_fusion(bool value) {
  if (!_internal_has_map_and_filter_fusion()) {
    clear_optional_map_and_filter_fusion();
    set_has_map_and_filter_fusion();
  }
  _impl_.optional_map_and_filter_fusion_.map_and_filter_fusion_ = value;
}
inline bool OptimizationOptions::map_and_filter_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_and_filter_fusion)
  return _internal_map_and_filter_fusion();
}
inline void OptimizationOptions::set_map_and_filter_fusion(bool value) {
  _internal_set_map_and_filter_fusion(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_and_filter_fusion)
}

// bool map_fusion = 11;
inline bool OptimizationOptions::_internal_has_map_fusion() const {
  return optional_map_fusion_case() == kMapFusion;
}
inline bool OptimizationOptions::has_map_fusion() const {
  return _internal_has_map_fusion();
}
inline void OptimizationOptions::set_has_map_fusion() {
  _impl_._oneof_case_[4] = kMapFusion;
}
inline void OptimizationOptions::clear_map_fusion() {
  if (_internal_has_map_fusion()) {
    _impl_.optional_map_fusion_.map_fusion_ = false;
    clear_has_optional_map_fusion();
  }
}
inline bool OptimizationOptions::_internal_map_fusion() const {
  if (_internal_has_map_fusion()) {
    return _impl_.optional_map_fusion_.map_fusion_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_map_fusion(bool value) {
  if (!_internal_has_map_fusion()) {
    clear_optional_map_fusion();
    set_has_map_fusion();
  }
  _impl_.optional_map_fusion_.map_fusion_ = value;
}
inline bool OptimizationOptions::map_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_fusion)
  return _internal_map_fusion();
}
inline void OptimizationOptions::set_map_fusion(bool value) {
  _internal_set_map_fusion(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_fusion)
}

// bool map_parallelization = 12;
inline bool OptimizationOptions::_internal_has_map_parallelization() const {
  return optional_map_parallelization_case() == kMapParallelization;
}
inline bool OptimizationOptions::has_map_parallelization() const {
  return _internal_has_map_parallelization();
}
inline void OptimizationOptions::set_has_map_parallelization() {
  _impl_._oneof_case_[5] = kMapParallelization;
}
inline void OptimizationOptions::clear_map_parallelization() {
  if (_internal_has_map_parallelization()) {
    _impl_.optional_map_parallelization_.map_parallelization_ = false;
    clear_has_optional_map_parallelization();
  }
}
inline bool OptimizationOptions::_internal_map_parallelization() const {
  if (_internal_has_map_parallelization()) {
    return _impl_.optional_map_parallelization_.map_parallelization_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_map_parallelization(bool value) {
  if (!_internal_has_map_parallelization()) {
    clear_optional_map_parallelization();
    set_has_map_parallelization();
  }
  _impl_.optional_map_parallelization_.map_parallelization_ = value;
}
inline bool OptimizationOptions::map_parallelization() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_parallelization)
  return _internal_map_parallelization();
}
inline void OptimizationOptions::set_map_parallelization(bool value) {
  _internal_set_map_parallelization(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_parallelization)
}

// bool noop_elimination = 14;
inline bool OptimizationOptions::_internal_has_noop_elimination() const {
  return optional_noop_elimination_case() == kNoopElimination;
}
inline bool OptimizationOptions::has_noop_elimination() const {
  return _internal_has_noop_elimination();
}
inline void OptimizationOptions::set_has_noop_elimination() {
  _impl_._oneof_case_[6] = kNoopElimination;
}
inline void OptimizationOptions::clear_noop_elimination() {
  if (_internal_has_noop_elimination()) {
    _impl_.optional_noop_elimination_.noop_elimination_ = false;
    clear_has_optional_noop_elimination();
  }
}
inline bool OptimizationOptions::_internal_noop_elimination() const {
  if (_internal_has_noop_elimination()) {
    return _impl_.optional_noop_elimination_.noop_elimination_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_noop_elimination(bool value) {
  if (!_internal_has_noop_elimination()) {
    clear_optional_noop_elimination();
    set_has_noop_elimination();
  }
  _impl_.optional_noop_elimination_.noop_elimination_ = value;
}
inline bool OptimizationOptions::noop_elimination() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.noop_elimination)
  return _internal_noop_elimination();
}
inline void OptimizationOptions::set_noop_elimination(bool value) {
  _internal_set_noop_elimination(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.noop_elimination)
}

// bool parallel_batch = 15;
inline bool OptimizationOptions::_internal_has_parallel_batch() const {
  return optional_parallel_batch_case() == kParallelBatch;
}
inline bool OptimizationOptions::has_parallel_batch() const {
  return _internal_has_parallel_batch();
}
inline void OptimizationOptions::set_has_parallel_batch() {
  _impl_._oneof_case_[7] = kParallelBatch;
}
inline void OptimizationOptions::clear_parallel_batch() {
  if (_internal_has_parallel_batch()) {
    _impl_.optional_parallel_batch_.parallel_batch_ = false;
    clear_has_optional_parallel_batch();
  }
}
inline bool OptimizationOptions::_internal_parallel_batch() const {
  if (_internal_has_parallel_batch()) {
    return _impl_.optional_parallel_batch_.parallel_batch_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_parallel_batch(bool value) {
  if (!_internal_has_parallel_batch()) {
    clear_optional_parallel_batch();
    set_has_parallel_batch();
  }
  _impl_.optional_parallel_batch_.parallel_batch_ = value;
}
inline bool OptimizationOptions::parallel_batch() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.parallel_batch)
  return _internal_parallel_batch();
}
inline void OptimizationOptions::set_parallel_batch(bool value) {
  _internal_set_parallel_batch(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.parallel_batch)
}

// bool shuffle_and_repeat_fusion = 17;
inline bool OptimizationOptions::_internal_has_shuffle_and_repeat_fusion() const {
  return optional_shuffle_and_repeat_fusion_case() == kShuffleAndRepeatFusion;
}
inline bool OptimizationOptions::has_shuffle_and_repeat_fusion() const {
  return _internal_has_shuffle_and_repeat_fusion();
}
inline void OptimizationOptions::set_has_shuffle_and_repeat_fusion() {
  _impl_._oneof_case_[8] = kShuffleAndRepeatFusion;
}
inline void OptimizationOptions::clear_shuffle_and_repeat_fusion() {
  if (_internal_has_shuffle_and_repeat_fusion()) {
    _impl_.optional_shuffle_and_repeat_fusion_.shuffle_and_repeat_fusion_ = false;
    clear_has_optional_shuffle_and_repeat_fusion();
  }
}
inline bool OptimizationOptions::_internal_shuffle_and_repeat_fusion() const {
  if (_internal_has_shuffle_and_repeat_fusion()) {
    return _impl_.optional_shuffle_and_repeat_fusion_.shuffle_and_repeat_fusion_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_shuffle_and_repeat_fusion(bool value) {
  if (!_internal_has_shuffle_and_repeat_fusion()) {
    clear_optional_shuffle_and_repeat_fusion();
    set_has_shuffle_and_repeat_fusion();
  }
  _impl_.optional_shuffle_and_repeat_fusion_.shuffle_and_repeat_fusion_ = value;
}
inline bool OptimizationOptions::shuffle_and_repeat_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.shuffle_and_repeat_fusion)
  return _internal_shuffle_and_repeat_fusion();
}
inline void OptimizationOptions::set_shuffle_and_repeat_fusion(bool value) {
  _internal_set_shuffle_and_repeat_fusion(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.shuffle_and_repeat_fusion)
}

// bool filter_parallelization = 18;
inline bool OptimizationOptions::_internal_has_filter_parallelization() const {
  return optional_filter_parallelization_case() == kFilterParallelization;
}
inline bool OptimizationOptions::has_filter_parallelization() const {
  return _internal_has_filter_parallelization();
}
inline void OptimizationOptions::set_has_filter_parallelization() {
  _impl_._oneof_case_[9] = kFilterParallelization;
}
inline void OptimizationOptions::clear_filter_parallelization() {
  if (_internal_has_filter_parallelization()) {
    _impl_.optional_filter_parallelization_.filter_parallelization_ = false;
    clear_has_optional_filter_parallelization();
  }
}
inline bool OptimizationOptions::_internal_filter_parallelization() const {
  if (_internal_has_filter_parallelization()) {
    return _impl_.optional_filter_parallelization_.filter_parallelization_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_filter_parallelization(bool value) {
  if (!_internal_has_filter_parallelization()) {
    clear_optional_filter_parallelization();
    set_has_filter_parallelization();
  }
  _impl_.optional_filter_parallelization_.filter_parallelization_ = value;
}
inline bool OptimizationOptions::filter_parallelization() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.filter_parallelization)
  return _internal_filter_parallelization();
}
inline void OptimizationOptions::set_filter_parallelization(bool value) {
  _internal_set_filter_parallelization(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.filter_parallelization)
}

// bool inject_prefetch = 19;
inline bool OptimizationOptions::_internal_has_inject_prefetch() const {
  return optional_inject_prefetch_case() == kInjectPrefetch;
}
inline bool OptimizationOptions::has_inject_prefetch() const {
  return _internal_has_inject_prefetch();
}
inline void OptimizationOptions::set_has_inject_prefetch() {
  _impl_._oneof_case_[10] = kInjectPrefetch;
}
inline void OptimizationOptions::clear_inject_prefetch() {
  if (_internal_has_inject_prefetch()) {
    _impl_.optional_inject_prefetch_.inject_prefetch_ = false;
    clear_has_optional_inject_prefetch();
  }
}
inline bool OptimizationOptions::_internal_inject_prefetch() const {
  if (_internal_has_inject_prefetch()) {
    return _impl_.optional_inject_prefetch_.inject_prefetch_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_inject_prefetch(bool value) {
  if (!_internal_has_inject_prefetch()) {
    clear_optional_inject_prefetch();
    set_has_inject_prefetch();
  }
  _impl_.optional_inject_prefetch_.inject_prefetch_ = value;
}
inline bool OptimizationOptions::inject_prefetch() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.inject_prefetch)
  return _internal_inject_prefetch();
}
inline void OptimizationOptions::set_inject_prefetch(bool value) {
  _internal_set_inject_prefetch(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.inject_prefetch)
}

// bool warm_start = 20;
inline bool OptimizationOptions::_internal_has_warm_start() const {
  return optional_warm_start_case() == kWarmStart;
}
inline bool OptimizationOptions::has_warm_start() const {
  return _internal_has_warm_start();
}
inline void OptimizationOptions::set_has_warm_start() {
  _impl_._oneof_case_[11] = kWarmStart;
}
inline void OptimizationOptions::clear_warm_start() {
  if (_internal_has_warm_start()) {
    _impl_.optional_warm_start_.warm_start_ = false;
    clear_has_optional_warm_start();
  }
}
inline bool OptimizationOptions::_internal_warm_start() const {
  if (_internal_has_warm_start()) {
    return _impl_.optional_warm_start_.warm_start_;
  }
  return false;
}
inline void OptimizationOptions::_internal_set_warm_start(bool value) {
  if (!_internal_has_warm_start()) {
    clear_optional_warm_start();
    set_has_warm_start();
  }
  _impl_.optional_warm_start_.warm_start_ = value;
}
inline bool OptimizationOptions::warm_start() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.warm_start)
  return _internal_warm_start();
}
inline void OptimizationOptions::set_warm_start(bool value) {
  _internal_set_warm_start(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.warm_start)
}

inline bool OptimizationOptions::has_optional_apply_default_optimizations() const {
  return optional_apply_default_optimizations_case() != OPTIONAL_APPLY_DEFAULT_OPTIMIZATIONS_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_apply_default_optimizations() {
  _impl_._oneof_case_[0] = OPTIONAL_APPLY_DEFAULT_OPTIMIZATIONS_NOT_SET;
}
inline bool OptimizationOptions::has_optional_filter_fusion() const {
  return optional_filter_fusion_case() != OPTIONAL_FILTER_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_filter_fusion() {
  _impl_._oneof_case_[1] = OPTIONAL_FILTER_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_and_batch_fusion() const {
  return optional_map_and_batch_fusion_case() != OPTIONAL_MAP_AND_BATCH_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_and_batch_fusion() {
  _impl_._oneof_case_[2] = OPTIONAL_MAP_AND_BATCH_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_and_filter_fusion() const {
  return optional_map_and_filter_fusion_case() != OPTIONAL_MAP_AND_FILTER_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_and_filter_fusion() {
  _impl_._oneof_case_[3] = OPTIONAL_MAP_AND_FILTER_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_fusion() const {
  return optional_map_fusion_case() != OPTIONAL_MAP_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_fusion() {
  _impl_._oneof_case_[4] = OPTIONAL_MAP_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_parallelization() const {
  return optional_map_parallelization_case() != OPTIONAL_MAP_PARALLELIZATION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_parallelization() {
  _impl_._oneof_case_[5] = OPTIONAL_MAP_PARALLELIZATION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_noop_elimination() const {
  return optional_noop_elimination_case() != OPTIONAL_NOOP_ELIMINATION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_noop_elimination() {
  _impl_._oneof_case_[6] = OPTIONAL_NOOP_ELIMINATION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_parallel_batch() const {
  return optional_parallel_batch_case() != OPTIONAL_PARALLEL_BATCH_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_parallel_batch() {
  _impl_._oneof_case_[7] = OPTIONAL_PARALLEL_BATCH_NOT_SET;
}
inline bool OptimizationOptions::has_optional_shuffle_and_repeat_fusion() const {
  return optional_shuffle_and_repeat_fusion_case() != OPTIONAL_SHUFFLE_AND_REPEAT_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_shuffle_and_repeat_fusion() {
  _impl_._oneof_case_[8] = OPTIONAL_SHUFFLE_AND_REPEAT_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_filter_parallelization() const {
  return optional_filter_parallelization_case() != OPTIONAL_FILTER_PARALLELIZATION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_filter_parallelization() {
  _impl_._oneof_case_[9] = OPTIONAL_FILTER_PARALLELIZATION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_inject_prefetch() const {
  return optional_inject_prefetch_case() != OPTIONAL_INJECT_PREFETCH_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_inject_prefetch() {
  _impl_._oneof_case_[10] = OPTIONAL_INJECT_PREFETCH_NOT_SET;
}
inline bool OptimizationOptions::has_optional_warm_start() const {
  return optional_warm_start_case() != OPTIONAL_WARM_START_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_warm_start() {
  _impl_._oneof_case_[11] = OPTIONAL_WARM_START_NOT_SET;
}
inline OptimizationOptions::OptionalApplyDefaultOptimizationsCase OptimizationOptions::optional_apply_default_optimizations_case() const {
  return OptimizationOptions::OptionalApplyDefaultOptimizationsCase(_impl_._oneof_case_[0]);
}
inline OptimizationOptions::OptionalFilterFusionCase OptimizationOptions::optional_filter_fusion_case() const {
  return OptimizationOptions::OptionalFilterFusionCase(_impl_._oneof_case_[1]);
}
inline OptimizationOptions::OptionalMapAndBatchFusionCase OptimizationOptions::optional_map_and_batch_fusion_case() const {
  return OptimizationOptions::OptionalMapAndBatchFusionCase(_impl_._oneof_case_[2]);
}
inline OptimizationOptions::OptionalMapAndFilterFusionCase OptimizationOptions::optional_map_and_filter_fusion_case() const {
  return OptimizationOptions::OptionalMapAndFilterFusionCase(_impl_._oneof_case_[3]);
}
inline OptimizationOptions::OptionalMapFusionCase OptimizationOptions::optional_map_fusion_case() const {
  return OptimizationOptions::OptionalMapFusionCase(_impl_._oneof_case_[4]);
}
inline OptimizationOptions::OptionalMapParallelizationCase OptimizationOptions::optional_map_parallelization_case() const {
  return OptimizationOptions::OptionalMapParallelizationCase(_impl_._oneof_case_[5]);
}
inline OptimizationOptions::OptionalNoopEliminationCase OptimizationOptions::optional_noop_elimination_case() const {
  return OptimizationOptions::OptionalNoopEliminationCase(_impl_._oneof_case_[6]);
}
inline OptimizationOptions::OptionalParallelBatchCase OptimizationOptions::optional_parallel_batch_case() const {
  return OptimizationOptions::OptionalParallelBatchCase(_impl_._oneof_case_[7]);
}
inline OptimizationOptions::OptionalShuffleAndRepeatFusionCase OptimizationOptions::optional_shuffle_and_repeat_fusion_case() const {
  return OptimizationOptions::OptionalShuffleAndRepeatFusionCase(_impl_._oneof_case_[8]);
}
inline OptimizationOptions::OptionalFilterParallelizationCase OptimizationOptions::optional_filter_parallelization_case() const {
  return OptimizationOptions::OptionalFilterParallelizationCase(_impl_._oneof_case_[9]);
}
inline OptimizationOptions::OptionalInjectPrefetchCase OptimizationOptions::optional_inject_prefetch_case() const {
  return OptimizationOptions::OptionalInjectPrefetchCase(_impl_._oneof_case_[10]);
}
inline OptimizationOptions::OptionalWarmStartCase OptimizationOptions::optional_warm_start_case() const {
  return OptimizationOptions::OptionalWarmStartCase(_impl_._oneof_case_[11]);
}
// -------------------------------------------------------------------

// ThreadingOptions

// int32 max_intra_op_parallelism = 1;
inline bool ThreadingOptions::_internal_has_max_intra_op_parallelism() const {
  return optional_max_intra_op_parallelism_case() == kMaxIntraOpParallelism;
}
inline bool ThreadingOptions::has_max_intra_op_parallelism() const {
  return _internal_has_max_intra_op_parallelism();
}
inline void ThreadingOptions::set_has_max_intra_op_parallelism() {
  _impl_._oneof_case_[0] = kMaxIntraOpParallelism;
}
inline void ThreadingOptions::clear_max_intra_op_parallelism() {
  if (_internal_has_max_intra_op_parallelism()) {
    _impl_.optional_max_intra_op_parallelism_.max_intra_op_parallelism_ = 0;
    clear_has_optional_max_intra_op_parallelism();
  }
}
inline int32_t ThreadingOptions::_internal_max_intra_op_parallelism() const {
  if (_internal_has_max_intra_op_parallelism()) {
    return _impl_.optional_max_intra_op_parallelism_.max_intra_op_parallelism_;
  }
  return 0;
}
inline void ThreadingOptions::_internal_set_max_intra_op_parallelism(int32_t value) {
  if (!_internal_has_max_intra_op_parallelism()) {
    clear_optional_max_intra_op_parallelism();
    set_has_max_intra_op_parallelism();
  }
  _impl_.optional_max_intra_op_parallelism_.max_intra_op_parallelism_ = value;
}
inline int32_t ThreadingOptions::max_intra_op_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.ThreadingOptions.max_intra_op_parallelism)
  return _internal_max_intra_op_parallelism();
}
inline void ThreadingOptions::set_max_intra_op_parallelism(int32_t value) {
  _internal_set_max_intra_op_parallelism(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.ThreadingOptions.max_intra_op_parallelism)
}

// int32 private_threadpool_size = 2;
inline bool ThreadingOptions::_internal_has_private_threadpool_size() const {
  return optional_private_threadpool_size_case() == kPrivateThreadpoolSize;
}
inline bool ThreadingOptions::has_private_threadpool_size() const {
  return _internal_has_private_threadpool_size();
}
inline void ThreadingOptions::set_has_private_threadpool_size() {
  _impl_._oneof_case_[1] = kPrivateThreadpoolSize;
}
inline void ThreadingOptions::clear_private_threadpool_size() {
  if (_internal_has_private_threadpool_size()) {
    _impl_.optional_private_threadpool_size_.private_threadpool_size_ = 0;
    clear_has_optional_private_threadpool_size();
  }
}
inline int32_t ThreadingOptions::_internal_private_threadpool_size() const {
  if (_internal_has_private_threadpool_size()) {
    return _impl_.optional_private_threadpool_size_.private_threadpool_size_;
  }
  return 0;
}
inline void ThreadingOptions::_internal_set_private_threadpool_size(int32_t value) {
  if (!_internal_has_private_threadpool_size()) {
    clear_optional_private_threadpool_size();
    set_has_private_threadpool_size();
  }
  _impl_.optional_private_threadpool_size_.private_threadpool_size_ = value;
}
inline int32_t ThreadingOptions::private_threadpool_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.ThreadingOptions.private_threadpool_size)
  return _internal_private_threadpool_size();
}
inline void ThreadingOptions::set_private_threadpool_size(int32_t value) {
  _internal_set_private_threadpool_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.ThreadingOptions.private_threadpool_size)
}

inline bool ThreadingOptions::has_optional_max_intra_op_parallelism() const {
  return optional_max_intra_op_parallelism_case() != OPTIONAL_MAX_INTRA_OP_PARALLELISM_NOT_SET;
}
inline void ThreadingOptions::clear_has_optional_max_intra_op_parallelism() {
  _impl_._oneof_case_[0] = OPTIONAL_MAX_INTRA_OP_PARALLELISM_NOT_SET;
}
inline bool ThreadingOptions::has_optional_private_threadpool_size() const {
  return optional_private_threadpool_size_case() != OPTIONAL_PRIVATE_THREADPOOL_SIZE_NOT_SET;
}
inline void ThreadingOptions::clear_has_optional_private_threadpool_size() {
  _impl_._oneof_case_[1] = OPTIONAL_PRIVATE_THREADPOOL_SIZE_NOT_SET;
}
inline ThreadingOptions::OptionalMaxIntraOpParallelismCase ThreadingOptions::optional_max_intra_op_parallelism_case() const {
  return ThreadingOptions::OptionalMaxIntraOpParallelismCase(_impl_._oneof_case_[0]);
}
inline ThreadingOptions::OptionalPrivateThreadpoolSizeCase ThreadingOptions::optional_private_threadpool_size_case() const {
  return ThreadingOptions::OptionalPrivateThreadpoolSizeCase(_impl_._oneof_case_[1]);
}
// -------------------------------------------------------------------

// Options

// bool deterministic = 1;
inline bool Options::_internal_has_deterministic() const {
  return optional_deterministic_case() == kDeterministic;
}
inline bool Options::has_deterministic() const {
  return _internal_has_deterministic();
}
inline void Options::set_has_deterministic() {
  _impl_._oneof_case_[0] = kDeterministic;
}
inline void Options::clear_deterministic() {
  if (_internal_has_deterministic()) {
    _impl_.optional_deterministic_.deterministic_ = false;
    clear_has_optional_deterministic();
  }
}
inline bool Options::_internal_deterministic() const {
  if (_internal_has_deterministic()) {
    return _impl_.optional_deterministic_.deterministic_;
  }
  return false;
}
inline void Options::_internal_set_deterministic(bool value) {
  if (!_internal_has_deterministic()) {
    clear_optional_deterministic();
    set_has_deterministic();
  }
  _impl_.optional_deterministic_.deterministic_ = value;
}
inline bool Options::deterministic() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.deterministic)
  return _internal_deterministic();
}
inline void Options::set_deterministic(bool value) {
  _internal_set_deterministic(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.deterministic)
}

// .tensorflow.data.AutotuneOptions autotune_options = 7;
inline bool Options::_internal_has_autotune_options() const {
  return this != internal_default_instance() && _impl_.autotune_options_ != nullptr;
}
inline bool Options::has_autotune_options() const {
  return _internal_has_autotune_options();
}
inline void Options::clear_autotune_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.autotune_options_ != nullptr) {
    delete _impl_.autotune_options_;
  }
  _impl_.autotune_options_ = nullptr;
}
inline const ::tensorflow::data::AutotuneOptions& Options::_internal_autotune_options() const {
  const ::tensorflow::data::AutotuneOptions* p = _impl_.autotune_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::data::AutotuneOptions&>(
      ::tensorflow::data::_AutotuneOptions_default_instance_);
}
inline const ::tensorflow::data::AutotuneOptions& Options::autotune_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.autotune_options)
  return _internal_autotune_options();
}
inline void Options::unsafe_arena_set_allocated_autotune_options(
    ::tensorflow::data::AutotuneOptions* autotune_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.autotune_options_);
  }
  _impl_.autotune_options_ = autotune_options;
  if (autotune_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.Options.autotune_options)
}
inline ::tensorflow::data::AutotuneOptions* Options::release_autotune_options() {
  
  ::tensorflow::data::AutotuneOptions* temp = _impl_.autotune_options_;
  _impl_.autotune_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::data::AutotuneOptions* Options::unsafe_arena_release_autotune_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.autotune_options)
  
  ::tensorflow::data::AutotuneOptions* temp = _impl_.autotune_options_;
  _impl_.autotune_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::AutotuneOptions* Options::_internal_mutable_autotune_options() {
  
  if (_impl_.autotune_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::AutotuneOptions>(GetArenaForAllocation());
    _impl_.autotune_options_ = p;
  }
  return _impl_.autotune_options_;
}
inline ::tensorflow::data::AutotuneOptions* Options::mutable_autotune_options() {
  ::tensorflow::data::AutotuneOptions* _msg = _internal_mutable_autotune_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.autotune_options)
  return _msg;
}
inline void Options::set_allocated_autotune_options(::tensorflow::data::AutotuneOptions* autotune_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.autotune_options_;
  }
  if (autotune_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(autotune_options);
    if (message_arena != submessage_arena) {
      autotune_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, autotune_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.autotune_options_ = autotune_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.autotune_options)
}

// .tensorflow.data.DistributeOptions distribute_options = 2;
inline bool Options::_internal_has_distribute_options() const {
  return this != internal_default_instance() && _impl_.distribute_options_ != nullptr;
}
inline bool Options::has_distribute_options() const {
  return _internal_has_distribute_options();
}
inline void Options::clear_distribute_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.distribute_options_ != nullptr) {
    delete _impl_.distribute_options_;
  }
  _impl_.distribute_options_ = nullptr;
}
inline const ::tensorflow::data::DistributeOptions& Options::_internal_distribute_options() const {
  const ::tensorflow::data::DistributeOptions* p = _impl_.distribute_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::data::DistributeOptions&>(
      ::tensorflow::data::_DistributeOptions_default_instance_);
}
inline const ::tensorflow::data::DistributeOptions& Options::distribute_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.distribute_options)
  return _internal_distribute_options();
}
inline void Options::unsafe_arena_set_allocated_distribute_options(
    ::tensorflow::data::DistributeOptions* distribute_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.distribute_options_);
  }
  _impl_.distribute_options_ = distribute_options;
  if (distribute_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.Options.distribute_options)
}
inline ::tensorflow::data::DistributeOptions* Options::release_distribute_options() {
  
  ::tensorflow::data::DistributeOptions* temp = _impl_.distribute_options_;
  _impl_.distribute_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::data::DistributeOptions* Options::unsafe_arena_release_distribute_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.distribute_options)
  
  ::tensorflow::data::DistributeOptions* temp = _impl_.distribute_options_;
  _impl_.distribute_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::DistributeOptions* Options::_internal_mutable_distribute_options() {
  
  if (_impl_.distribute_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::DistributeOptions>(GetArenaForAllocation());
    _impl_.distribute_options_ = p;
  }
  return _impl_.distribute_options_;
}
inline ::tensorflow::data::DistributeOptions* Options::mutable_distribute_options() {
  ::tensorflow::data::DistributeOptions* _msg = _internal_mutable_distribute_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.distribute_options)
  return _msg;
}
inline void Options::set_allocated_distribute_options(::tensorflow::data::DistributeOptions* distribute_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.distribute_options_;
  }
  if (distribute_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(distribute_options);
    if (message_arena != submessage_arena) {
      distribute_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, distribute_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.distribute_options_ = distribute_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.distribute_options)
}

// .tensorflow.data.OptimizationOptions optimization_options = 3;
inline bool Options::_internal_has_optimization_options() const {
  return this != internal_default_instance() && _impl_.optimization_options_ != nullptr;
}
inline bool Options::has_optimization_options() const {
  return _internal_has_optimization_options();
}
inline void Options::clear_optimization_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.optimization_options_ != nullptr) {
    delete _impl_.optimization_options_;
  }
  _impl_.optimization_options_ = nullptr;
}
inline const ::tensorflow::data::OptimizationOptions& Options::_internal_optimization_options() const {
  const ::tensorflow::data::OptimizationOptions* p = _impl_.optimization_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::data::OptimizationOptions&>(
      ::tensorflow::data::_OptimizationOptions_default_instance_);
}
inline const ::tensorflow::data::OptimizationOptions& Options::optimization_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.optimization_options)
  return _internal_optimization_options();
}
inline void Options::unsafe_arena_set_allocated_optimization_options(
    ::tensorflow::data::OptimizationOptions* optimization_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimization_options_);
  }
  _impl_.optimization_options_ = optimization_options;
  if (optimization_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.Options.optimization_options)
}
inline ::tensorflow::data::OptimizationOptions* Options::release_optimization_options() {
  
  ::tensorflow::data::OptimizationOptions* temp = _impl_.optimization_options_;
  _impl_.optimization_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::data::OptimizationOptions* Options::unsafe_arena_release_optimization_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.optimization_options)
  
  ::tensorflow::data::OptimizationOptions* temp = _impl_.optimization_options_;
  _impl_.optimization_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::OptimizationOptions* Options::_internal_mutable_optimization_options() {
  
  if (_impl_.optimization_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::OptimizationOptions>(GetArenaForAllocation());
    _impl_.optimization_options_ = p;
  }
  return _impl_.optimization_options_;
}
inline ::tensorflow::data::OptimizationOptions* Options::mutable_optimization_options() {
  ::tensorflow::data::OptimizationOptions* _msg = _internal_mutable_optimization_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.optimization_options)
  return _msg;
}
inline void Options::set_allocated_optimization_options(::tensorflow::data::OptimizationOptions* optimization_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.optimization_options_;
  }
  if (optimization_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(optimization_options);
    if (message_arena != submessage_arena) {
      optimization_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.optimization_options_ = optimization_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.optimization_options)
}

// bool slack = 4;
inline bool Options::_internal_has_slack() const {
  return optional_slack_case() == kSlack;
}
inline bool Options::has_slack() const {
  return _internal_has_slack();
}
inline void Options::set_has_slack() {
  _impl_._oneof_case_[1] = kSlack;
}
inline void Options::clear_slack() {
  if (_internal_has_slack()) {
    _impl_.optional_slack_.slack_ = false;
    clear_has_optional_slack();
  }
}
inline bool Options::_internal_slack() const {
  if (_internal_has_slack()) {
    return _impl_.optional_slack_.slack_;
  }
  return false;
}
inline void Options::_internal_set_slack(bool value) {
  if (!_internal_has_slack()) {
    clear_optional_slack();
    set_has_slack();
  }
  _impl_.optional_slack_.slack_ = value;
}
inline bool Options::slack() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.slack)
  return _internal_slack();
}
inline void Options::set_slack(bool value) {
  _internal_set_slack(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.slack)
}

// .tensorflow.data.ThreadingOptions threading_options = 5;
inline bool Options::_internal_has_threading_options() const {
  return this != internal_default_instance() && _impl_.threading_options_ != nullptr;
}
inline bool Options::has_threading_options() const {
  return _internal_has_threading_options();
}
inline void Options::clear_threading_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.threading_options_ != nullptr) {
    delete _impl_.threading_options_;
  }
  _impl_.threading_options_ = nullptr;
}
inline const ::tensorflow::data::ThreadingOptions& Options::_internal_threading_options() const {
  const ::tensorflow::data::ThreadingOptions* p = _impl_.threading_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::data::ThreadingOptions&>(
      ::tensorflow::data::_ThreadingOptions_default_instance_);
}
inline const ::tensorflow::data::ThreadingOptions& Options::threading_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.threading_options)
  return _internal_threading_options();
}
inline void Options::unsafe_arena_set_allocated_threading_options(
    ::tensorflow::data::ThreadingOptions* threading_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.threading_options_);
  }
  _impl_.threading_options_ = threading_options;
  if (threading_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.Options.threading_options)
}
inline ::tensorflow::data::ThreadingOptions* Options::release_threading_options() {
  
  ::tensorflow::data::ThreadingOptions* temp = _impl_.threading_options_;
  _impl_.threading_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::data::ThreadingOptions* Options::unsafe_arena_release_threading_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.threading_options)
  
  ::tensorflow::data::ThreadingOptions* temp = _impl_.threading_options_;
  _impl_.threading_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::ThreadingOptions* Options::_internal_mutable_threading_options() {
  
  if (_impl_.threading_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::ThreadingOptions>(GetArenaForAllocation());
    _impl_.threading_options_ = p;
  }
  return _impl_.threading_options_;
}
inline ::tensorflow::data::ThreadingOptions* Options::mutable_threading_options() {
  ::tensorflow::data::ThreadingOptions* _msg = _internal_mutable_threading_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.threading_options)
  return _msg;
}
inline void Options::set_allocated_threading_options(::tensorflow::data::ThreadingOptions* threading_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.threading_options_;
  }
  if (threading_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(threading_options);
    if (message_arena != submessage_arena) {
      threading_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, threading_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.threading_options_ = threading_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.threading_options)
}

// .tensorflow.data.ExternalStatePolicy external_state_policy = 6;
inline bool Options::_internal_has_external_state_policy() const {
  return optional_external_state_policy_case() == kExternalStatePolicy;
}
inline bool Options::has_external_state_policy() const {
  return _internal_has_external_state_policy();
}
inline void Options::set_has_external_state_policy() {
  _impl_._oneof_case_[2] = kExternalStatePolicy;
}
inline void Options::clear_external_state_policy() {
  if (_internal_has_external_state_policy()) {
    _impl_.optional_external_state_policy_.external_state_policy_ = 0;
    clear_has_optional_external_state_policy();
  }
}
inline ::tensorflow::data::ExternalStatePolicy Options::_internal_external_state_policy() const {
  if (_internal_has_external_state_policy()) {
    return static_cast< ::tensorflow::data::ExternalStatePolicy >(_impl_.optional_external_state_policy_.external_state_policy_);
  }
  return static_cast< ::tensorflow::data::ExternalStatePolicy >(0);
}
inline ::tensorflow::data::ExternalStatePolicy Options::external_state_policy() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.external_state_policy)
  return _internal_external_state_policy();
}
inline void Options::_internal_set_external_state_policy(::tensorflow::data::ExternalStatePolicy value) {
  if (!_internal_has_external_state_policy()) {
    clear_optional_external_state_policy();
    set_has_external_state_policy();
  }
  _impl_.optional_external_state_policy_.external_state_policy_ = value;
}
inline void Options::set_external_state_policy(::tensorflow::data::ExternalStatePolicy value) {
  _internal_set_external_state_policy(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.external_state_policy)
}

// bool symbolic_checkpoint = 8;
inline bool Options::_internal_has_symbolic_checkpoint() const {
  return optional_symbolic_checkpoint_case() == kSymbolicCheckpoint;
}
inline bool Options::has_symbolic_checkpoint() const {
  return _internal_has_symbolic_checkpoint();
}
inline void Options::set_has_symbolic_checkpoint() {
  _impl_._oneof_case_[3] = kSymbolicCheckpoint;
}
inline void Options::clear_symbolic_checkpoint() {
  if (_internal_has_symbolic_checkpoint()) {
    _impl_.optional_symbolic_checkpoint_.symbolic_checkpoint_ = false;
    clear_has_optional_symbolic_checkpoint();
  }
}
inline bool Options::_internal_symbolic_checkpoint() const {
  if (_internal_has_symbolic_checkpoint()) {
    return _impl_.optional_symbolic_checkpoint_.symbolic_checkpoint_;
  }
  return false;
}
inline void Options::_internal_set_symbolic_checkpoint(bool value) {
  if (!_internal_has_symbolic_checkpoint()) {
    clear_optional_symbolic_checkpoint();
    set_has_symbolic_checkpoint();
  }
  _impl_.optional_symbolic_checkpoint_.symbolic_checkpoint_ = value;
}
inline bool Options::symbolic_checkpoint() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.symbolic_checkpoint)
  return _internal_symbolic_checkpoint();
}
inline void Options::set_symbolic_checkpoint(bool value) {
  _internal_set_symbolic_checkpoint(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.symbolic_checkpoint)
}

inline bool Options::has_optional_deterministic() const {
  return optional_deterministic_case() != OPTIONAL_DETERMINISTIC_NOT_SET;
}
inline void Options::clear_has_optional_deterministic() {
  _impl_._oneof_case_[0] = OPTIONAL_DETERMINISTIC_NOT_SET;
}
inline bool Options::has_optional_slack() const {
  return optional_slack_case() != OPTIONAL_SLACK_NOT_SET;
}
inline void Options::clear_has_optional_slack() {
  _impl_._oneof_case_[1] = OPTIONAL_SLACK_NOT_SET;
}
inline bool Options::has_optional_external_state_policy() const {
  return optional_external_state_policy_case() != OPTIONAL_EXTERNAL_STATE_POLICY_NOT_SET;
}
inline void Options::clear_has_optional_external_state_policy() {
  _impl_._oneof_case_[2] = OPTIONAL_EXTERNAL_STATE_POLICY_NOT_SET;
}
inline bool Options::has_optional_symbolic_checkpoint() const {
  return optional_symbolic_checkpoint_case() != OPTIONAL_SYMBOLIC_CHECKPOINT_NOT_SET;
}
inline void Options::clear_has_optional_symbolic_checkpoint() {
  _impl_._oneof_case_[3] = OPTIONAL_SYMBOLIC_CHECKPOINT_NOT_SET;
}
inline Options::OptionalDeterministicCase Options::optional_deterministic_case() const {
  return Options::OptionalDeterministicCase(_impl_._oneof_case_[0]);
}
inline Options::OptionalSlackCase Options::optional_slack_case() const {
  return Options::OptionalSlackCase(_impl_._oneof_case_[1]);
}
inline Options::OptionalExternalStatePolicyCase Options::optional_external_state_policy_case() const {
  return Options::OptionalExternalStatePolicyCase(_impl_._oneof_case_[2]);
}
inline Options::OptionalSymbolicCheckpointCase Options::optional_symbolic_checkpoint_case() const {
  return Options::OptionalSymbolicCheckpointCase(_impl_._oneof_case_[3]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace data
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::data::CardinalityOptions_ComputeLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::CardinalityOptions_ComputeLevel>() {
  return ::tensorflow::data::CardinalityOptions_ComputeLevel_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::AutoShardPolicy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::AutoShardPolicy>() {
  return ::tensorflow::data::AutoShardPolicy_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::ExternalStatePolicy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::ExternalStatePolicy>() {
  return ::tensorflow::data::ExternalStatePolicy_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto
