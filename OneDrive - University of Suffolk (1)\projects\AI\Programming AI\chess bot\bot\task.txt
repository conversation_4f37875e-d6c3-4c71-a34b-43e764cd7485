code a chess bot. use learning models as the AI, train them using tensorflow, and create a simple UI for me to play against the bot.

implement improvements for the chess_bot project, found inside the "chess_bot" directory

--------------------

Enhance the AI model: The current model is quite simple. We can improve it by using a more sophisticated architecture and incorporating chess-specific features.
Implement position evaluation: Add a method to evaluate board positions based on piece values and positions.
Improve the training process: Implement a more advanced training method, such as self-play or learning from master games.
Add difficulty levels: Implement different difficulty levels for the AI.
Enhance the UI: Improve the user interface to display more information and provide a better user experience.

