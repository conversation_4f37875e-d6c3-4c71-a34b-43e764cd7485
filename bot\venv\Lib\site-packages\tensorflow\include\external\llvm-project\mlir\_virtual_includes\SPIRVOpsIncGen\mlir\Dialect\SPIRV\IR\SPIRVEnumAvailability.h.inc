/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* SPIR-V Enum Availability Declarations                                      *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(Capability value);
std::optional<::mlir::spirv::Version> getMinVersion(Capability value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(AddressingModel value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(AddressingModel value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(BuiltIn value);
std::optional<::mlir::spirv::Version> getMinVersion(BuiltIn value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(BuiltIn value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(Decoration value);
std::optional<::mlir::spirv::Version> getMaxVersion(Decoration value);
std::optional<::mlir::spirv::Version> getMinVersion(Decoration value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(Decoration value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(Dim value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ExecutionMode value);
std::optional<::mlir::spirv::Version> getMinVersion(ExecutionMode value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(ExecutionMode value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ExecutionModel value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(FunctionControl value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(GroupOperation value);
std::optional<::mlir::spirv::Version> getMinVersion(GroupOperation value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(GroupOperation value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ImageFormat value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ImageOperands value);
std::optional<::mlir::spirv::Version> getMinVersion(ImageOperands value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(LinkageType value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(LinkageType value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::mlir::spirv::Version> getMinVersion(LoopControl value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(LoopControl value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(LoopControl value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::mlir::spirv::Version> getMinVersion(MemoryAccess value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(MemoryAccess value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(MemoryAccess value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(MemoryModel value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(MemoryModel value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(MemorySemantics value);
std::optional<::mlir::spirv::Version> getMinVersion(MemorySemantics value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(MemorySemantics value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::mlir::spirv::Version> getMinVersion(Scope value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(Scope value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(StorageClass value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(StorageClass value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
std::optional<::mlir::spirv::Version> getMinVersion(PackedVectorFormat value);
std::optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(PackedVectorFormat value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
