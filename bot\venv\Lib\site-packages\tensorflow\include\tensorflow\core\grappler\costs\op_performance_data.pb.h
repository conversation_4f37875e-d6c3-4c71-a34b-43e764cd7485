// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/grappler/costs/op_performance_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/protobuf/device_properties.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
namespace tensorflow {
class LogNormalDistribution;
struct LogNormalDistributionDefaultTypeInternal;
extern LogNormalDistributionDefaultTypeInternal _LogNormalDistribution_default_instance_;
class NormalDistribution;
struct NormalDistributionDefaultTypeInternal;
extern NormalDistributionDefaultTypeInternal _NormalDistribution_default_instance_;
class OpInfo;
struct OpInfoDefaultTypeInternal;
extern OpInfoDefaultTypeInternal _OpInfo_default_instance_;
class OpInfo_AttrEntry_DoNotUse;
struct OpInfo_AttrEntry_DoNotUseDefaultTypeInternal;
extern OpInfo_AttrEntry_DoNotUseDefaultTypeInternal _OpInfo_AttrEntry_DoNotUse_default_instance_;
class OpInfo_TensorProperties;
struct OpInfo_TensorPropertiesDefaultTypeInternal;
extern OpInfo_TensorPropertiesDefaultTypeInternal _OpInfo_TensorProperties_default_instance_;
class OpPerformance;
struct OpPerformanceDefaultTypeInternal;
extern OpPerformanceDefaultTypeInternal _OpPerformance_default_instance_;
class OpPerformanceList;
struct OpPerformanceListDefaultTypeInternal;
extern OpPerformanceListDefaultTypeInternal _OpPerformanceList_default_instance_;
class OpPerformance_OpMemory;
struct OpPerformance_OpMemoryDefaultTypeInternal;
extern OpPerformance_OpMemoryDefaultTypeInternal _OpPerformance_OpMemory_default_instance_;
class SessionInfo;
struct SessionInfoDefaultTypeInternal;
extern SessionInfoDefaultTypeInternal _SessionInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::LogNormalDistribution* Arena::CreateMaybeMessage<::tensorflow::LogNormalDistribution>(Arena*);
template<> ::tensorflow::NormalDistribution* Arena::CreateMaybeMessage<::tensorflow::NormalDistribution>(Arena*);
template<> ::tensorflow::OpInfo* Arena::CreateMaybeMessage<::tensorflow::OpInfo>(Arena*);
template<> ::tensorflow::OpInfo_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::OpInfo_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::OpInfo_TensorProperties* Arena::CreateMaybeMessage<::tensorflow::OpInfo_TensorProperties>(Arena*);
template<> ::tensorflow::OpPerformance* Arena::CreateMaybeMessage<::tensorflow::OpPerformance>(Arena*);
template<> ::tensorflow::OpPerformanceList* Arena::CreateMaybeMessage<::tensorflow::OpPerformanceList>(Arena*);
template<> ::tensorflow::OpPerformance_OpMemory* Arena::CreateMaybeMessage<::tensorflow::OpPerformance_OpMemory>(Arena*);
template<> ::tensorflow::SessionInfo* Arena::CreateMaybeMessage<::tensorflow::SessionInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class SessionInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionInfo) */ {
 public:
  inline SessionInfo() : SessionInfo(nullptr) {}
  ~SessionInfo() override;
  explicit PROTOBUF_CONSTEXPR SessionInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SessionInfo(const SessionInfo& from);
  SessionInfo(SessionInfo&& from) noexcept
    : SessionInfo() {
    *this = ::std::move(from);
  }

  inline SessionInfo& operator=(const SessionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline SessionInfo& operator=(SessionInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SessionInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const SessionInfo* internal_default_instance() {
    return reinterpret_cast<const SessionInfo*>(
               &_SessionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SessionInfo& a, SessionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(SessionInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SessionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SessionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SessionInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SessionInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SessionInfo& from) {
    SessionInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SessionInfo";
  }
  protected:
  explicit SessionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIntraOpParallelismFieldNumber = 1,
  };
  // int64 intra_op_parallelism = 1;
  void clear_intra_op_parallelism();
  int64_t intra_op_parallelism() const;
  void set_intra_op_parallelism(int64_t value);
  private:
  int64_t _internal_intra_op_parallelism() const;
  void _internal_set_intra_op_parallelism(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SessionInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t intra_op_parallelism_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpInfo_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpInfo_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpInfo_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  OpInfo_AttrEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR OpInfo_AttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit OpInfo_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const OpInfo_AttrEntry_DoNotUse& other);
  static const OpInfo_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const OpInfo_AttrEntry_DoNotUse*>(&_OpInfo_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.OpInfo.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};

// -------------------------------------------------------------------

class OpInfo_TensorProperties final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpInfo.TensorProperties) */ {
 public:
  inline OpInfo_TensorProperties() : OpInfo_TensorProperties(nullptr) {}
  ~OpInfo_TensorProperties() override;
  explicit PROTOBUF_CONSTEXPR OpInfo_TensorProperties(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpInfo_TensorProperties(const OpInfo_TensorProperties& from);
  OpInfo_TensorProperties(OpInfo_TensorProperties&& from) noexcept
    : OpInfo_TensorProperties() {
    *this = ::std::move(from);
  }

  inline OpInfo_TensorProperties& operator=(const OpInfo_TensorProperties& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpInfo_TensorProperties& operator=(OpInfo_TensorProperties&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpInfo_TensorProperties& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpInfo_TensorProperties* internal_default_instance() {
    return reinterpret_cast<const OpInfo_TensorProperties*>(
               &_OpInfo_TensorProperties_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OpInfo_TensorProperties& a, OpInfo_TensorProperties& b) {
    a.Swap(&b);
  }
  inline void Swap(OpInfo_TensorProperties* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpInfo_TensorProperties* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpInfo_TensorProperties* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpInfo_TensorProperties>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpInfo_TensorProperties& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpInfo_TensorProperties& from) {
    OpInfo_TensorProperties::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpInfo_TensorProperties* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpInfo.TensorProperties";
  }
  protected:
  explicit OpInfo_TensorProperties(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kValueFieldNumber = 3,
    kDtypeFieldNumber = 1,
  };
  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto value = 3;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::tensorflow::TensorProto& value() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_value();
  ::tensorflow::TensorProto* mutable_value();
  void set_allocated_value(::tensorflow::TensorProto* value);
  private:
  const ::tensorflow::TensorProto& _internal_value() const;
  ::tensorflow::TensorProto* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::tensorflow::TensorProto* value);
  ::tensorflow::TensorProto* unsafe_arena_release_value();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OpInfo.TensorProperties)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::TensorProto* value_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpInfo) */ {
 public:
  inline OpInfo() : OpInfo(nullptr) {}
  ~OpInfo() override;
  explicit PROTOBUF_CONSTEXPR OpInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpInfo(const OpInfo& from);
  OpInfo(OpInfo&& from) noexcept
    : OpInfo() {
    *this = ::std::move(from);
  }

  inline OpInfo& operator=(const OpInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpInfo& operator=(OpInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpInfo* internal_default_instance() {
    return reinterpret_cast<const OpInfo*>(
               &_OpInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OpInfo& a, OpInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(OpInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpInfo& from) {
    OpInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpInfo";
  }
  protected:
  explicit OpInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpInfo_TensorProperties TensorProperties;

  // accessors -------------------------------------------------------

  enum : int {
    kAttrFieldNumber = 2,
    kInputsFieldNumber = 3,
    kOutputsFieldNumber = 5,
    kOpFieldNumber = 1,
    kDeviceFieldNumber = 4,
    kSessionInfoFieldNumber = 6,
  };
  // map<string, .tensorflow.AttrValue> attr = 2;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
  int inputs_size() const;
  private:
  int _internal_inputs_size() const;
  public:
  void clear_inputs();
  ::tensorflow::OpInfo_TensorProperties* mutable_inputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
      mutable_inputs();
  private:
  const ::tensorflow::OpInfo_TensorProperties& _internal_inputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* _internal_add_inputs();
  public:
  const ::tensorflow::OpInfo_TensorProperties& inputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* add_inputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
      inputs() const;

  // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
  int outputs_size() const;
  private:
  int _internal_outputs_size() const;
  public:
  void clear_outputs();
  ::tensorflow::OpInfo_TensorProperties* mutable_outputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
      mutable_outputs();
  private:
  const ::tensorflow::OpInfo_TensorProperties& _internal_outputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* _internal_add_outputs();
  public:
  const ::tensorflow::OpInfo_TensorProperties& outputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* add_outputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
      outputs() const;

  // string op = 1;
  void clear_op();
  const std::string& op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op();
  PROTOBUF_NODISCARD std::string* release_op();
  void set_allocated_op(std::string* op);
  private:
  const std::string& _internal_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op(const std::string& value);
  std::string* _internal_mutable_op();
  public:

  // .tensorflow.DeviceProperties device = 4;
  bool has_device() const;
  private:
  bool _internal_has_device() const;
  public:
  void clear_device();
  const ::tensorflow::DeviceProperties& device() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceProperties* release_device();
  ::tensorflow::DeviceProperties* mutable_device();
  void set_allocated_device(::tensorflow::DeviceProperties* device);
  private:
  const ::tensorflow::DeviceProperties& _internal_device() const;
  ::tensorflow::DeviceProperties* _internal_mutable_device();
  public:
  void unsafe_arena_set_allocated_device(
      ::tensorflow::DeviceProperties* device);
  ::tensorflow::DeviceProperties* unsafe_arena_release_device();

  // .tensorflow.SessionInfo session_info = 6;
  bool has_session_info() const;
  private:
  bool _internal_has_session_info() const;
  public:
  void clear_session_info();
  const ::tensorflow::SessionInfo& session_info() const;
  PROTOBUF_NODISCARD ::tensorflow::SessionInfo* release_session_info();
  ::tensorflow::SessionInfo* mutable_session_info();
  void set_allocated_session_info(::tensorflow::SessionInfo* session_info);
  private:
  const ::tensorflow::SessionInfo& _internal_session_info() const;
  ::tensorflow::SessionInfo* _internal_mutable_session_info();
  public:
  void unsafe_arena_set_allocated_session_info(
      ::tensorflow::SessionInfo* session_info);
  ::tensorflow::SessionInfo* unsafe_arena_release_session_info();

  // @@protoc_insertion_point(class_scope:tensorflow.OpInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        OpInfo_AttrEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attr_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties > inputs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties > outputs_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
    ::tensorflow::DeviceProperties* device_;
    ::tensorflow::SessionInfo* session_info_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class NormalDistribution final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NormalDistribution) */ {
 public:
  inline NormalDistribution() : NormalDistribution(nullptr) {}
  ~NormalDistribution() override;
  explicit PROTOBUF_CONSTEXPR NormalDistribution(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NormalDistribution(const NormalDistribution& from);
  NormalDistribution(NormalDistribution&& from) noexcept
    : NormalDistribution() {
    *this = ::std::move(from);
  }

  inline NormalDistribution& operator=(const NormalDistribution& from) {
    CopyFrom(from);
    return *this;
  }
  inline NormalDistribution& operator=(NormalDistribution&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NormalDistribution& default_instance() {
    return *internal_default_instance();
  }
  static inline const NormalDistribution* internal_default_instance() {
    return reinterpret_cast<const NormalDistribution*>(
               &_NormalDistribution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(NormalDistribution& a, NormalDistribution& b) {
    a.Swap(&b);
  }
  inline void Swap(NormalDistribution* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NormalDistribution* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NormalDistribution* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NormalDistribution>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NormalDistribution& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NormalDistribution& from) {
    NormalDistribution::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NormalDistribution* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NormalDistribution";
  }
  protected:
  explicit NormalDistribution(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMuFieldNumber = 1,
    kSigmaFieldNumber = 2,
  };
  // double mu = 1;
  void clear_mu();
  double mu() const;
  void set_mu(double value);
  private:
  double _internal_mu() const;
  void _internal_set_mu(double value);
  public:

  // double sigma = 2;
  void clear_sigma();
  double sigma() const;
  void set_sigma(double value);
  private:
  double _internal_sigma() const;
  void _internal_set_sigma(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.NormalDistribution)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double mu_;
    double sigma_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class LogNormalDistribution final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LogNormalDistribution) */ {
 public:
  inline LogNormalDistribution() : LogNormalDistribution(nullptr) {}
  ~LogNormalDistribution() override;
  explicit PROTOBUF_CONSTEXPR LogNormalDistribution(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LogNormalDistribution(const LogNormalDistribution& from);
  LogNormalDistribution(LogNormalDistribution&& from) noexcept
    : LogNormalDistribution() {
    *this = ::std::move(from);
  }

  inline LogNormalDistribution& operator=(const LogNormalDistribution& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogNormalDistribution& operator=(LogNormalDistribution&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LogNormalDistribution& default_instance() {
    return *internal_default_instance();
  }
  static inline const LogNormalDistribution* internal_default_instance() {
    return reinterpret_cast<const LogNormalDistribution*>(
               &_LogNormalDistribution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(LogNormalDistribution& a, LogNormalDistribution& b) {
    a.Swap(&b);
  }
  inline void Swap(LogNormalDistribution* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogNormalDistribution* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LogNormalDistribution* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LogNormalDistribution>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LogNormalDistribution& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LogNormalDistribution& from) {
    LogNormalDistribution::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogNormalDistribution* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LogNormalDistribution";
  }
  protected:
  explicit LogNormalDistribution(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMuFieldNumber = 1,
    kSigmaFieldNumber = 2,
  };
  // double mu = 1;
  void clear_mu();
  double mu() const;
  void set_mu(double value);
  private:
  double _internal_mu() const;
  void _internal_set_mu(double value);
  public:

  // double sigma = 2;
  void clear_sigma();
  double sigma() const;
  void set_sigma(double value);
  private:
  double _internal_sigma() const;
  void _internal_set_sigma(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.LogNormalDistribution)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double mu_;
    double sigma_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpPerformance_OpMemory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformance.OpMemory) */ {
 public:
  inline OpPerformance_OpMemory() : OpPerformance_OpMemory(nullptr) {}
  ~OpPerformance_OpMemory() override;
  explicit PROTOBUF_CONSTEXPR OpPerformance_OpMemory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpPerformance_OpMemory(const OpPerformance_OpMemory& from);
  OpPerformance_OpMemory(OpPerformance_OpMemory&& from) noexcept
    : OpPerformance_OpMemory() {
    *this = ::std::move(from);
  }

  inline OpPerformance_OpMemory& operator=(const OpPerformance_OpMemory& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpPerformance_OpMemory& operator=(OpPerformance_OpMemory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpPerformance_OpMemory& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpPerformance_OpMemory* internal_default_instance() {
    return reinterpret_cast<const OpPerformance_OpMemory*>(
               &_OpPerformance_OpMemory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(OpPerformance_OpMemory& a, OpPerformance_OpMemory& b) {
    a.Swap(&b);
  }
  inline void Swap(OpPerformance_OpMemory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpPerformance_OpMemory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpPerformance_OpMemory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpPerformance_OpMemory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpPerformance_OpMemory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpPerformance_OpMemory& from) {
    OpPerformance_OpMemory::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformance_OpMemory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpPerformance.OpMemory";
  }
  protected:
  explicit OpPerformance_OpMemory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputMemoryFieldNumber = 1,
    kTempMemoryFieldNumber = 2,
    kDeviceTempMemoryFieldNumber = 3,
    kPersistentMemoryFieldNumber = 4,
    kDevicePersistentMemoryFieldNumber = 5,
  };
  // repeated int64 output_memory = 1;
  int output_memory_size() const;
  private:
  int _internal_output_memory_size() const;
  public:
  void clear_output_memory();
  private:
  int64_t _internal_output_memory(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_output_memory() const;
  void _internal_add_output_memory(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_output_memory();
  public:
  int64_t output_memory(int index) const;
  void set_output_memory(int index, int64_t value);
  void add_output_memory(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      output_memory() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_output_memory();

  // int64 temp_memory = 2;
  void clear_temp_memory();
  int64_t temp_memory() const;
  void set_temp_memory(int64_t value);
  private:
  int64_t _internal_temp_memory() const;
  void _internal_set_temp_memory(int64_t value);
  public:

  // int64 device_temp_memory = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_temp_memory();
  PROTOBUF_DEPRECATED int64_t device_temp_memory() const;
  PROTOBUF_DEPRECATED void set_device_temp_memory(int64_t value);
  private:
  int64_t _internal_device_temp_memory() const;
  void _internal_set_device_temp_memory(int64_t value);
  public:

  // int64 persistent_memory = 4;
  void clear_persistent_memory();
  int64_t persistent_memory() const;
  void set_persistent_memory(int64_t value);
  private:
  int64_t _internal_persistent_memory() const;
  void _internal_set_persistent_memory(int64_t value);
  public:

  // int64 device_persistent_memory = 5 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_persistent_memory();
  PROTOBUF_DEPRECATED int64_t device_persistent_memory() const;
  PROTOBUF_DEPRECATED void set_device_persistent_memory(int64_t value);
  private:
  int64_t _internal_device_persistent_memory() const;
  void _internal_set_device_persistent_memory(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformance.OpMemory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > output_memory_;
    mutable std::atomic<int> _output_memory_cached_byte_size_;
    int64_t temp_memory_;
    int64_t device_temp_memory_;
    int64_t persistent_memory_;
    int64_t device_persistent_memory_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpPerformance final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformance) */ {
 public:
  inline OpPerformance() : OpPerformance(nullptr) {}
  ~OpPerformance() override;
  explicit PROTOBUF_CONSTEXPR OpPerformance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpPerformance(const OpPerformance& from);
  OpPerformance(OpPerformance&& from) noexcept
    : OpPerformance() {
    *this = ::std::move(from);
  }

  inline OpPerformance& operator=(const OpPerformance& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpPerformance& operator=(OpPerformance&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpPerformance& default_instance() {
    return *internal_default_instance();
  }
  enum ExecutionTimeCase {
    kExecutionTimeNormal = 10,
    kExecutionTimeLogNormal = 11,
    EXECUTION_TIME_NOT_SET = 0,
  };

  static inline const OpPerformance* internal_default_instance() {
    return reinterpret_cast<const OpPerformance*>(
               &_OpPerformance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(OpPerformance& a, OpPerformance& b) {
    a.Swap(&b);
  }
  inline void Swap(OpPerformance* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpPerformance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpPerformance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpPerformance>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpPerformance& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpPerformance& from) {
    OpPerformance::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformance* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpPerformance";
  }
  protected:
  explicit OpPerformance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpPerformance_OpMemory OpMemory;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 5,
    kOpFieldNumber = 1,
    kOpMemoryFieldNumber = 9,
    kSessionInfoFieldNumber = 12,
    kTemporaryMemorySizeFieldNumber = 2,
    kComputeCostFieldNumber = 3,
    kComputeEfficiencyFieldNumber = 4,
    kComputeTimeFieldNumber = 6,
    kMemoryTimeFieldNumber = 7,
    kMemoryEfficiencyFieldNumber = 8,
    kExecutionTimeNormalFieldNumber = 10,
    kExecutionTimeLogNormalFieldNumber = 11,
  };
  // string node = 5;
  void clear_node();
  const std::string& node() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node();
  PROTOBUF_NODISCARD std::string* release_node();
  void set_allocated_node(std::string* node);
  private:
  const std::string& _internal_node() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node(const std::string& value);
  std::string* _internal_mutable_node();
  public:

  // .tensorflow.OpInfo op = 1;
  bool has_op() const;
  private:
  bool _internal_has_op() const;
  public:
  void clear_op();
  const ::tensorflow::OpInfo& op() const;
  PROTOBUF_NODISCARD ::tensorflow::OpInfo* release_op();
  ::tensorflow::OpInfo* mutable_op();
  void set_allocated_op(::tensorflow::OpInfo* op);
  private:
  const ::tensorflow::OpInfo& _internal_op() const;
  ::tensorflow::OpInfo* _internal_mutable_op();
  public:
  void unsafe_arena_set_allocated_op(
      ::tensorflow::OpInfo* op);
  ::tensorflow::OpInfo* unsafe_arena_release_op();

  // .tensorflow.OpPerformance.OpMemory op_memory = 9;
  bool has_op_memory() const;
  private:
  bool _internal_has_op_memory() const;
  public:
  void clear_op_memory();
  const ::tensorflow::OpPerformance_OpMemory& op_memory() const;
  PROTOBUF_NODISCARD ::tensorflow::OpPerformance_OpMemory* release_op_memory();
  ::tensorflow::OpPerformance_OpMemory* mutable_op_memory();
  void set_allocated_op_memory(::tensorflow::OpPerformance_OpMemory* op_memory);
  private:
  const ::tensorflow::OpPerformance_OpMemory& _internal_op_memory() const;
  ::tensorflow::OpPerformance_OpMemory* _internal_mutable_op_memory();
  public:
  void unsafe_arena_set_allocated_op_memory(
      ::tensorflow::OpPerformance_OpMemory* op_memory);
  ::tensorflow::OpPerformance_OpMemory* unsafe_arena_release_op_memory();

  // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_session_info() const;
  private:
  bool _internal_has_session_info() const;
  public:
  PROTOBUF_DEPRECATED void clear_session_info();
  PROTOBUF_DEPRECATED const ::tensorflow::SessionInfo& session_info() const;
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED ::tensorflow::SessionInfo* release_session_info();
  PROTOBUF_DEPRECATED ::tensorflow::SessionInfo* mutable_session_info();
  PROTOBUF_DEPRECATED void set_allocated_session_info(::tensorflow::SessionInfo* session_info);
  private:
  const ::tensorflow::SessionInfo& _internal_session_info() const;
  ::tensorflow::SessionInfo* _internal_mutable_session_info();
  public:
  PROTOBUF_DEPRECATED void unsafe_arena_set_allocated_session_info(
      ::tensorflow::SessionInfo* session_info);
  PROTOBUF_DEPRECATED ::tensorflow::SessionInfo* unsafe_arena_release_session_info();

  // int64 temporary_memory_size = 2;
  void clear_temporary_memory_size();
  int64_t temporary_memory_size() const;
  void set_temporary_memory_size(int64_t value);
  private:
  int64_t _internal_temporary_memory_size() const;
  void _internal_set_temporary_memory_size(int64_t value);
  public:

  // int64 compute_cost = 3;
  void clear_compute_cost();
  int64_t compute_cost() const;
  void set_compute_cost(int64_t value);
  private:
  int64_t _internal_compute_cost() const;
  void _internal_set_compute_cost(int64_t value);
  public:

  // double compute_efficiency = 4;
  void clear_compute_efficiency();
  double compute_efficiency() const;
  void set_compute_efficiency(double value);
  private:
  double _internal_compute_efficiency() const;
  void _internal_set_compute_efficiency(double value);
  public:

  // int64 compute_time = 6;
  void clear_compute_time();
  int64_t compute_time() const;
  void set_compute_time(int64_t value);
  private:
  int64_t _internal_compute_time() const;
  void _internal_set_compute_time(int64_t value);
  public:

  // int64 memory_time = 7;
  void clear_memory_time();
  int64_t memory_time() const;
  void set_memory_time(int64_t value);
  private:
  int64_t _internal_memory_time() const;
  void _internal_set_memory_time(int64_t value);
  public:

  // double memory_efficiency = 8;
  void clear_memory_efficiency();
  double memory_efficiency() const;
  void set_memory_efficiency(double value);
  private:
  double _internal_memory_efficiency() const;
  void _internal_set_memory_efficiency(double value);
  public:

  // .tensorflow.NormalDistribution execution_time_normal = 10;
  bool has_execution_time_normal() const;
  private:
  bool _internal_has_execution_time_normal() const;
  public:
  void clear_execution_time_normal();
  const ::tensorflow::NormalDistribution& execution_time_normal() const;
  PROTOBUF_NODISCARD ::tensorflow::NormalDistribution* release_execution_time_normal();
  ::tensorflow::NormalDistribution* mutable_execution_time_normal();
  void set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal);
  private:
  const ::tensorflow::NormalDistribution& _internal_execution_time_normal() const;
  ::tensorflow::NormalDistribution* _internal_mutable_execution_time_normal();
  public:
  void unsafe_arena_set_allocated_execution_time_normal(
      ::tensorflow::NormalDistribution* execution_time_normal);
  ::tensorflow::NormalDistribution* unsafe_arena_release_execution_time_normal();

  // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
  bool has_execution_time_log_normal() const;
  private:
  bool _internal_has_execution_time_log_normal() const;
  public:
  void clear_execution_time_log_normal();
  const ::tensorflow::LogNormalDistribution& execution_time_log_normal() const;
  PROTOBUF_NODISCARD ::tensorflow::LogNormalDistribution* release_execution_time_log_normal();
  ::tensorflow::LogNormalDistribution* mutable_execution_time_log_normal();
  void set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal);
  private:
  const ::tensorflow::LogNormalDistribution& _internal_execution_time_log_normal() const;
  ::tensorflow::LogNormalDistribution* _internal_mutable_execution_time_log_normal();
  public:
  void unsafe_arena_set_allocated_execution_time_log_normal(
      ::tensorflow::LogNormalDistribution* execution_time_log_normal);
  ::tensorflow::LogNormalDistribution* unsafe_arena_release_execution_time_log_normal();

  void clear_execution_time();
  ExecutionTimeCase execution_time_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformance)
 private:
  class _Internal;
  void set_has_execution_time_normal();
  void set_has_execution_time_log_normal();

  inline bool has_execution_time() const;
  inline void clear_has_execution_time();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_;
    ::tensorflow::OpInfo* op_;
    ::tensorflow::OpPerformance_OpMemory* op_memory_;
    ::tensorflow::SessionInfo* session_info_;
    int64_t temporary_memory_size_;
    int64_t compute_cost_;
    double compute_efficiency_;
    int64_t compute_time_;
    int64_t memory_time_;
    double memory_efficiency_;
    union ExecutionTimeUnion {
      constexpr ExecutionTimeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::NormalDistribution* execution_time_normal_;
      ::tensorflow::LogNormalDistribution* execution_time_log_normal_;
    } execution_time_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpPerformanceList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformanceList) */ {
 public:
  inline OpPerformanceList() : OpPerformanceList(nullptr) {}
  ~OpPerformanceList() override;
  explicit PROTOBUF_CONSTEXPR OpPerformanceList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpPerformanceList(const OpPerformanceList& from);
  OpPerformanceList(OpPerformanceList&& from) noexcept
    : OpPerformanceList() {
    *this = ::std::move(from);
  }

  inline OpPerformanceList& operator=(const OpPerformanceList& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpPerformanceList& operator=(OpPerformanceList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpPerformanceList& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpPerformanceList* internal_default_instance() {
    return reinterpret_cast<const OpPerformanceList*>(
               &_OpPerformanceList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(OpPerformanceList& a, OpPerformanceList& b) {
    a.Swap(&b);
  }
  inline void Swap(OpPerformanceList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpPerformanceList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpPerformanceList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpPerformanceList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpPerformanceList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpPerformanceList& from) {
    OpPerformanceList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformanceList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpPerformanceList";
  }
  protected:
  explicit OpPerformanceList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpPerformanceFieldNumber = 1,
  };
  // repeated .tensorflow.OpPerformance op_performance = 1;
  int op_performance_size() const;
  private:
  int _internal_op_performance_size() const;
  public:
  void clear_op_performance();
  ::tensorflow::OpPerformance* mutable_op_performance(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >*
      mutable_op_performance();
  private:
  const ::tensorflow::OpPerformance& _internal_op_performance(int index) const;
  ::tensorflow::OpPerformance* _internal_add_op_performance();
  public:
  const ::tensorflow::OpPerformance& op_performance(int index) const;
  ::tensorflow::OpPerformance* add_op_performance();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >&
      op_performance() const;

  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformanceList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance > op_performance_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SessionInfo

// int64 intra_op_parallelism = 1;
inline void SessionInfo::clear_intra_op_parallelism() {
  _impl_.intra_op_parallelism_ = int64_t{0};
}
inline int64_t SessionInfo::_internal_intra_op_parallelism() const {
  return _impl_.intra_op_parallelism_;
}
inline int64_t SessionInfo::intra_op_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionInfo.intra_op_parallelism)
  return _internal_intra_op_parallelism();
}
inline void SessionInfo::_internal_set_intra_op_parallelism(int64_t value) {
  
  _impl_.intra_op_parallelism_ = value;
}
inline void SessionInfo::set_intra_op_parallelism(int64_t value) {
  _internal_set_intra_op_parallelism(value);
  // @@protoc_insertion_point(field_set:tensorflow.SessionInfo.intra_op_parallelism)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// OpInfo_TensorProperties

// .tensorflow.DataType dtype = 1;
inline void OpInfo_TensorProperties::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType OpInfo_TensorProperties::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType OpInfo_TensorProperties::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.dtype)
  return _internal_dtype();
}
inline void OpInfo_TensorProperties::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void OpInfo_TensorProperties::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpInfo.TensorProperties.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool OpInfo_TensorProperties::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool OpInfo_TensorProperties::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& OpInfo_TensorProperties::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& OpInfo_TensorProperties::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.shape)
  return _internal_shape();
}
inline void OpInfo_TensorProperties::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.TensorProperties.shape)
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.TensorProperties.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.TensorProperties.shape)
  return _msg;
}
inline void OpInfo_TensorProperties::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.TensorProperties.shape)
}

// .tensorflow.TensorProto value = 3;
inline bool OpInfo_TensorProperties::_internal_has_value() const {
  return this != internal_default_instance() && _impl_.value_ != nullptr;
}
inline bool OpInfo_TensorProperties::has_value() const {
  return _internal_has_value();
}
inline const ::tensorflow::TensorProto& OpInfo_TensorProperties::_internal_value() const {
  const ::tensorflow::TensorProto* p = _impl_.value_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& OpInfo_TensorProperties::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.value)
  return _internal_value();
}
inline void OpInfo_TensorProperties::unsafe_arena_set_allocated_value(
    ::tensorflow::TensorProto* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.value_);
  }
  _impl_.value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.TensorProperties.value)
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::release_value() {
  
  ::tensorflow::TensorProto* temp = _impl_.value_;
  _impl_.value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.TensorProperties.value)
  
  ::tensorflow::TensorProto* temp = _impl_.value_;
  _impl_.value_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::_internal_mutable_value() {
  
  if (_impl_.value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.value_ = p;
  }
  return _impl_.value_;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::mutable_value() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.TensorProperties.value)
  return _msg;
}
inline void OpInfo_TensorProperties::set_allocated_value(::tensorflow::TensorProto* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.value_ = value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.TensorProperties.value)
}

// -------------------------------------------------------------------

// OpInfo

// string op = 1;
inline void OpInfo::clear_op() {
  _impl_.op_.ClearToEmpty();
}
inline const std::string& OpInfo::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.op)
  return _internal_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpInfo::set_op(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpInfo.op)
}
inline std::string* OpInfo::mutable_op() {
  std::string* _s = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.op)
  return _s;
}
inline const std::string& OpInfo::_internal_op() const {
  return _impl_.op_.Get();
}
inline void OpInfo::_internal_set_op(const std::string& value) {
  
  _impl_.op_.Set(value, GetArenaForAllocation());
}
inline std::string* OpInfo::_internal_mutable_op() {
  
  return _impl_.op_.Mutable(GetArenaForAllocation());
}
inline std::string* OpInfo::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.op)
  return _impl_.op_.Release();
}
inline void OpInfo::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  _impl_.op_.SetAllocated(op, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_.IsDefault()) {
    _impl_.op_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.op)
}

// map<string, .tensorflow.AttrValue> attr = 2;
inline int OpInfo::_internal_attr_size() const {
  return _impl_.attr_.size();
}
inline int OpInfo::attr_size() const {
  return _internal_attr_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
OpInfo::_internal_attr() const {
  return _impl_.attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
OpInfo::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.OpInfo.attr)
  return _internal_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
OpInfo::_internal_mutable_attr() {
  return _impl_.attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
OpInfo::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.OpInfo.attr)
  return _internal_mutable_attr();
}

// repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
inline int OpInfo::_internal_inputs_size() const {
  return _impl_.inputs_.size();
}
inline int OpInfo::inputs_size() const {
  return _internal_inputs_size();
}
inline void OpInfo::clear_inputs() {
  _impl_.inputs_.Clear();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::mutable_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.inputs)
  return _impl_.inputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
OpInfo::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpInfo.inputs)
  return &_impl_.inputs_;
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::_internal_inputs(int index) const {
  return _impl_.inputs_.Get(index);
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.inputs)
  return _internal_inputs(index);
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::_internal_add_inputs() {
  return _impl_.inputs_.Add();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::add_inputs() {
  ::tensorflow::OpInfo_TensorProperties* _add = _internal_add_inputs();
  // @@protoc_insertion_point(field_add:tensorflow.OpInfo.inputs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
OpInfo::inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpInfo.inputs)
  return _impl_.inputs_;
}

// repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
inline int OpInfo::_internal_outputs_size() const {
  return _impl_.outputs_.size();
}
inline int OpInfo::outputs_size() const {
  return _internal_outputs_size();
}
inline void OpInfo::clear_outputs() {
  _impl_.outputs_.Clear();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::mutable_outputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.outputs)
  return _impl_.outputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
OpInfo::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpInfo.outputs)
  return &_impl_.outputs_;
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::_internal_outputs(int index) const {
  return _impl_.outputs_.Get(index);
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::outputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.outputs)
  return _internal_outputs(index);
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::_internal_add_outputs() {
  return _impl_.outputs_.Add();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::add_outputs() {
  ::tensorflow::OpInfo_TensorProperties* _add = _internal_add_outputs();
  // @@protoc_insertion_point(field_add:tensorflow.OpInfo.outputs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
OpInfo::outputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpInfo.outputs)
  return _impl_.outputs_;
}

// .tensorflow.DeviceProperties device = 4;
inline bool OpInfo::_internal_has_device() const {
  return this != internal_default_instance() && _impl_.device_ != nullptr;
}
inline bool OpInfo::has_device() const {
  return _internal_has_device();
}
inline const ::tensorflow::DeviceProperties& OpInfo::_internal_device() const {
  const ::tensorflow::DeviceProperties* p = _impl_.device_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceProperties&>(
      ::tensorflow::_DeviceProperties_default_instance_);
}
inline const ::tensorflow::DeviceProperties& OpInfo::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.device)
  return _internal_device();
}
inline void OpInfo::unsafe_arena_set_allocated_device(
    ::tensorflow::DeviceProperties* device) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_);
  }
  _impl_.device_ = device;
  if (device) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.device)
}
inline ::tensorflow::DeviceProperties* OpInfo::release_device() {
  
  ::tensorflow::DeviceProperties* temp = _impl_.device_;
  _impl_.device_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceProperties* OpInfo::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.device)
  
  ::tensorflow::DeviceProperties* temp = _impl_.device_;
  _impl_.device_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceProperties* OpInfo::_internal_mutable_device() {
  
  if (_impl_.device_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceProperties>(GetArenaForAllocation());
    _impl_.device_ = p;
  }
  return _impl_.device_;
}
inline ::tensorflow::DeviceProperties* OpInfo::mutable_device() {
  ::tensorflow::DeviceProperties* _msg = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.device)
  return _msg;
}
inline void OpInfo::set_allocated_device(::tensorflow::DeviceProperties* device) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_);
  }
  if (device) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device));
    if (message_arena != submessage_arena) {
      device = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.device_ = device;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.device)
}

// .tensorflow.SessionInfo session_info = 6;
inline bool OpInfo::_internal_has_session_info() const {
  return this != internal_default_instance() && _impl_.session_info_ != nullptr;
}
inline bool OpInfo::has_session_info() const {
  return _internal_has_session_info();
}
inline void OpInfo::clear_session_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.session_info_ != nullptr) {
    delete _impl_.session_info_;
  }
  _impl_.session_info_ = nullptr;
}
inline const ::tensorflow::SessionInfo& OpInfo::_internal_session_info() const {
  const ::tensorflow::SessionInfo* p = _impl_.session_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SessionInfo&>(
      ::tensorflow::_SessionInfo_default_instance_);
}
inline const ::tensorflow::SessionInfo& OpInfo::session_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.session_info)
  return _internal_session_info();
}
inline void OpInfo::unsafe_arena_set_allocated_session_info(
    ::tensorflow::SessionInfo* session_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.session_info_);
  }
  _impl_.session_info_ = session_info;
  if (session_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.session_info)
}
inline ::tensorflow::SessionInfo* OpInfo::release_session_info() {
  
  ::tensorflow::SessionInfo* temp = _impl_.session_info_;
  _impl_.session_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SessionInfo* OpInfo::unsafe_arena_release_session_info() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.session_info)
  
  ::tensorflow::SessionInfo* temp = _impl_.session_info_;
  _impl_.session_info_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionInfo* OpInfo::_internal_mutable_session_info() {
  
  if (_impl_.session_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionInfo>(GetArenaForAllocation());
    _impl_.session_info_ = p;
  }
  return _impl_.session_info_;
}
inline ::tensorflow::SessionInfo* OpInfo::mutable_session_info() {
  ::tensorflow::SessionInfo* _msg = _internal_mutable_session_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.session_info)
  return _msg;
}
inline void OpInfo::set_allocated_session_info(::tensorflow::SessionInfo* session_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.session_info_;
  }
  if (session_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(session_info);
    if (message_arena != submessage_arena) {
      session_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.session_info_ = session_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.session_info)
}

// -------------------------------------------------------------------

// NormalDistribution

// double mu = 1;
inline void NormalDistribution::clear_mu() {
  _impl_.mu_ = 0;
}
inline double NormalDistribution::_internal_mu() const {
  return _impl_.mu_;
}
inline double NormalDistribution::mu() const {
  // @@protoc_insertion_point(field_get:tensorflow.NormalDistribution.mu)
  return _internal_mu();
}
inline void NormalDistribution::_internal_set_mu(double value) {
  
  _impl_.mu_ = value;
}
inline void NormalDistribution::set_mu(double value) {
  _internal_set_mu(value);
  // @@protoc_insertion_point(field_set:tensorflow.NormalDistribution.mu)
}

// double sigma = 2;
inline void NormalDistribution::clear_sigma() {
  _impl_.sigma_ = 0;
}
inline double NormalDistribution::_internal_sigma() const {
  return _impl_.sigma_;
}
inline double NormalDistribution::sigma() const {
  // @@protoc_insertion_point(field_get:tensorflow.NormalDistribution.sigma)
  return _internal_sigma();
}
inline void NormalDistribution::_internal_set_sigma(double value) {
  
  _impl_.sigma_ = value;
}
inline void NormalDistribution::set_sigma(double value) {
  _internal_set_sigma(value);
  // @@protoc_insertion_point(field_set:tensorflow.NormalDistribution.sigma)
}

// -------------------------------------------------------------------

// LogNormalDistribution

// double mu = 1;
inline void LogNormalDistribution::clear_mu() {
  _impl_.mu_ = 0;
}
inline double LogNormalDistribution::_internal_mu() const {
  return _impl_.mu_;
}
inline double LogNormalDistribution::mu() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogNormalDistribution.mu)
  return _internal_mu();
}
inline void LogNormalDistribution::_internal_set_mu(double value) {
  
  _impl_.mu_ = value;
}
inline void LogNormalDistribution::set_mu(double value) {
  _internal_set_mu(value);
  // @@protoc_insertion_point(field_set:tensorflow.LogNormalDistribution.mu)
}

// double sigma = 2;
inline void LogNormalDistribution::clear_sigma() {
  _impl_.sigma_ = 0;
}
inline double LogNormalDistribution::_internal_sigma() const {
  return _impl_.sigma_;
}
inline double LogNormalDistribution::sigma() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogNormalDistribution.sigma)
  return _internal_sigma();
}
inline void LogNormalDistribution::_internal_set_sigma(double value) {
  
  _impl_.sigma_ = value;
}
inline void LogNormalDistribution::set_sigma(double value) {
  _internal_set_sigma(value);
  // @@protoc_insertion_point(field_set:tensorflow.LogNormalDistribution.sigma)
}

// -------------------------------------------------------------------

// OpPerformance_OpMemory

// repeated int64 output_memory = 1;
inline int OpPerformance_OpMemory::_internal_output_memory_size() const {
  return _impl_.output_memory_.size();
}
inline int OpPerformance_OpMemory::output_memory_size() const {
  return _internal_output_memory_size();
}
inline void OpPerformance_OpMemory::clear_output_memory() {
  _impl_.output_memory_.Clear();
}
inline int64_t OpPerformance_OpMemory::_internal_output_memory(int index) const {
  return _impl_.output_memory_.Get(index);
}
inline int64_t OpPerformance_OpMemory::output_memory(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.output_memory)
  return _internal_output_memory(index);
}
inline void OpPerformance_OpMemory::set_output_memory(int index, int64_t value) {
  _impl_.output_memory_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.output_memory)
}
inline void OpPerformance_OpMemory::_internal_add_output_memory(int64_t value) {
  _impl_.output_memory_.Add(value);
}
inline void OpPerformance_OpMemory::add_output_memory(int64_t value) {
  _internal_add_output_memory(value);
  // @@protoc_insertion_point(field_add:tensorflow.OpPerformance.OpMemory.output_memory)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
OpPerformance_OpMemory::_internal_output_memory() const {
  return _impl_.output_memory_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
OpPerformance_OpMemory::output_memory() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpPerformance.OpMemory.output_memory)
  return _internal_output_memory();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
OpPerformance_OpMemory::_internal_mutable_output_memory() {
  return &_impl_.output_memory_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
OpPerformance_OpMemory::mutable_output_memory() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpPerformance.OpMemory.output_memory)
  return _internal_mutable_output_memory();
}

// int64 temp_memory = 2;
inline void OpPerformance_OpMemory::clear_temp_memory() {
  _impl_.temp_memory_ = int64_t{0};
}
inline int64_t OpPerformance_OpMemory::_internal_temp_memory() const {
  return _impl_.temp_memory_;
}
inline int64_t OpPerformance_OpMemory::temp_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.temp_memory)
  return _internal_temp_memory();
}
inline void OpPerformance_OpMemory::_internal_set_temp_memory(int64_t value) {
  
  _impl_.temp_memory_ = value;
}
inline void OpPerformance_OpMemory::set_temp_memory(int64_t value) {
  _internal_set_temp_memory(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.temp_memory)
}

// int64 persistent_memory = 4;
inline void OpPerformance_OpMemory::clear_persistent_memory() {
  _impl_.persistent_memory_ = int64_t{0};
}
inline int64_t OpPerformance_OpMemory::_internal_persistent_memory() const {
  return _impl_.persistent_memory_;
}
inline int64_t OpPerformance_OpMemory::persistent_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.persistent_memory)
  return _internal_persistent_memory();
}
inline void OpPerformance_OpMemory::_internal_set_persistent_memory(int64_t value) {
  
  _impl_.persistent_memory_ = value;
}
inline void OpPerformance_OpMemory::set_persistent_memory(int64_t value) {
  _internal_set_persistent_memory(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.persistent_memory)
}

// int64 device_temp_memory = 3 [deprecated = true];
inline void OpPerformance_OpMemory::clear_device_temp_memory() {
  _impl_.device_temp_memory_ = int64_t{0};
}
inline int64_t OpPerformance_OpMemory::_internal_device_temp_memory() const {
  return _impl_.device_temp_memory_;
}
inline int64_t OpPerformance_OpMemory::device_temp_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.device_temp_memory)
  return _internal_device_temp_memory();
}
inline void OpPerformance_OpMemory::_internal_set_device_temp_memory(int64_t value) {
  
  _impl_.device_temp_memory_ = value;
}
inline void OpPerformance_OpMemory::set_device_temp_memory(int64_t value) {
  _internal_set_device_temp_memory(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.device_temp_memory)
}

// int64 device_persistent_memory = 5 [deprecated = true];
inline void OpPerformance_OpMemory::clear_device_persistent_memory() {
  _impl_.device_persistent_memory_ = int64_t{0};
}
inline int64_t OpPerformance_OpMemory::_internal_device_persistent_memory() const {
  return _impl_.device_persistent_memory_;
}
inline int64_t OpPerformance_OpMemory::device_persistent_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.device_persistent_memory)
  return _internal_device_persistent_memory();
}
inline void OpPerformance_OpMemory::_internal_set_device_persistent_memory(int64_t value) {
  
  _impl_.device_persistent_memory_ = value;
}
inline void OpPerformance_OpMemory::set_device_persistent_memory(int64_t value) {
  _internal_set_device_persistent_memory(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.device_persistent_memory)
}

// -------------------------------------------------------------------

// OpPerformance

// .tensorflow.OpInfo op = 1;
inline bool OpPerformance::_internal_has_op() const {
  return this != internal_default_instance() && _impl_.op_ != nullptr;
}
inline bool OpPerformance::has_op() const {
  return _internal_has_op();
}
inline void OpPerformance::clear_op() {
  if (GetArenaForAllocation() == nullptr && _impl_.op_ != nullptr) {
    delete _impl_.op_;
  }
  _impl_.op_ = nullptr;
}
inline const ::tensorflow::OpInfo& OpPerformance::_internal_op() const {
  const ::tensorflow::OpInfo* p = _impl_.op_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::OpInfo&>(
      ::tensorflow::_OpInfo_default_instance_);
}
inline const ::tensorflow::OpInfo& OpPerformance::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.op)
  return _internal_op();
}
inline void OpPerformance::unsafe_arena_set_allocated_op(
    ::tensorflow::OpInfo* op) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.op_);
  }
  _impl_.op_ = op;
  if (op) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.op)
}
inline ::tensorflow::OpInfo* OpPerformance::release_op() {
  
  ::tensorflow::OpInfo* temp = _impl_.op_;
  _impl_.op_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::OpInfo* OpPerformance::unsafe_arena_release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.op)
  
  ::tensorflow::OpInfo* temp = _impl_.op_;
  _impl_.op_ = nullptr;
  return temp;
}
inline ::tensorflow::OpInfo* OpPerformance::_internal_mutable_op() {
  
  if (_impl_.op_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpInfo>(GetArenaForAllocation());
    _impl_.op_ = p;
  }
  return _impl_.op_;
}
inline ::tensorflow::OpInfo* OpPerformance::mutable_op() {
  ::tensorflow::OpInfo* _msg = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.op)
  return _msg;
}
inline void OpPerformance::set_allocated_op(::tensorflow::OpInfo* op) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.op_;
  }
  if (op) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(op);
    if (message_arena != submessage_arena) {
      op = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, op, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.op_ = op;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.op)
}

// .tensorflow.SessionInfo session_info = 12 [deprecated = true];
inline bool OpPerformance::_internal_has_session_info() const {
  return this != internal_default_instance() && _impl_.session_info_ != nullptr;
}
inline bool OpPerformance::has_session_info() const {
  return _internal_has_session_info();
}
inline void OpPerformance::clear_session_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.session_info_ != nullptr) {
    delete _impl_.session_info_;
  }
  _impl_.session_info_ = nullptr;
}
inline const ::tensorflow::SessionInfo& OpPerformance::_internal_session_info() const {
  const ::tensorflow::SessionInfo* p = _impl_.session_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SessionInfo&>(
      ::tensorflow::_SessionInfo_default_instance_);
}
inline const ::tensorflow::SessionInfo& OpPerformance::session_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.session_info)
  return _internal_session_info();
}
inline void OpPerformance::unsafe_arena_set_allocated_session_info(
    ::tensorflow::SessionInfo* session_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.session_info_);
  }
  _impl_.session_info_ = session_info;
  if (session_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.session_info)
}
inline ::tensorflow::SessionInfo* OpPerformance::release_session_info() {
  
  ::tensorflow::SessionInfo* temp = _impl_.session_info_;
  _impl_.session_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SessionInfo* OpPerformance::unsafe_arena_release_session_info() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.session_info)
  
  ::tensorflow::SessionInfo* temp = _impl_.session_info_;
  _impl_.session_info_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionInfo* OpPerformance::_internal_mutable_session_info() {
  
  if (_impl_.session_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionInfo>(GetArenaForAllocation());
    _impl_.session_info_ = p;
  }
  return _impl_.session_info_;
}
inline ::tensorflow::SessionInfo* OpPerformance::mutable_session_info() {
  ::tensorflow::SessionInfo* _msg = _internal_mutable_session_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.session_info)
  return _msg;
}
inline void OpPerformance::set_allocated_session_info(::tensorflow::SessionInfo* session_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.session_info_;
  }
  if (session_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(session_info);
    if (message_arena != submessage_arena) {
      session_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.session_info_ = session_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.session_info)
}

// string node = 5;
inline void OpPerformance::clear_node() {
  _impl_.node_.ClearToEmpty();
}
inline const std::string& OpPerformance::node() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.node)
  return _internal_node();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpPerformance::set_node(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.node)
}
inline std::string* OpPerformance::mutable_node() {
  std::string* _s = _internal_mutable_node();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.node)
  return _s;
}
inline const std::string& OpPerformance::_internal_node() const {
  return _impl_.node_.Get();
}
inline void OpPerformance::_internal_set_node(const std::string& value) {
  
  _impl_.node_.Set(value, GetArenaForAllocation());
}
inline std::string* OpPerformance::_internal_mutable_node() {
  
  return _impl_.node_.Mutable(GetArenaForAllocation());
}
inline std::string* OpPerformance::release_node() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.node)
  return _impl_.node_.Release();
}
inline void OpPerformance::set_allocated_node(std::string* node) {
  if (node != nullptr) {
    
  } else {
    
  }
  _impl_.node_.SetAllocated(node, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_.IsDefault()) {
    _impl_.node_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.node)
}

// int64 temporary_memory_size = 2;
inline void OpPerformance::clear_temporary_memory_size() {
  _impl_.temporary_memory_size_ = int64_t{0};
}
inline int64_t OpPerformance::_internal_temporary_memory_size() const {
  return _impl_.temporary_memory_size_;
}
inline int64_t OpPerformance::temporary_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.temporary_memory_size)
  return _internal_temporary_memory_size();
}
inline void OpPerformance::_internal_set_temporary_memory_size(int64_t value) {
  
  _impl_.temporary_memory_size_ = value;
}
inline void OpPerformance::set_temporary_memory_size(int64_t value) {
  _internal_set_temporary_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.temporary_memory_size)
}

// int64 compute_cost = 3;
inline void OpPerformance::clear_compute_cost() {
  _impl_.compute_cost_ = int64_t{0};
}
inline int64_t OpPerformance::_internal_compute_cost() const {
  return _impl_.compute_cost_;
}
inline int64_t OpPerformance::compute_cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_cost)
  return _internal_compute_cost();
}
inline void OpPerformance::_internal_set_compute_cost(int64_t value) {
  
  _impl_.compute_cost_ = value;
}
inline void OpPerformance::set_compute_cost(int64_t value) {
  _internal_set_compute_cost(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_cost)
}

// int64 compute_time = 6;
inline void OpPerformance::clear_compute_time() {
  _impl_.compute_time_ = int64_t{0};
}
inline int64_t OpPerformance::_internal_compute_time() const {
  return _impl_.compute_time_;
}
inline int64_t OpPerformance::compute_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_time)
  return _internal_compute_time();
}
inline void OpPerformance::_internal_set_compute_time(int64_t value) {
  
  _impl_.compute_time_ = value;
}
inline void OpPerformance::set_compute_time(int64_t value) {
  _internal_set_compute_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_time)
}

// int64 memory_time = 7;
inline void OpPerformance::clear_memory_time() {
  _impl_.memory_time_ = int64_t{0};
}
inline int64_t OpPerformance::_internal_memory_time() const {
  return _impl_.memory_time_;
}
inline int64_t OpPerformance::memory_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.memory_time)
  return _internal_memory_time();
}
inline void OpPerformance::_internal_set_memory_time(int64_t value) {
  
  _impl_.memory_time_ = value;
}
inline void OpPerformance::set_memory_time(int64_t value) {
  _internal_set_memory_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.memory_time)
}

// double compute_efficiency = 4;
inline void OpPerformance::clear_compute_efficiency() {
  _impl_.compute_efficiency_ = 0;
}
inline double OpPerformance::_internal_compute_efficiency() const {
  return _impl_.compute_efficiency_;
}
inline double OpPerformance::compute_efficiency() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_efficiency)
  return _internal_compute_efficiency();
}
inline void OpPerformance::_internal_set_compute_efficiency(double value) {
  
  _impl_.compute_efficiency_ = value;
}
inline void OpPerformance::set_compute_efficiency(double value) {
  _internal_set_compute_efficiency(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_efficiency)
}

// double memory_efficiency = 8;
inline void OpPerformance::clear_memory_efficiency() {
  _impl_.memory_efficiency_ = 0;
}
inline double OpPerformance::_internal_memory_efficiency() const {
  return _impl_.memory_efficiency_;
}
inline double OpPerformance::memory_efficiency() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.memory_efficiency)
  return _internal_memory_efficiency();
}
inline void OpPerformance::_internal_set_memory_efficiency(double value) {
  
  _impl_.memory_efficiency_ = value;
}
inline void OpPerformance::set_memory_efficiency(double value) {
  _internal_set_memory_efficiency(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.memory_efficiency)
}

// .tensorflow.NormalDistribution execution_time_normal = 10;
inline bool OpPerformance::_internal_has_execution_time_normal() const {
  return execution_time_case() == kExecutionTimeNormal;
}
inline bool OpPerformance::has_execution_time_normal() const {
  return _internal_has_execution_time_normal();
}
inline void OpPerformance::set_has_execution_time_normal() {
  _impl_._oneof_case_[0] = kExecutionTimeNormal;
}
inline void OpPerformance::clear_execution_time_normal() {
  if (_internal_has_execution_time_normal()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.execution_time_.execution_time_normal_;
    }
    clear_has_execution_time();
  }
}
inline ::tensorflow::NormalDistribution* OpPerformance::release_execution_time_normal() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.execution_time_normal)
  if (_internal_has_execution_time_normal()) {
    clear_has_execution_time();
    ::tensorflow::NormalDistribution* temp = _impl_.execution_time_.execution_time_normal_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.execution_time_.execution_time_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NormalDistribution& OpPerformance::_internal_execution_time_normal() const {
  return _internal_has_execution_time_normal()
      ? *_impl_.execution_time_.execution_time_normal_
      : reinterpret_cast< ::tensorflow::NormalDistribution&>(::tensorflow::_NormalDistribution_default_instance_);
}
inline const ::tensorflow::NormalDistribution& OpPerformance::execution_time_normal() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.execution_time_normal)
  return _internal_execution_time_normal();
}
inline ::tensorflow::NormalDistribution* OpPerformance::unsafe_arena_release_execution_time_normal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.execution_time_normal)
  if (_internal_has_execution_time_normal()) {
    clear_has_execution_time();
    ::tensorflow::NormalDistribution* temp = _impl_.execution_time_.execution_time_normal_;
    _impl_.execution_time_.execution_time_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OpPerformance::unsafe_arena_set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal) {
  clear_execution_time();
  if (execution_time_normal) {
    set_has_execution_time_normal();
    _impl_.execution_time_.execution_time_normal_ = execution_time_normal;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.execution_time_normal)
}
inline ::tensorflow::NormalDistribution* OpPerformance::_internal_mutable_execution_time_normal() {
  if (!_internal_has_execution_time_normal()) {
    clear_execution_time();
    set_has_execution_time_normal();
    _impl_.execution_time_.execution_time_normal_ = CreateMaybeMessage< ::tensorflow::NormalDistribution >(GetArenaForAllocation());
  }
  return _impl_.execution_time_.execution_time_normal_;
}
inline ::tensorflow::NormalDistribution* OpPerformance::mutable_execution_time_normal() {
  ::tensorflow::NormalDistribution* _msg = _internal_mutable_execution_time_normal();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.execution_time_normal)
  return _msg;
}

// .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
inline bool OpPerformance::_internal_has_execution_time_log_normal() const {
  return execution_time_case() == kExecutionTimeLogNormal;
}
inline bool OpPerformance::has_execution_time_log_normal() const {
  return _internal_has_execution_time_log_normal();
}
inline void OpPerformance::set_has_execution_time_log_normal() {
  _impl_._oneof_case_[0] = kExecutionTimeLogNormal;
}
inline void OpPerformance::clear_execution_time_log_normal() {
  if (_internal_has_execution_time_log_normal()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.execution_time_.execution_time_log_normal_;
    }
    clear_has_execution_time();
  }
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::release_execution_time_log_normal() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.execution_time_log_normal)
  if (_internal_has_execution_time_log_normal()) {
    clear_has_execution_time();
    ::tensorflow::LogNormalDistribution* temp = _impl_.execution_time_.execution_time_log_normal_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.execution_time_.execution_time_log_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::LogNormalDistribution& OpPerformance::_internal_execution_time_log_normal() const {
  return _internal_has_execution_time_log_normal()
      ? *_impl_.execution_time_.execution_time_log_normal_
      : reinterpret_cast< ::tensorflow::LogNormalDistribution&>(::tensorflow::_LogNormalDistribution_default_instance_);
}
inline const ::tensorflow::LogNormalDistribution& OpPerformance::execution_time_log_normal() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.execution_time_log_normal)
  return _internal_execution_time_log_normal();
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::unsafe_arena_release_execution_time_log_normal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.execution_time_log_normal)
  if (_internal_has_execution_time_log_normal()) {
    clear_has_execution_time();
    ::tensorflow::LogNormalDistribution* temp = _impl_.execution_time_.execution_time_log_normal_;
    _impl_.execution_time_.execution_time_log_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OpPerformance::unsafe_arena_set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal) {
  clear_execution_time();
  if (execution_time_log_normal) {
    set_has_execution_time_log_normal();
    _impl_.execution_time_.execution_time_log_normal_ = execution_time_log_normal;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.execution_time_log_normal)
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::_internal_mutable_execution_time_log_normal() {
  if (!_internal_has_execution_time_log_normal()) {
    clear_execution_time();
    set_has_execution_time_log_normal();
    _impl_.execution_time_.execution_time_log_normal_ = CreateMaybeMessage< ::tensorflow::LogNormalDistribution >(GetArenaForAllocation());
  }
  return _impl_.execution_time_.execution_time_log_normal_;
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::mutable_execution_time_log_normal() {
  ::tensorflow::LogNormalDistribution* _msg = _internal_mutable_execution_time_log_normal();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.execution_time_log_normal)
  return _msg;
}

// .tensorflow.OpPerformance.OpMemory op_memory = 9;
inline bool OpPerformance::_internal_has_op_memory() const {
  return this != internal_default_instance() && _impl_.op_memory_ != nullptr;
}
inline bool OpPerformance::has_op_memory() const {
  return _internal_has_op_memory();
}
inline void OpPerformance::clear_op_memory() {
  if (GetArenaForAllocation() == nullptr && _impl_.op_memory_ != nullptr) {
    delete _impl_.op_memory_;
  }
  _impl_.op_memory_ = nullptr;
}
inline const ::tensorflow::OpPerformance_OpMemory& OpPerformance::_internal_op_memory() const {
  const ::tensorflow::OpPerformance_OpMemory* p = _impl_.op_memory_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::OpPerformance_OpMemory&>(
      ::tensorflow::_OpPerformance_OpMemory_default_instance_);
}
inline const ::tensorflow::OpPerformance_OpMemory& OpPerformance::op_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.op_memory)
  return _internal_op_memory();
}
inline void OpPerformance::unsafe_arena_set_allocated_op_memory(
    ::tensorflow::OpPerformance_OpMemory* op_memory) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.op_memory_);
  }
  _impl_.op_memory_ = op_memory;
  if (op_memory) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.op_memory)
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::release_op_memory() {
  
  ::tensorflow::OpPerformance_OpMemory* temp = _impl_.op_memory_;
  _impl_.op_memory_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::unsafe_arena_release_op_memory() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.op_memory)
  
  ::tensorflow::OpPerformance_OpMemory* temp = _impl_.op_memory_;
  _impl_.op_memory_ = nullptr;
  return temp;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::_internal_mutable_op_memory() {
  
  if (_impl_.op_memory_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpPerformance_OpMemory>(GetArenaForAllocation());
    _impl_.op_memory_ = p;
  }
  return _impl_.op_memory_;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::mutable_op_memory() {
  ::tensorflow::OpPerformance_OpMemory* _msg = _internal_mutable_op_memory();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.op_memory)
  return _msg;
}
inline void OpPerformance::set_allocated_op_memory(::tensorflow::OpPerformance_OpMemory* op_memory) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.op_memory_;
  }
  if (op_memory) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(op_memory);
    if (message_arena != submessage_arena) {
      op_memory = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, op_memory, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.op_memory_ = op_memory;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.op_memory)
}

inline bool OpPerformance::has_execution_time() const {
  return execution_time_case() != EXECUTION_TIME_NOT_SET;
}
inline void OpPerformance::clear_has_execution_time() {
  _impl_._oneof_case_[0] = EXECUTION_TIME_NOT_SET;
}
inline OpPerformance::ExecutionTimeCase OpPerformance::execution_time_case() const {
  return OpPerformance::ExecutionTimeCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// OpPerformanceList

// repeated .tensorflow.OpPerformance op_performance = 1;
inline int OpPerformanceList::_internal_op_performance_size() const {
  return _impl_.op_performance_.size();
}
inline int OpPerformanceList::op_performance_size() const {
  return _internal_op_performance_size();
}
inline void OpPerformanceList::clear_op_performance() {
  _impl_.op_performance_.Clear();
}
inline ::tensorflow::OpPerformance* OpPerformanceList::mutable_op_performance(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformanceList.op_performance)
  return _impl_.op_performance_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >*
OpPerformanceList::mutable_op_performance() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpPerformanceList.op_performance)
  return &_impl_.op_performance_;
}
inline const ::tensorflow::OpPerformance& OpPerformanceList::_internal_op_performance(int index) const {
  return _impl_.op_performance_.Get(index);
}
inline const ::tensorflow::OpPerformance& OpPerformanceList::op_performance(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformanceList.op_performance)
  return _internal_op_performance(index);
}
inline ::tensorflow::OpPerformance* OpPerformanceList::_internal_add_op_performance() {
  return _impl_.op_performance_.Add();
}
inline ::tensorflow::OpPerformance* OpPerformanceList::add_op_performance() {
  ::tensorflow::OpPerformance* _add = _internal_add_op_performance();
  // @@protoc_insertion_point(field_add:tensorflow.OpPerformanceList.op_performance)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >&
OpPerformanceList::op_performance() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpPerformanceList.op_performance)
  return _impl_.op_performance_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
