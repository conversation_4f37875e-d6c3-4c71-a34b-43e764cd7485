//===- TensorEncoding.h - MLIR Tensor Encoding Declarations------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef MLIR_IR_TENSORENCODING_H
#define MLIR_IR_TENSORENCODING_H

#include "mlir/IR/AffineMap.h"
#include "mlir/IR/OpDefinition.h"

//===----------------------------------------------------------------------===//
// Tablegen Type Declarations
//===----------------------------------------------------------------------===//

#include "mlir/IR/TensorEncInterfaces.h.inc"

#endif // MLIR_IR_TENSORENCODING_H
