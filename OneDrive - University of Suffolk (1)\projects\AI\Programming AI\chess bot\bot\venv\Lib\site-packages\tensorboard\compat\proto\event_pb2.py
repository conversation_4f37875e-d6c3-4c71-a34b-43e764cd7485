# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/event.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import summary_pb2 as tensorboard_dot_compat_dot_proto_dot_summary__pb2
try:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard_dot_compat_dot_proto_dot_histogram__pb2
except AttributeError:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard.compat.proto.histogram_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$tensorboard/compat/proto/event.proto\x12\x0btensorboard\x1a&tensorboard/compat/proto/summary.proto\"\xf9\x02\n\x05\x45vent\x12\x11\n\twall_time\x18\x01 \x01(\x01\x12\x0c\n\x04step\x18\x02 \x01(\x03\x12\x16\n\x0c\x66ile_version\x18\x03 \x01(\tH\x00\x12\x13\n\tgraph_def\x18\x04 \x01(\x0cH\x00\x12\'\n\x07summary\x18\x05 \x01(\x0b\x32\x14.tensorboard.SummaryH\x00\x12\x32\n\x0blog_message\x18\x06 \x01(\x0b\x32\x17.tensorboard.LogMessageB\x02\x18\x01H\x00\x12.\n\x0bsession_log\x18\x07 \x01(\x0b\x32\x17.tensorboard.SessionLogH\x00\x12=\n\x13tagged_run_metadata\x18\x08 \x01(\x0b\x32\x1e.tensorboard.TaggedRunMetadataH\x00\x12\x18\n\x0emeta_graph_def\x18\t \x01(\x0cH\x00\x12\x34\n\x0fsource_metadata\x18\n \x01(\x0b\x32\x1b.tensorboard.SourceMetadataB\x06\n\x04what\" \n\x0eSourceMetadata\x12\x0e\n\x06writer\x18\x01 \x01(\t\"\xa2\x01\n\nLogMessage\x12,\n\x05level\x18\x01 \x01(\x0e\x32\x1d.tensorboard.LogMessage.Level\x12\x0f\n\x07message\x18\x02 \x01(\t\"Q\n\x05Level\x12\x0b\n\x07UNKNOWN\x10\x00\x12\r\n\tDEBUGGING\x10\n\x12\x08\n\x04INFO\x10\x14\x12\x08\n\x04WARN\x10\x1e\x12\t\n\x05\x45RROR\x10(\x12\t\n\x05\x46\x41TAL\x10\x32\x1a\x02\x18\x01:\x02\x18\x01\"\xb7\x01\n\nSessionLog\x12\x35\n\x06status\x18\x01 \x01(\x0e\x32%.tensorboard.SessionLog.SessionStatus\x12\x17\n\x0f\x63heckpoint_path\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\"L\n\rSessionStatus\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\t\n\x05START\x10\x01\x12\x08\n\x04STOP\x10\x02\x12\x0e\n\nCHECKPOINT\x10\x03\"6\n\x11TaggedRunMetadata\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x14\n\x0crun_metadata\x18\x02 \x01(\x0c\"$\n\x0eWatchdogConfig\x12\x12\n\ntimeout_ms\x18\x01 \x01(\x03\"&\n\x11RequestedExitCode\x12\x11\n\texit_code\x18\x01 \x01(\x05\"\xb9\x01\n\x16WorkerHeartbeatRequest\x12\x36\n\rshutdown_mode\x18\x01 \x01(\x0e\x32\x1f.tensorboard.WorkerShutdownMode\x12\x34\n\x0fwatchdog_config\x18\x02 \x01(\x0b\x32\x1b.tensorboard.WatchdogConfig\x12\x31\n\texit_code\x18\x03 \x01(\x0b\x32\x1e.tensorboard.RequestedExitCode\"\x85\x01\n\x17WorkerHeartbeatResponse\x12\x30\n\rhealth_status\x18\x01 \x01(\x0e\x32\x19.tensorboard.WorkerHealth\x12&\n\nworker_log\x18\x02 \x03(\x0b\x32\x12.tensorboard.Event\x12\x10\n\x08hostname\x18\x03 \x01(\t*[\n\x0cWorkerHealth\x12\x06\n\x02OK\x10\x00\x12\x1c\n\x18RECEIVED_SHUTDOWN_SIGNAL\x10\x01\x12\x12\n\x0eINTERNAL_ERROR\x10\x02\x12\x11\n\rSHUTTING_DOWN\x10\x03*k\n\x12WorkerShutdownMode\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x12\n\x0eNOT_CONFIGURED\x10\x01\x12\x18\n\x14WAIT_FOR_COORDINATOR\x10\x02\x12\x1a\n\x16SHUTDOWN_AFTER_TIMEOUT\x10\x03\x42p\n\x13org.tensorflow.utilB\x0b\x45ventProtosP\x01ZGgithub.com/tensorflow/tensorflow/tensorflow/go/core/util/event_go_proto\xf8\x01\x01\x62\x06proto3')

_WORKERHEALTH = DESCRIPTOR.enum_types_by_name['WorkerHealth']
WorkerHealth = enum_type_wrapper.EnumTypeWrapper(_WORKERHEALTH)
_WORKERSHUTDOWNMODE = DESCRIPTOR.enum_types_by_name['WorkerShutdownMode']
WorkerShutdownMode = enum_type_wrapper.EnumTypeWrapper(_WORKERSHUTDOWNMODE)
OK = 0
RECEIVED_SHUTDOWN_SIGNAL = 1
INTERNAL_ERROR = 2
SHUTTING_DOWN = 3
DEFAULT = 0
NOT_CONFIGURED = 1
WAIT_FOR_COORDINATOR = 2
SHUTDOWN_AFTER_TIMEOUT = 3


_EVENT = DESCRIPTOR.message_types_by_name['Event']
_SOURCEMETADATA = DESCRIPTOR.message_types_by_name['SourceMetadata']
_LOGMESSAGE = DESCRIPTOR.message_types_by_name['LogMessage']
_SESSIONLOG = DESCRIPTOR.message_types_by_name['SessionLog']
_TAGGEDRUNMETADATA = DESCRIPTOR.message_types_by_name['TaggedRunMetadata']
_WATCHDOGCONFIG = DESCRIPTOR.message_types_by_name['WatchdogConfig']
_REQUESTEDEXITCODE = DESCRIPTOR.message_types_by_name['RequestedExitCode']
_WORKERHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['WorkerHeartbeatRequest']
_WORKERHEARTBEATRESPONSE = DESCRIPTOR.message_types_by_name['WorkerHeartbeatResponse']
_LOGMESSAGE_LEVEL = _LOGMESSAGE.enum_types_by_name['Level']
_SESSIONLOG_SESSIONSTATUS = _SESSIONLOG.enum_types_by_name['SessionStatus']
Event = _reflection.GeneratedProtocolMessageType('Event', (_message.Message,), {
  'DESCRIPTOR' : _EVENT,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.Event)
  })
_sym_db.RegisterMessage(Event)

SourceMetadata = _reflection.GeneratedProtocolMessageType('SourceMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SOURCEMETADATA,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.SourceMetadata)
  })
_sym_db.RegisterMessage(SourceMetadata)

LogMessage = _reflection.GeneratedProtocolMessageType('LogMessage', (_message.Message,), {
  'DESCRIPTOR' : _LOGMESSAGE,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.LogMessage)
  })
_sym_db.RegisterMessage(LogMessage)

SessionLog = _reflection.GeneratedProtocolMessageType('SessionLog', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONLOG,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.SessionLog)
  })
_sym_db.RegisterMessage(SessionLog)

TaggedRunMetadata = _reflection.GeneratedProtocolMessageType('TaggedRunMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TAGGEDRUNMETADATA,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.TaggedRunMetadata)
  })
_sym_db.RegisterMessage(TaggedRunMetadata)

WatchdogConfig = _reflection.GeneratedProtocolMessageType('WatchdogConfig', (_message.Message,), {
  'DESCRIPTOR' : _WATCHDOGCONFIG,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.WatchdogConfig)
  })
_sym_db.RegisterMessage(WatchdogConfig)

RequestedExitCode = _reflection.GeneratedProtocolMessageType('RequestedExitCode', (_message.Message,), {
  'DESCRIPTOR' : _REQUESTEDEXITCODE,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.RequestedExitCode)
  })
_sym_db.RegisterMessage(RequestedExitCode)

WorkerHeartbeatRequest = _reflection.GeneratedProtocolMessageType('WorkerHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _WORKERHEARTBEATREQUEST,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.WorkerHeartbeatRequest)
  })
_sym_db.RegisterMessage(WorkerHeartbeatRequest)

WorkerHeartbeatResponse = _reflection.GeneratedProtocolMessageType('WorkerHeartbeatResponse', (_message.Message,), {
  'DESCRIPTOR' : _WORKERHEARTBEATRESPONSE,
  '__module__' : 'tensorboard.compat.proto.event_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.WorkerHeartbeatResponse)
  })
_sym_db.RegisterMessage(WorkerHeartbeatResponse)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\023org.tensorflow.utilB\013EventProtosP\001ZGgithub.com/tensorflow/tensorflow/tensorflow/go/core/util/event_go_proto\370\001\001'
  _EVENT.fields_by_name['log_message']._options = None
  _EVENT.fields_by_name['log_message']._serialized_options = b'\030\001'
  _LOGMESSAGE_LEVEL._options = None
  _LOGMESSAGE_LEVEL._serialized_options = b'\030\001'
  _LOGMESSAGE._options = None
  _LOGMESSAGE._serialized_options = b'\030\001'
  _WORKERHEALTH._serialized_start=1316
  _WORKERHEALTH._serialized_end=1407
  _WORKERSHUTDOWNMODE._serialized_start=1409
  _WORKERSHUTDOWNMODE._serialized_end=1516
  _EVENT._serialized_start=94
  _EVENT._serialized_end=471
  _SOURCEMETADATA._serialized_start=473
  _SOURCEMETADATA._serialized_end=505
  _LOGMESSAGE._serialized_start=508
  _LOGMESSAGE._serialized_end=670
  _LOGMESSAGE_LEVEL._serialized_start=585
  _LOGMESSAGE_LEVEL._serialized_end=666
  _SESSIONLOG._serialized_start=673
  _SESSIONLOG._serialized_end=856
  _SESSIONLOG_SESSIONSTATUS._serialized_start=780
  _SESSIONLOG_SESSIONSTATUS._serialized_end=856
  _TAGGEDRUNMETADATA._serialized_start=858
  _TAGGEDRUNMETADATA._serialized_end=912
  _WATCHDOGCONFIG._serialized_start=914
  _WATCHDOGCONFIG._serialized_end=950
  _REQUESTEDEXITCODE._serialized_start=952
  _REQUESTEDEXITCODE._serialized_end=990
  _WORKERHEARTBEATREQUEST._serialized_start=993
  _WORKERHEARTBEATREQUEST._serialized_end=1178
  _WORKERHEARTBEATRESPONSE._serialized_start=1181
  _WORKERHEARTBEATRESPONSE._serialized_end=1314
# @@protoc_insertion_point(module_scope)
