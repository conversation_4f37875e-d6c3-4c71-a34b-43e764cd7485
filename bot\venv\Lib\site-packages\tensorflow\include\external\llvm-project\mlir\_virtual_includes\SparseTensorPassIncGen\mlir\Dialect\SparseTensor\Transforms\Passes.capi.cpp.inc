/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// SparseTensor Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterSparseTensorPasses(void) {
  registerSparseTensorPasses();
}

MlirPass mlirCreateSparseTensorPostSparsificationRewrite(void) {
  return wrap(mlir::createPostSparsificationRewritePass().release());
}
void mlirRegisterSparseTensorPostSparsificationRewrite(void) {
  registerPostSparsificationRewrite();
}


MlirPass mlirCreateSparseTensorPreSparsificationRewrite(void) {
  return wrap(mlir::createPreSparsificationRewritePass().release());
}
void mlirRegisterSparseTensorPreSparsificationRewrite(void) {
  registerPreSparsificationRewrite();
}


MlirPass mlirCreateSparseTensorSparseBufferRewrite(void) {
  return wrap(mlir::createSparseBufferRewritePass().release());
}
void mlirRegisterSparseTensorSparseBufferRewrite(void) {
  registerSparseBufferRewrite();
}


MlirPass mlirCreateSparseTensorSparseGPUCodegen(void) {
  return wrap(mlir::createSparseGPUCodegenPass().release());
}
void mlirRegisterSparseTensorSparseGPUCodegen(void) {
  registerSparseGPUCodegen();
}


MlirPass mlirCreateSparseTensorSparseTensorCodegen(void) {
  return wrap(mlir::createSparseTensorCodegenPass().release());
}
void mlirRegisterSparseTensorSparseTensorCodegen(void) {
  registerSparseTensorCodegen();
}


MlirPass mlirCreateSparseTensorSparseTensorConversionPass(void) {
  return wrap(mlir::createSparseTensorConversionPass().release());
}
void mlirRegisterSparseTensorSparseTensorConversionPass(void) {
  registerSparseTensorConversionPass();
}


MlirPass mlirCreateSparseTensorSparseVectorization(void) {
  return wrap(mlir::createSparseVectorizationPass().release());
}
void mlirRegisterSparseTensorSparseVectorization(void) {
  registerSparseVectorization();
}


MlirPass mlirCreateSparseTensorSparsificationPass(void) {
  return wrap(mlir::createSparsificationPass().release());
}
void mlirRegisterSparseTensorSparsificationPass(void) {
  registerSparsificationPass();
}


MlirPass mlirCreateSparseTensorStorageSpecifierToLLVM(void) {
  return wrap(mlir::createStorageSpecifierToLLVMPass().release());
}
void mlirRegisterSparseTensorStorageSpecifierToLLVM(void) {
  registerStorageSpecifierToLLVM();
}

