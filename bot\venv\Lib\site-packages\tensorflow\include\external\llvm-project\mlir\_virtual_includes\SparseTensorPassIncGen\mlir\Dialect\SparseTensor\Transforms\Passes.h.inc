/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_POSTSPARSIFICATIONREWRITE
#define GEN_PASS_DECL_PRESPARSIFICATIONREWRITE
#define GEN_PASS_DECL_SPARSEBUFFERREWRITE
#define GEN_PASS_DECL_SPARSEGPUCODEGEN
#define GEN_PASS_DECL_SPARSETENSORCODEGEN
#define GEN_PASS_DECL_SPARSETENSORCONVERSIONPASS
#define GEN_PASS_DECL_SPARSEVECTORIZATION
#define GEN_PASS_DECL_SPARSIFICATIONPASS
#define GEN_PASS_DECL_STORAGESPECIFIERTOLLVM
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// PostSparsificationRewrite
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_POSTSPARSIFICATIONREWRITE
struct PostSparsificationRewriteOptions {
  bool enableRuntimeLibrary = true;
  bool enableForeach = true;
  bool enableConvert = true;
};
#undef GEN_PASS_DECL_POSTSPARSIFICATIONREWRITE
#endif // GEN_PASS_DECL_POSTSPARSIFICATIONREWRITE
#ifdef GEN_PASS_DEF_POSTSPARSIFICATIONREWRITE
namespace impl {

template <typename DerivedT>
class PostSparsificationRewriteBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PostSparsificationRewriteBase;

  PostSparsificationRewriteBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PostSparsificationRewriteBase(const PostSparsificationRewriteBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("post-sparsification-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "post-sparsification-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Applies sparse tensor rewriting rules after sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PostSparsificationRewrite");
  }
  ::llvm::StringRef getName() const override { return "PostSparsificationRewrite"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PostSparsificationRewriteBase<DerivedT>)

  PostSparsificationRewriteBase(const PostSparsificationRewriteOptions &options) : PostSparsificationRewriteBase() {
    enableRuntimeLibrary = options.enableRuntimeLibrary;
    enableForeach = options.enableForeach;
    enableConvert = options.enableConvert;
  }
protected:
  ::mlir::Pass::Option<bool> enableRuntimeLibrary{*this, "enable-runtime-library", ::llvm::cl::desc("Enable runtime library for manipulating sparse tensors"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableForeach{*this, "enable-foreach", ::llvm::cl::desc("Enable rewriting rules for the foreach operator"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableConvert{*this, "enable-convert", ::llvm::cl::desc("Enable rewriting rules for the convert operator"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_POSTSPARSIFICATIONREWRITE
#endif // GEN_PASS_DEF_POSTSPARSIFICATIONREWRITE

//===----------------------------------------------------------------------===//
// PreSparsificationRewrite
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PRESPARSIFICATIONREWRITE
#undef GEN_PASS_DECL_PRESPARSIFICATIONREWRITE
#endif // GEN_PASS_DECL_PRESPARSIFICATIONREWRITE
#ifdef GEN_PASS_DEF_PRESPARSIFICATIONREWRITE
namespace impl {

template <typename DerivedT>
class PreSparsificationRewriteBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PreSparsificationRewriteBase;

  PreSparsificationRewriteBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PreSparsificationRewriteBase(const PreSparsificationRewriteBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("pre-sparsification-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "pre-sparsification-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Applies sparse tensor rewriting rules prior to sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PreSparsificationRewrite");
  }
  ::llvm::StringRef getName() const override { return "PreSparsificationRewrite"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PreSparsificationRewriteBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PRESPARSIFICATIONREWRITE
#endif // GEN_PASS_DEF_PRESPARSIFICATIONREWRITE

//===----------------------------------------------------------------------===//
// SparseBufferRewrite
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SPARSEBUFFERREWRITE
struct SparseBufferRewriteOptions {
  bool enableBufferInitialization = false;
};
#undef GEN_PASS_DECL_SPARSEBUFFERREWRITE
#endif // GEN_PASS_DECL_SPARSEBUFFERREWRITE
#ifdef GEN_PASS_DEF_SPARSEBUFFERREWRITE
namespace impl {

template <typename DerivedT>
class SparseBufferRewriteBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseBufferRewriteBase;

  SparseBufferRewriteBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseBufferRewriteBase(const SparseBufferRewriteBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-buffer-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-buffer-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite sparse primitives on buffers to actual code"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseBufferRewrite");
  }
  ::llvm::StringRef getName() const override { return "SparseBufferRewrite"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseBufferRewriteBase<DerivedT>)

  SparseBufferRewriteBase(const SparseBufferRewriteOptions &options) : SparseBufferRewriteBase() {
    enableBufferInitialization = options.enableBufferInitialization;
  }
protected:
  ::mlir::Pass::Option<bool> enableBufferInitialization{*this, "enable-buffer-initialization", ::llvm::cl::desc("Enable zero-initialization of the memory buffers"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SPARSEBUFFERREWRITE
#endif // GEN_PASS_DEF_SPARSEBUFFERREWRITE

//===----------------------------------------------------------------------===//
// SparseGPUCodegen
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SPARSEGPUCODEGEN
struct SparseGPUCodegenOptions {
  int32_t numThreads = 1024;
};
#undef GEN_PASS_DECL_SPARSEGPUCODEGEN
#endif // GEN_PASS_DECL_SPARSEGPUCODEGEN
#ifdef GEN_PASS_DEF_SPARSEGPUCODEGEN
namespace impl {

template <typename DerivedT>
class SparseGPUCodegenBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseGPUCodegenBase;

  SparseGPUCodegenBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseGPUCodegenBase(const SparseGPUCodegenBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-gpu-codegen");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-gpu-codegen"; }

  ::llvm::StringRef getDescription() const override { return "Generates GPU code during sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseGPUCodegen");
  }
  ::llvm::StringRef getName() const override { return "SparseGPUCodegen"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<gpu::GPUDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseGPUCodegenBase<DerivedT>)

  SparseGPUCodegenBase(const SparseGPUCodegenOptions &options) : SparseGPUCodegenBase() {
    numThreads = options.numThreads;
  }
protected:
  ::mlir::Pass::Option<int32_t> numThreads{*this, "num_threads", ::llvm::cl::desc("Sets the number of GPU threads"), ::llvm::cl::init(1024)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SPARSEGPUCODEGEN
#endif // GEN_PASS_DEF_SPARSEGPUCODEGEN

//===----------------------------------------------------------------------===//
// SparseTensorCodegen
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SPARSETENSORCODEGEN
struct SparseTensorCodegenOptions {
  bool enableBufferInitialization = false;
  bool createSparseDeallocs = true;
};
#undef GEN_PASS_DECL_SPARSETENSORCODEGEN
#endif // GEN_PASS_DECL_SPARSETENSORCODEGEN
#ifdef GEN_PASS_DEF_SPARSETENSORCODEGEN
namespace impl {

template <typename DerivedT>
class SparseTensorCodegenBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseTensorCodegenBase;

  SparseTensorCodegenBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseTensorCodegenBase(const SparseTensorCodegenBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-tensor-codegen");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-tensor-codegen"; }

  ::llvm::StringRef getDescription() const override { return "Convert sparse tensors and primitives to actual code"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseTensorCodegen");
  }
  ::llvm::StringRef getName() const override { return "SparseTensorCodegen"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseTensorCodegenBase<DerivedT>)

  SparseTensorCodegenBase(const SparseTensorCodegenOptions &options) : SparseTensorCodegenBase() {
    enableBufferInitialization = options.enableBufferInitialization;
    createSparseDeallocs = options.createSparseDeallocs;
  }
protected:
  ::mlir::Pass::Option<bool> enableBufferInitialization{*this, "enable-buffer-initialization", ::llvm::cl::desc("Enable zero-initialization of the memory buffers"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> createSparseDeallocs{*this, "create-sparse-deallocs", ::llvm::cl::desc("Specify if the temporary buffers created by the sparse compiler should be deallocated. For compatibility with core bufferization passes. This option is only used when enable-runtime-library=false. See also create-deallocs for BufferizationOption."), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SPARSETENSORCODEGEN
#endif // GEN_PASS_DEF_SPARSETENSORCODEGEN

//===----------------------------------------------------------------------===//
// SparseTensorConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SPARSETENSORCONVERSIONPASS
struct SparseTensorConversionPassOptions {
  int32_t sparseToSparse = 0;
};
#undef GEN_PASS_DECL_SPARSETENSORCONVERSIONPASS
#endif // GEN_PASS_DECL_SPARSETENSORCONVERSIONPASS
#ifdef GEN_PASS_DEF_SPARSETENSORCONVERSIONPASS
namespace impl {

template <typename DerivedT>
class SparseTensorConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseTensorConversionPassBase;

  SparseTensorConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseTensorConversionPassBase(const SparseTensorConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-tensor-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-tensor-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert sparse tensors and primitives to library calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseTensorConversionPass");
  }
  ::llvm::StringRef getName() const override { return "SparseTensorConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseTensorConversionPassBase<DerivedT>)

  SparseTensorConversionPassBase(const SparseTensorConversionPassOptions &options) : SparseTensorConversionPassBase() {
    sparseToSparse = options.sparseToSparse;
  }
protected:
  ::mlir::Pass::Option<int32_t> sparseToSparse{*this, "s2s-strategy", ::llvm::cl::desc("Set the strategy for sparse-to-sparse conversion"), ::llvm::cl::init(0)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SPARSETENSORCONVERSIONPASS
#endif // GEN_PASS_DEF_SPARSETENSORCONVERSIONPASS

//===----------------------------------------------------------------------===//
// SparseVectorization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SPARSEVECTORIZATION
struct SparseVectorizationOptions {
  int32_t vectorLength = 0;
  bool enableVLAVectorization = false;
  bool enableSIMDIndex32 = false;
};
#undef GEN_PASS_DECL_SPARSEVECTORIZATION
#endif // GEN_PASS_DECL_SPARSEVECTORIZATION
#ifdef GEN_PASS_DEF_SPARSEVECTORIZATION
namespace impl {

template <typename DerivedT>
class SparseVectorizationBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseVectorizationBase;

  SparseVectorizationBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseVectorizationBase(const SparseVectorizationBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-vectorization");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-vectorization"; }

  ::llvm::StringRef getDescription() const override { return "Vectorizes loops after sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseVectorization");
  }
  ::llvm::StringRef getName() const override { return "SparseVectorization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseVectorizationBase<DerivedT>)

  SparseVectorizationBase(const SparseVectorizationOptions &options) : SparseVectorizationBase() {
    vectorLength = options.vectorLength;
    enableVLAVectorization = options.enableVLAVectorization;
    enableSIMDIndex32 = options.enableSIMDIndex32;
  }
protected:
  ::mlir::Pass::Option<int32_t> vectorLength{*this, "vl", ::llvm::cl::desc("Set the vector length (use 0 to disable vectorization)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> enableVLAVectorization{*this, "enable-vla-vectorization", ::llvm::cl::desc("Enable vector length agnostic vectorization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableSIMDIndex32{*this, "enable-simd-index32", ::llvm::cl::desc("Enable i32 indexing into vectors (for efficient gather/scatter)"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SPARSEVECTORIZATION
#endif // GEN_PASS_DEF_SPARSEVECTORIZATION

//===----------------------------------------------------------------------===//
// SparsificationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SPARSIFICATIONPASS
struct SparsificationPassOptions {
  bool enableIndexReduction = false;
  mlir::SparseParallelizationStrategy parallelization = mlir::SparseParallelizationStrategy::kNone;
};
#undef GEN_PASS_DECL_SPARSIFICATIONPASS
#endif // GEN_PASS_DECL_SPARSIFICATIONPASS
#ifdef GEN_PASS_DEF_SPARSIFICATIONPASS
namespace impl {

template <typename DerivedT>
class SparsificationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparsificationPassBase;

  SparsificationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparsificationPassBase(const SparsificationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparsification");
  }
  ::llvm::StringRef getArgument() const override { return "sparsification"; }

  ::llvm::StringRef getDescription() const override { return "Automatically generate sparse tensor code from sparse tensor types"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparsificationPass");
  }
  ::llvm::StringRef getName() const override { return "SparsificationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparsificationPassBase<DerivedT>)

  SparsificationPassBase(const SparsificationPassOptions &options) : SparsificationPassBase() {
    enableIndexReduction = options.enableIndexReduction;
    parallelization = options.parallelization;
  }
protected:
  ::mlir::Pass::Option<bool> enableIndexReduction{*this, "enable-index-reduction", ::llvm::cl::desc("Enable dependent index reduction based algorithm to handle non-trivial index expressions on sparse inputs (experimental features)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<mlir::SparseParallelizationStrategy> parallelization{*this, "parallelization-strategy", ::llvm::cl::desc("Set the parallelization strategy"), ::llvm::cl::init(mlir::SparseParallelizationStrategy::kNone), llvm::cl::values(
             clEnumValN(mlir::SparseParallelizationStrategy::kNone, "none",
                        "Turn off sparse parallelization."),
             clEnumValN(mlir::SparseParallelizationStrategy::kDenseOuterLoop,
                        "dense-outer-loop",
                        "Enable dense outer loop sparse parallelization."),
             clEnumValN(mlir::SparseParallelizationStrategy::kAnyStorageOuterLoop,
                        "any-storage-outer-loop",
                        "Enable sparse parallelization regardless of storage for the outer loop."),
             clEnumValN(mlir::SparseParallelizationStrategy::kDenseAnyLoop,
                        "dense-any-loop",
                        "Enable dense parallelization for any loop."),
             clEnumValN(mlir::SparseParallelizationStrategy::kAnyStorageAnyLoop,
                        "any-storage-any-loop",
                        "Enable sparse parallelization for any storage and loop."))};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SPARSIFICATIONPASS
#endif // GEN_PASS_DEF_SPARSIFICATIONPASS

//===----------------------------------------------------------------------===//
// StorageSpecifierToLLVM
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STORAGESPECIFIERTOLLVM
#undef GEN_PASS_DECL_STORAGESPECIFIERTOLLVM
#endif // GEN_PASS_DECL_STORAGESPECIFIERTOLLVM
#ifdef GEN_PASS_DEF_STORAGESPECIFIERTOLLVM
namespace impl {

template <typename DerivedT>
class StorageSpecifierToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StorageSpecifierToLLVMBase;

  StorageSpecifierToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StorageSpecifierToLLVMBase(const StorageSpecifierToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-storage-specifier-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-storage-specifier-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower sparse storage specifer to llvm structure"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StorageSpecifierToLLVM");
  }
  ::llvm::StringRef getName() const override { return "StorageSpecifierToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StorageSpecifierToLLVMBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STORAGESPECIFIERTOLLVM
#endif // GEN_PASS_DEF_STORAGESPECIFIERTOLLVM
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// PostSparsificationRewrite Registration
//===----------------------------------------------------------------------===//

inline void registerPostSparsificationRewrite() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPostSparsificationRewritePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPostSparsificationRewritePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPostSparsificationRewritePass();
  });
}

//===----------------------------------------------------------------------===//
// PreSparsificationRewrite Registration
//===----------------------------------------------------------------------===//

inline void registerPreSparsificationRewrite() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPreSparsificationRewritePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPreSparsificationRewritePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPreSparsificationRewritePass();
  });
}

//===----------------------------------------------------------------------===//
// SparseBufferRewrite Registration
//===----------------------------------------------------------------------===//

inline void registerSparseBufferRewrite() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseBufferRewritePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSparseBufferRewritePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseBufferRewritePass();
  });
}

//===----------------------------------------------------------------------===//
// SparseGPUCodegen Registration
//===----------------------------------------------------------------------===//

inline void registerSparseGPUCodegen() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseGPUCodegenPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSparseGPUCodegenPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseGPUCodegenPass();
  });
}

//===----------------------------------------------------------------------===//
// SparseTensorCodegen Registration
//===----------------------------------------------------------------------===//

inline void registerSparseTensorCodegen() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseTensorCodegenPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSparseTensorCodegenPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseTensorCodegenPass();
  });
}

//===----------------------------------------------------------------------===//
// SparseTensorConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerSparseTensorConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseTensorConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSparseTensorConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseTensorConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// SparseVectorization Registration
//===----------------------------------------------------------------------===//

inline void registerSparseVectorization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseVectorizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSparseVectorizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseVectorizationPass();
  });
}

//===----------------------------------------------------------------------===//
// SparsificationPass Registration
//===----------------------------------------------------------------------===//

inline void registerSparsificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparsificationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSparsificationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparsificationPass();
  });
}

//===----------------------------------------------------------------------===//
// StorageSpecifierToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerStorageSpecifierToLLVM() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStorageSpecifierToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStorageSpecifierToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStorageSpecifierToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// SparseTensor Registration
//===----------------------------------------------------------------------===//

inline void registerSparseTensorPasses() {
  registerPostSparsificationRewrite();
  registerPreSparsificationRewrite();
  registerSparseBufferRewrite();
  registerSparseGPUCodegen();
  registerSparseTensorCodegen();
  registerSparseTensorConversionPass();
  registerSparseVectorization();
  registerSparsificationPass();
  registerStorageSpecifierToLLVM();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class PostSparsificationRewriteBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PostSparsificationRewriteBase;

  PostSparsificationRewriteBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PostSparsificationRewriteBase(const PostSparsificationRewriteBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("post-sparsification-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "post-sparsification-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Applies sparse tensor rewriting rules after sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PostSparsificationRewrite");
  }
  ::llvm::StringRef getName() const override { return "PostSparsificationRewrite"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PostSparsificationRewriteBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enableRuntimeLibrary{*this, "enable-runtime-library", ::llvm::cl::desc("Enable runtime library for manipulating sparse tensors"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableForeach{*this, "enable-foreach", ::llvm::cl::desc("Enable rewriting rules for the foreach operator"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableConvert{*this, "enable-convert", ::llvm::cl::desc("Enable rewriting rules for the convert operator"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class PreSparsificationRewriteBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PreSparsificationRewriteBase;

  PreSparsificationRewriteBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PreSparsificationRewriteBase(const PreSparsificationRewriteBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("pre-sparsification-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "pre-sparsification-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Applies sparse tensor rewriting rules prior to sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PreSparsificationRewrite");
  }
  ::llvm::StringRef getName() const override { return "PreSparsificationRewrite"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PreSparsificationRewriteBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SparseBufferRewriteBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseBufferRewriteBase;

  SparseBufferRewriteBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseBufferRewriteBase(const SparseBufferRewriteBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-buffer-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-buffer-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite sparse primitives on buffers to actual code"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseBufferRewrite");
  }
  ::llvm::StringRef getName() const override { return "SparseBufferRewrite"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseBufferRewriteBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enableBufferInitialization{*this, "enable-buffer-initialization", ::llvm::cl::desc("Enable zero-initialization of the memory buffers"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class SparseGPUCodegenBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseGPUCodegenBase;

  SparseGPUCodegenBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseGPUCodegenBase(const SparseGPUCodegenBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-gpu-codegen");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-gpu-codegen"; }

  ::llvm::StringRef getDescription() const override { return "Generates GPU code during sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseGPUCodegen");
  }
  ::llvm::StringRef getName() const override { return "SparseGPUCodegen"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<gpu::GPUDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseGPUCodegenBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int32_t> numThreads{*this, "num_threads", ::llvm::cl::desc("Sets the number of GPU threads"), ::llvm::cl::init(1024)};
};

template <typename DerivedT>
class SparseTensorCodegenBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseTensorCodegenBase;

  SparseTensorCodegenBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseTensorCodegenBase(const SparseTensorCodegenBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-tensor-codegen");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-tensor-codegen"; }

  ::llvm::StringRef getDescription() const override { return "Convert sparse tensors and primitives to actual code"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseTensorCodegen");
  }
  ::llvm::StringRef getName() const override { return "SparseTensorCodegen"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseTensorCodegenBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enableBufferInitialization{*this, "enable-buffer-initialization", ::llvm::cl::desc("Enable zero-initialization of the memory buffers"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> createSparseDeallocs{*this, "create-sparse-deallocs", ::llvm::cl::desc("Specify if the temporary buffers created by the sparse compiler should be deallocated. For compatibility with core bufferization passes. This option is only used when enable-runtime-library=false. See also create-deallocs for BufferizationOption."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class SparseTensorConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseTensorConversionPassBase;

  SparseTensorConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseTensorConversionPassBase(const SparseTensorConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-tensor-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-tensor-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert sparse tensors and primitives to library calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseTensorConversionPass");
  }
  ::llvm::StringRef getName() const override { return "SparseTensorConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseTensorConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int32_t> sparseToSparse{*this, "s2s-strategy", ::llvm::cl::desc("Set the strategy for sparse-to-sparse conversion"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class SparseVectorizationBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseVectorizationBase;

  SparseVectorizationBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseVectorizationBase(const SparseVectorizationBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-vectorization");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-vectorization"; }

  ::llvm::StringRef getDescription() const override { return "Vectorizes loops after sparsification"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseVectorization");
  }
  ::llvm::StringRef getName() const override { return "SparseVectorization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparseVectorizationBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int32_t> vectorLength{*this, "vl", ::llvm::cl::desc("Set the vector length (use 0 to disable vectorization)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> enableVLAVectorization{*this, "enable-vla-vectorization", ::llvm::cl::desc("Enable vector length agnostic vectorization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableSIMDIndex32{*this, "enable-simd-index32", ::llvm::cl::desc("Enable i32 indexing into vectors (for efficient gather/scatter)"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class SparsificationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparsificationPassBase;

  SparsificationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparsificationPassBase(const SparsificationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparsification");
  }
  ::llvm::StringRef getArgument() const override { return "sparsification"; }

  ::llvm::StringRef getDescription() const override { return "Automatically generate sparse tensor code from sparse tensor types"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparsificationPass");
  }
  ::llvm::StringRef getName() const override { return "SparsificationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<arith::ArithDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SparsificationPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enableIndexReduction{*this, "enable-index-reduction", ::llvm::cl::desc("Enable dependent index reduction based algorithm to handle non-trivial index expressions on sparse inputs (experimental features)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<mlir::SparseParallelizationStrategy> parallelization{*this, "parallelization-strategy", ::llvm::cl::desc("Set the parallelization strategy"), ::llvm::cl::init(mlir::SparseParallelizationStrategy::kNone), llvm::cl::values(
             clEnumValN(mlir::SparseParallelizationStrategy::kNone, "none",
                        "Turn off sparse parallelization."),
             clEnumValN(mlir::SparseParallelizationStrategy::kDenseOuterLoop,
                        "dense-outer-loop",
                        "Enable dense outer loop sparse parallelization."),
             clEnumValN(mlir::SparseParallelizationStrategy::kAnyStorageOuterLoop,
                        "any-storage-outer-loop",
                        "Enable sparse parallelization regardless of storage for the outer loop."),
             clEnumValN(mlir::SparseParallelizationStrategy::kDenseAnyLoop,
                        "dense-any-loop",
                        "Enable dense parallelization for any loop."),
             clEnumValN(mlir::SparseParallelizationStrategy::kAnyStorageAnyLoop,
                        "any-storage-any-loop",
                        "Enable sparse parallelization for any storage and loop."))};
};

template <typename DerivedT>
class StorageSpecifierToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StorageSpecifierToLLVMBase;

  StorageSpecifierToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StorageSpecifierToLLVMBase(const StorageSpecifierToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-storage-specifier-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-storage-specifier-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower sparse storage specifer to llvm structure"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StorageSpecifierToLLVM");
  }
  ::llvm::StringRef getName() const override { return "StorageSpecifierToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StorageSpecifierToLLVMBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
