# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/full_type.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(tensorboard/compat/proto/full_type.proto\x12\x0btensorboard\"\x81\x01\n\x0b\x46ullTypeDef\x12(\n\x07type_id\x18\x01 \x01(\x0e\x32\x17.tensorboard.FullTypeId\x12&\n\x04\x61rgs\x18\x02 \x03(\x0b\x32\x18.tensorboard.FullTypeDef\x12\x0b\n\x01s\x18\x03 \x01(\tH\x00\x12\x0b\n\x01i\x18\x04 \x01(\x03H\x00\x42\x06\n\x04\x61ttr*\xda\x04\n\nFullTypeId\x12\r\n\tTFT_UNSET\x10\x00\x12\x0b\n\x07TFT_VAR\x10\x01\x12\x0b\n\x07TFT_ANY\x10\x02\x12\x0f\n\x0bTFT_PRODUCT\x10\x03\x12\r\n\tTFT_NAMED\x10\x04\x12\x10\n\x0cTFT_FOR_EACH\x10\x14\x12\x10\n\x0cTFT_CALLABLE\x10\x64\x12\x0f\n\nTFT_TENSOR\x10\xe8\x07\x12\x0e\n\tTFT_ARRAY\x10\xe9\x07\x12\x11\n\x0cTFT_OPTIONAL\x10\xea\x07\x12\x10\n\x0bTFT_LITERAL\x10\xeb\x07\x12\x10\n\x0bTFT_ENCODED\x10\xec\x07\x12\x15\n\x10TFT_SHAPE_TENSOR\x10\xed\x07\x12\r\n\x08TFT_BOOL\x10\xc8\x01\x12\x0e\n\tTFT_UINT8\x10\xc9\x01\x12\x0f\n\nTFT_UINT16\x10\xca\x01\x12\x0f\n\nTFT_UINT32\x10\xcb\x01\x12\x0f\n\nTFT_UINT64\x10\xcc\x01\x12\r\n\x08TFT_INT8\x10\xcd\x01\x12\x0e\n\tTFT_INT16\x10\xce\x01\x12\x0e\n\tTFT_INT32\x10\xcf\x01\x12\x0e\n\tTFT_INT64\x10\xd0\x01\x12\r\n\x08TFT_HALF\x10\xd1\x01\x12\x0e\n\tTFT_FLOAT\x10\xd2\x01\x12\x0f\n\nTFT_DOUBLE\x10\xd3\x01\x12\x11\n\x0cTFT_BFLOAT16\x10\xd7\x01\x12\x12\n\rTFT_COMPLEX64\x10\xd4\x01\x12\x13\n\x0eTFT_COMPLEX128\x10\xd5\x01\x12\x0f\n\nTFT_STRING\x10\xd6\x01\x12\x10\n\x0bTFT_DATASET\x10\xf6N\x12\x0f\n\nTFT_RAGGED\x10\xf7N\x12\x11\n\x0cTFT_ITERATOR\x10\xf8N\x12\x13\n\x0eTFT_MUTEX_LOCK\x10\xdaO\x12\x17\n\x12TFT_LEGACY_VARIANT\x10\xdbOB\x81\x01\n\x18org.tensorflow.frameworkB\x0e\x46ullTypeProtosP\x01ZPgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto\xf8\x01\x01\x62\x06proto3')

_FULLTYPEID = DESCRIPTOR.enum_types_by_name['FullTypeId']
FullTypeId = enum_type_wrapper.EnumTypeWrapper(_FULLTYPEID)
TFT_UNSET = 0
TFT_VAR = 1
TFT_ANY = 2
TFT_PRODUCT = 3
TFT_NAMED = 4
TFT_FOR_EACH = 20
TFT_CALLABLE = 100
TFT_TENSOR = 1000
TFT_ARRAY = 1001
TFT_OPTIONAL = 1002
TFT_LITERAL = 1003
TFT_ENCODED = 1004
TFT_SHAPE_TENSOR = 1005
TFT_BOOL = 200
TFT_UINT8 = 201
TFT_UINT16 = 202
TFT_UINT32 = 203
TFT_UINT64 = 204
TFT_INT8 = 205
TFT_INT16 = 206
TFT_INT32 = 207
TFT_INT64 = 208
TFT_HALF = 209
TFT_FLOAT = 210
TFT_DOUBLE = 211
TFT_BFLOAT16 = 215
TFT_COMPLEX64 = 212
TFT_COMPLEX128 = 213
TFT_STRING = 214
TFT_DATASET = 10102
TFT_RAGGED = 10103
TFT_ITERATOR = 10104
TFT_MUTEX_LOCK = 10202
TFT_LEGACY_VARIANT = 10203


_FULLTYPEDEF = DESCRIPTOR.message_types_by_name['FullTypeDef']
FullTypeDef = _reflection.GeneratedProtocolMessageType('FullTypeDef', (_message.Message,), {
  'DESCRIPTOR' : _FULLTYPEDEF,
  '__module__' : 'tensorboard.compat.proto.full_type_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.FullTypeDef)
  })
_sym_db.RegisterMessage(FullTypeDef)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\016FullTypeProtosP\001ZPgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto\370\001\001'
  _FULLTYPEID._serialized_start=190
  _FULLTYPEID._serialized_end=792
  _FULLTYPEDEF._serialized_start=58
  _FULLTYPEDEF._serialized_end=187
# @@protoc_insertion_point(module_scope)
