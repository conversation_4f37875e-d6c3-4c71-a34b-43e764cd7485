<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chess Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <style>
        #board {
            width: 400px;
            margin: 20px auto;
        }
        #message {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
        }
        .button-container {
            text-align: center;
            margin-top: 20px;
        }
        button {
            margin: 0 10px;
            padding: 10px 20px;
            font-size: 16px;
        }
        #evaluation {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
        }
        .difficulty-container {
            text-align: center;
            margin-top: 20px;
        }
        #difficultySlider {
            width: 200px;
        }
    </style>
</head>
<body>
    <div id="board"></div>
    <div id="message"></div>
    <div id="evaluation"></div>
    <div class="difficulty-container">
        <label for="difficultySlider">AI Difficulty: </label>
        <input type="range" id="difficultySlider" min="0" max="1" step="0.1" value="0.8">
        <span id="difficultyValue">0.8 (Master)</span>
    </div>
    <div class="button-container">
        <button id="newGameBtn">New Game</button>
        <button id="trainAIBtn">Train AI</button>
        <button id="undoBtn">Undo Move</button>
    </div>
    <script>
        var board = null;
        var game = new Chess();
        var difficulty = 0.8;

        function onDragStart(source, piece, position, orientation) {
            if (game.game_over()) return false;
            if (piece.search(/^b/) !== -1) return false;
        }

        function makeMove(move) {
            $('#message').text('AI is thinking...');
            $.ajax({
                url: '/make_move',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ move: move, difficulty: difficulty }),
                success: function(data) {
                    game.load(data.fen);
                    board.position(data.fen);

                    if (data.game_over) {
                        $('#message').text('Game Over. Result: ' + data.result);
                    } else {
                        $('#message').text('AI moved: ' + data.ai_move);
                    }
                    $('#evaluation').text('Evaluation: ' + data.evaluation);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error:', textStatus, errorThrown);
                    game.undo();
                    board.position(game.fen());
                    $('#message').text('Invalid move. Try again.');
                }
            });
        }

        function onDrop(source, target) {
            var move = game.move({
                from: source,
                to: target,
                promotion: 'q'
            });

            if (move === null) return 'snapback';

            makeMove(source + target);
            return '';
        }

        function onSnapEnd() {
            board.position(game.fen());
        }

        function updateDifficulty() {
            difficulty = parseFloat($('#difficultySlider').val());
            let skillLevel = '';
            if (difficulty >= 0.9) {
                skillLevel = ' (Grandmaster)';
            } else if (difficulty >= 0.8) {
                skillLevel = ' (Master)';
            } else if (difficulty >= 0.6) {
                skillLevel = ' (Expert)';
            } else if (difficulty >= 0.4) {
                skillLevel = ' (Intermediate)';
            } else if (difficulty >= 0.2) {
                skillLevel = ' (Beginner)';
            } else {
                skillLevel = ' (Very Easy)';
            }
            $('#difficultyValue').text(difficulty.toFixed(1) + skillLevel);
        }

        $(document).ready(function() {
            var config = {
                draggable: true,
                position: 'start',
                onDragStart: onDragStart,
                onDrop: onDrop,
                onSnapEnd: onSnapEnd,
                pieceTheme: 'https://chessboardjs.com/img/chesspieces/wikipedia/{piece}.png'
            };
            board = Chessboard('board', config);

            $('#newGameBtn').click(function() {
                $.post('/new_game', function(data) {
                    game = new Chess();
                    game.load(data.fen);
                    board.position(data.fen);
                    $('#message').text('New game started.');
                    $('#evaluation').text('');
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('Error starting new game:', textStatus, errorThrown);
                    $('#message').text('Error starting new game. Please try again.');
                });
            });

            $('#trainAIBtn').click(function() {
                $('#message').text('Starting AI training...');
                $.ajax({
                    url: '/train_ai',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ num_games: 50 }),
                    success: function(data) {
                        $('#message').text(data.message);
                        // Start polling for training status
                        pollTrainingStatus();
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error training AI:', textStatus, errorThrown);
                        if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
                            $('#message').text('Error: ' + jqXHR.responseJSON.error);
                        } else {
                            $('#message').text('Error training AI. Please try again.');
                        }
                    }
                });
            });

            function pollTrainingStatus() {
                $.ajax({
                    url: '/training_status',
                    method: 'GET',
                    success: function(data) {
                        if (data.is_training) {
                            $('#message').text(`Training: ${data.current_game}/${data.total_games} games (${data.progress}%)`);
                            // Continue polling
                            setTimeout(pollTrainingStatus, 1000);
                        } else {
                            $('#message').text(data.message);
                        }
                    },
                    error: function() {
                        console.error('Error getting training status');
                        setTimeout(pollTrainingStatus, 2000); // Retry after 2 seconds
                    }
                });
            }

            $('#undoBtn').click(function() {
                $.ajax({
                    url: '/undo_move',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({}),
                    success: function(data) {
                        game.load(data.fen);
                        board.position(data.fen);
                        $('#message').text('Move(s) undone. Undid ' + data.undone_moves + ' move(s).');
                        $('#evaluation').text('Evaluation: ' + data.evaluation);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error undoing move:', textStatus, errorThrown);
                        $('#message').text('No moves to undo.');
                    }
                });
            });

            $('#difficultySlider').on('input', updateDifficulty);
            updateDifficulty();
        });
    </script>
</body>
</html>
