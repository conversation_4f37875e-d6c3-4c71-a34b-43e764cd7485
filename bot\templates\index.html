<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chess Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .game-mode-selection {
            text-align: center;
            margin: 40px 0;
        }
        .mode-buttons {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 30px;
        }
        .mode-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 30px;
            border-radius: 15px;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            min-width: 200px;
        }
        .mode-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .mode-btn h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .mode-btn p {
            margin: 0;
            opacity: 0.9;
        }
        #board {
            width: 400px;
            margin: 20px auto;
        }
        #message {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
        }
        .button-container {
            text-align: center;
            margin-top: 20px;
        }
        button {
            margin: 0 10px;
            padding: 10px 20px;
            font-size: 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        #evaluation {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
        }
        .difficulty-container {
            text-align: center;
            margin-top: 20px;
        }
        #difficultySlider, #gameSpeed {
            width: 200px;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
        }
        .speed-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        #gameMode {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            color: #1976d2;
        }
        #aiStatus {
            text-align: center;
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- Game Mode Selection Menu -->
    <div id="gameModeMenu" class="container">
        <h1>Chess AI Bot</h1>
        <div class="game-mode-selection">
            <h2>Select Game Mode</h2>
            <div class="mode-buttons">
                <button id="humanVsAI" class="mode-btn">
                    <h3>👤 Human vs AI</h3>
                    <p>Play against the AI opponent</p>
                </button>
                <button id="aiVsAI" class="mode-btn">
                    <h3>🤖 AI vs AI</h3>
                    <p>Watch two AIs play against each other</p>
                </button>
            </div>
        </div>
    </div>

    <!-- Game Interface (hidden initially) -->
    <div id="gameInterface" class="container" style="display: none;">
        <h1>Chess AI Bot</h1>
        <div id="gameMode"></div>

        <div class="controls">
            <button id="newGameBtn">New Game</button>
            <button id="undoBtn">Undo Move</button>
            <button id="trainAIBtn">Train AI</button>
            <button id="backToMenu">Back to Menu</button>
        </div>

        <div class="difficulty-container">
            <label for="difficultySlider">AI Difficulty: </label>
            <input type="range" id="difficultySlider" min="0" max="1" step="0.1" value="0.8">
            <span id="difficultyValue">0.8 (Master)</span>
        </div>

        <!-- AI vs AI specific controls -->
        <div id="aiVsAiControls" style="display: none;">
            <div class="controls">
                <button id="pauseGame">⏸️ Pause</button>
                <button id="resumeGame">▶️ Resume</button>
                <div class="speed-control">
                    <label for="gameSpeed">Game Speed:</label>
                    <input type="range" id="gameSpeed" min="500" max="3000" step="250" value="1500">
                    <span id="speedValue">1.5s</span>
                </div>
            </div>
        </div>

        <div id="board"></div>
        <div id="message"></div>
        <div id="evaluation"></div>
        <div id="aiStatus"></div>
    </div>
    <script>
        var board = null;
        var game = new Chess();
        var difficulty = 0.8;
        var gameMode = 'human'; // 'human' or 'ai'
        var aiVsAiInterval = null;
        var gameSpeed = 1500; // milliseconds
        var isPaused = false;

        function onDragStart(source, piece, position, orientation) {
            if (game.game_over()) return false;
            if (gameMode === 'ai') return false; // No dragging in AI vs AI mode
            if (piece.search(/^b/) !== -1) return false;
        }

        function makeMove(move) {
            $('#message').text('AI is thinking...');
            $.ajax({
                url: '/make_move',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ move: move, difficulty: difficulty }),
                success: function(data) {
                    game.load(data.fen);
                    board.position(data.fen);

                    if (data.game_over) {
                        $('#message').text('Game Over. Result: ' + data.result);
                        $('#aiStatus').text('🧠 AI is learning from this game...');
                        setTimeout(() => {
                            $('#aiStatus').text('✅ AI has learned from the game and improved!');
                        }, 2000);
                    } else {
                        $('#message').text('AI moved: ' + data.ai_move);
                        $('#aiStatus').text('');
                    }
                    updateEvaluation(data.evaluation);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error:', textStatus, errorThrown);
                    game.undo();
                    board.position(game.fen());
                    $('#message').text('Invalid move. Try again.');
                }
            });
        }

        function onDrop(source, target) {
            var move = game.move({
                from: source,
                to: target,
                promotion: 'q'
            });

            if (move === null) return 'snapback';

            makeMove(source + target);
            return '';
        }

        function onSnapEnd() {
            board.position(game.fen());
        }

        function updateDifficulty() {
            difficulty = parseFloat($('#difficultySlider').val());
            let skillLevel = '';
            if (difficulty >= 0.9) {
                skillLevel = ' (Grandmaster)';
            } else if (difficulty >= 0.8) {
                skillLevel = ' (Master)';
            } else if (difficulty >= 0.6) {
                skillLevel = ' (Expert)';
            } else if (difficulty >= 0.4) {
                skillLevel = ' (Intermediate)';
            } else if (difficulty >= 0.2) {
                skillLevel = ' (Beginner)';
            } else {
                skillLevel = ' (Very Easy)';
            }
            $('#difficultyValue').text(difficulty.toFixed(1) + skillLevel);
        }

        function makeAIMove() {
            if (game.game_over() || isPaused) return;

            $('#message').text('AI is thinking...');
            $.ajax({
                url: '/ai_move',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    fen: game.fen(),
                    difficulty: difficulty
                }),
                success: function(data) {
                    if (data.move) {
                        // Convert move format from "e2e4" to {from: "e2", to: "e4"}
                        let moveObj = {
                            from: data.move.substring(0, 2),
                            to: data.move.substring(2, 4),
                            promotion: 'q' // Default promotion to queen
                        };

                        console.log('Attempting AI move:', moveObj, 'on position:', game.fen());

                        // Validate move before attempting
                        let possibleMoves = game.moves({verbose: true});
                        let isValidMove = possibleMoves.some(move =>
                            move.from === moveObj.from && move.to === moveObj.to
                        );

                        if (isValidMove) {
                            let move = game.move(moveObj);
                            if (move) {
                                // Update the visual board
                                board.position(game.fen());

                                if (game.game_over()) {
                                    $('#message').text('Game Over! Result: ' + getGameResult());
                                    clearInterval(aiVsAiInterval);
                                    $('#aiStatus').text('🏁 Game finished!');
                                } else {
                                    $('#message').text('AI moved: ' + data.move);
                                    updateEvaluation(data.evaluation);
                                }
                            } else {
                                console.error('Move failed despite validation:', data.move);
                                makeSmartFallbackMove();
                            }
                        } else {
                            console.error('Invalid move generated:', data.move, 'Legal moves:', possibleMoves.map(m => m.from + m.to));
                            makeSmartFallbackMove();
                        }
                    } else {
                        console.error('No move returned from AI');
                        makeSmartFallbackMove();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AI move error:', xhr.responseText || error);
                    makeSmartFallbackMove();
                }
            });
        }

        function makeSmartFallbackMove() {
            console.log('Making smart fallback move...');

            let legalMoves = game.moves({ verbose: true });
            if (legalMoves.length === 0) {
                $('#message').text('Game Over! No legal moves available');
                clearInterval(aiVsAiInterval);
                $('#aiStatus').text('🏁 Game finished!');
                return;
            }

            // Smart move selection instead of random
            let bestMove = selectBestFallbackMove(legalMoves);

            let move = game.move(bestMove);
            if (move) {
                board.position(game.fen());
                $('#message').text('AI moved: ' + bestMove.san + ' (auto-selected)');

                if (game.game_over()) {
                    $('#message').text('Game Over! Result: ' + getGameResult());
                    clearInterval(aiVsAiInterval);
                    $('#aiStatus').text('🏁 Game finished!');
                }
            } else {
                console.error('Fallback move failed!');
                clearInterval(aiVsAiInterval);
                $('#message').text('Game stopped due to technical error');
            }
        }

        function selectBestFallbackMove(legalMoves) {
            // Priority order for smart fallback moves:

            // 1. Captures (taking opponent pieces)
            let captures = legalMoves.filter(move => move.captured);
            if (captures.length > 0) {
                console.log('Selecting capture move');
                return captures[0];
            }

            // 2. Checks (putting opponent in check)
            let checks = legalMoves.filter(move => {
                game.move(move);
                let inCheck = game.in_check();
                game.undo();
                return inCheck;
            });
            if (checks.length > 0) {
                console.log('Selecting check move');
                return checks[0];
            }

            // 3. Center control (e4, e5, d4, d5)
            let centerMoves = legalMoves.filter(move =>
                ['e4', 'e5', 'd4', 'd5'].includes(move.to) ||
                ['e4', 'e5', 'd4', 'd5'].includes(move.san)
            );
            if (centerMoves.length > 0) {
                console.log('Selecting center control move');
                return centerMoves[0];
            }

            // 4. Piece development (knights and bishops)
            let developmentMoves = legalMoves.filter(move =>
                (move.piece === 'n' || move.piece === 'b') &&
                !['a1', 'a8', 'h1', 'h8'].includes(move.from)
            );
            if (developmentMoves.length > 0) {
                console.log('Selecting development move');
                return developmentMoves[0];
            }

            // 5. Pawn moves (safe advancement)
            let pawnMoves = legalMoves.filter(move => move.piece === 'p');
            if (pawnMoves.length > 0) {
                console.log('Selecting pawn move');
                return pawnMoves[0];
            }

            // 6. Any other move
            console.log('Selecting first available move');
            return legalMoves[0];
        }

        function startAIvsAI() {
            if (aiVsAiInterval) clearInterval(aiVsAiInterval);
            isPaused = false;

            aiVsAiInterval = setInterval(function() {
                if (!isPaused && !game.game_over()) {
                    makeAIMove();
                } else if (game.game_over()) {
                    clearInterval(aiVsAiInterval);
                }
            }, gameSpeed);
        }

        function getGameResult() {
            if (game.in_checkmate()) {
                return game.turn() === 'w' ? 'Black wins by checkmate' : 'White wins by checkmate';
            } else if (game.in_stalemate()) {
                return 'Draw by stalemate';
            } else if (game.in_threefold_repetition()) {
                return 'Draw by repetition';
            } else if (game.insufficient_material()) {
                return 'Draw by insufficient material';
            }
            return 'Game over';
        }

        function updateEvaluation(evaluation) {
            let evalText = 'Evaluation: ';
            if (evaluation > 100) {
                evalText += '+' + (evaluation / 100).toFixed(1) + ' (winning)';
            } else if (evaluation < -100) {
                evalText += (evaluation / 100).toFixed(1) + ' (losing)';
            } else if (evaluation > 50) {
                evalText += '+' + (evaluation / 100).toFixed(1) + ' (good)';
            } else if (evaluation < -50) {
                evalText += (evaluation / 100).toFixed(1) + ' (bad)';
            } else {
                evalText += (evaluation / 100).toFixed(1) + ' (equal)';
            }
            $('#evaluation').text(evalText);
        }

        $(document).ready(function() {
            var config = {
                draggable: true,
                position: 'start',
                onDragStart: onDragStart,
                onDrop: onDrop,
                onSnapEnd: onSnapEnd,
                pieceTheme: 'https://chessboardjs.com/img/chesspieces/wikipedia/{piece}.png'
            };
            board = Chessboard('board', config);

            // Game mode selection
            $('#humanVsAI').click(function() {
                gameMode = 'human';
                $('#gameModeMenu').hide();
                $('#gameInterface').show();
                $('#gameMode').text('🎮 Human vs AI Mode').show();
                $('#aiVsAiControls').hide();

                // Enable dragging in Human vs AI mode
                var config = {
                    draggable: true,
                    position: 'start',
                    onDragStart: onDragStart,
                    onDrop: onDrop,
                    onSnapEnd: onSnapEnd,
                    pieceTheme: 'https://chessboardjs.com/img/chesspieces/wikipedia/{piece}.png'
                };
                board = Chessboard('board', config);

                startNewGame();
            });

            $('#aiVsAI').click(function() {
                gameMode = 'ai';
                $('#gameModeMenu').hide();
                $('#gameInterface').show();
                $('#gameMode').text('🤖 AI vs AI Mode - Watch the battle!').show();
                $('#aiVsAiControls').show();

                // Disable dragging in AI vs AI mode
                var config = {
                    draggable: false,
                    position: game.fen(),
                    pieceTheme: 'https://chessboardjs.com/img/chesspieces/wikipedia/{piece}.png'
                };
                board = Chessboard('board', config);

                startNewGame();
                setTimeout(startAIvsAI, 1000); // Start AI vs AI after 1 second
            });

            $('#backToMenu').click(function() {
                if (aiVsAiInterval) clearInterval(aiVsAiInterval);
                $('#gameInterface').hide();
                $('#gameModeMenu').show();
                game = new Chess();
                board.position('start');
            });

            // AI vs AI controls
            $('#pauseGame').click(function() {
                isPaused = true;
                $('#aiStatus').text('⏸️ Game paused');
            });

            $('#resumeGame').click(function() {
                isPaused = false;
                $('#aiStatus').text('▶️ Game resumed');
            });

            $('#gameSpeed').on('input', function() {
                gameSpeed = parseInt($(this).val());
                $('#speedValue').text((gameSpeed / 1000).toFixed(1) + 's');

                // Restart interval with new speed
                if (gameMode === 'ai' && aiVsAiInterval) {
                    clearInterval(aiVsAiInterval);
                    startAIvsAI();
                }
            });

            function startNewGame() {
                if (aiVsAiInterval) clearInterval(aiVsAiInterval);
                isPaused = false;

                $.post('/new_game', function(data) {
                    game = new Chess();
                    game.load(data.fen);
                    board.position(data.fen);
                    $('#message').text('New game started.');
                    $('#evaluation').text('');
                    $('#aiStatus').text('');
                    console.log('New game started with FEN:', data.fen);
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('Error starting new game:', textStatus, errorThrown);
                    $('#message').text('Error starting new game. Please try again.');
                });
            }

            $('#newGameBtn').click(function() {
                startNewGame();
                if (gameMode === 'ai') {
                    setTimeout(startAIvsAI, 1000);
                }
            });

            $('#trainAIBtn').click(function() {
                $('#message').text('Starting AI training...');
                $.ajax({
                    url: '/train_ai',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ num_games: 50 }),
                    success: function(data) {
                        $('#message').text(data.message);
                        // Start polling for training status
                        pollTrainingStatus();
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error training AI:', textStatus, errorThrown);
                        if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
                            $('#message').text('Error: ' + jqXHR.responseJSON.error);
                        } else {
                            $('#message').text('Error training AI. Please try again.');
                        }
                    }
                });
            });

            function pollTrainingStatus() {
                $.ajax({
                    url: '/training_status',
                    method: 'GET',
                    success: function(data) {
                        if (data.is_training) {
                            $('#message').text(`Training: ${data.current_game}/${data.total_games} games (${data.progress}%)`);
                            // Continue polling
                            setTimeout(pollTrainingStatus, 1000);
                        } else {
                            $('#message').text(data.message);
                        }
                    },
                    error: function() {
                        console.error('Error getting training status');
                        setTimeout(pollTrainingStatus, 2000); // Retry after 2 seconds
                    }
                });
            }

            $('#undoBtn').click(function() {
                $.ajax({
                    url: '/undo_move',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({}),
                    success: function(data) {
                        game.load(data.fen);
                        board.position(data.fen);
                        $('#message').text('Move(s) undone. Undid ' + data.undone_moves + ' move(s).');
                        updateEvaluation(data.evaluation);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error undoing move:', textStatus, errorThrown);
                        $('#message').text('No moves to undo.');
                    }
                });
            });

            $('#difficultySlider').on('input', updateDifficulty);
            updateDifficulty();

            // Initialize speed control
            $('#speedValue').text((gameSpeed / 1000).toFixed(1) + 's');
        });
    </script>
</body>
</html>
