# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.quantization namespace.
"""

import sys as _sys

from tensorflow.python.ops.array_ops import dequantize
from tensorflow.python.ops.array_ops import quantize
from tensorflow.python.ops.array_ops import quantize_and_dequantize
from tensorflow.python.ops.array_ops import quantize_and_dequantize_v2
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_args
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_args_gradient
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars_gradient
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars_per_channel
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars_per_channel_gradient
from tensorflow.python.ops.gen_array_ops import quantized_concat