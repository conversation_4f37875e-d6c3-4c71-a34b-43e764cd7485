/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_DUPLICATEFUNCTIONELIMINATIONPASS
#define GEN_PASS_DECL_FUNCBUFFERIZE
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// DuplicateFunctionEliminationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DUPLICATEFUNCTIONELIMINATIONPASS
#undef GEN_PASS_DECL_DUPLICATEFUNCTIONELIMINATIONPASS
#endif // GEN_PASS_DECL_DUPLICATEFUNCTIONELIMINATIONPASS
#ifdef GEN_PASS_DEF_DUPLICATEFUNCTIONELIMINATIONPASS
namespace impl {

template <typename DerivedT>
class DuplicateFunctionEliminationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DuplicateFunctionEliminationPassBase;

  DuplicateFunctionEliminationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DuplicateFunctionEliminationPassBase(const DuplicateFunctionEliminationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("duplicate-function-elimination");
  }
  ::llvm::StringRef getArgument() const override { return "duplicate-function-elimination"; }

  ::llvm::StringRef getDescription() const override { return "Deduplicate functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DuplicateFunctionEliminationPass");
  }
  ::llvm::StringRef getName() const override { return "DuplicateFunctionEliminationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DuplicateFunctionEliminationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DUPLICATEFUNCTIONELIMINATIONPASS
#endif // GEN_PASS_DEF_DUPLICATEFUNCTIONELIMINATIONPASS

//===----------------------------------------------------------------------===//
// FuncBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FUNCBUFFERIZE
#undef GEN_PASS_DECL_FUNCBUFFERIZE
#endif // GEN_PASS_DECL_FUNCBUFFERIZE
#ifdef GEN_PASS_DEF_FUNCBUFFERIZE
namespace impl {

template <typename DerivedT>
class FuncBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FuncBufferizeBase;

  FuncBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FuncBufferizeBase(const FuncBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("func-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "func-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize func/call/return ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FuncBufferize");
  }
  ::llvm::StringRef getName() const override { return "FuncBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FuncBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FUNCBUFFERIZE
#endif // GEN_PASS_DEF_FUNCBUFFERIZE
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// DuplicateFunctionEliminationPass Registration
//===----------------------------------------------------------------------===//

inline void registerDuplicateFunctionEliminationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::func::createDuplicateFunctionEliminationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDuplicateFunctionEliminationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::func::createDuplicateFunctionEliminationPass();
  });
}

//===----------------------------------------------------------------------===//
// FuncBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerFuncBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::func::createFuncBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFuncBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::func::createFuncBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Func Registration
//===----------------------------------------------------------------------===//

inline void registerFuncPasses() {
  registerDuplicateFunctionEliminationPass();
  registerFuncBufferize();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class DuplicateFunctionEliminationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DuplicateFunctionEliminationPassBase;

  DuplicateFunctionEliminationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DuplicateFunctionEliminationPassBase(const DuplicateFunctionEliminationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("duplicate-function-elimination");
  }
  ::llvm::StringRef getArgument() const override { return "duplicate-function-elimination"; }

  ::llvm::StringRef getDescription() const override { return "Deduplicate functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DuplicateFunctionEliminationPass");
  }
  ::llvm::StringRef getName() const override { return "DuplicateFunctionEliminationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DuplicateFunctionEliminationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FuncBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FuncBufferizeBase;

  FuncBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FuncBufferizeBase(const FuncBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("func-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "func-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize func/call/return ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FuncBufferize");
  }
  ::llvm::StringRef getName() const override { return "FuncBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FuncBufferizeBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
