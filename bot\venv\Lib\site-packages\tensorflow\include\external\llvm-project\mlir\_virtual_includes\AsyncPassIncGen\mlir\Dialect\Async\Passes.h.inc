/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ASYNCFUNCTOASYNCRUNTIME
#define GEN_PASS_DECL_ASYNCPARALLELFOR
#define GEN_PASS_DECL_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING
#define GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTING
#define GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTINGOPT
#define GEN_PASS_DECL_ASYNCTOASYNCRUNTIME
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AsyncFuncToAsyncRuntime
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ASYNCFUNCTOASYNCRUNTIME
#undef GEN_PASS_DECL_ASYNCFUNCTOASYNCRUNTIME
#endif // GEN_PASS_DECL_ASYNCFUNCTOASYNCRUNTIME
#ifdef GEN_PASS_DEF_ASYNCFUNCTOASYNCRUNTIME
namespace impl {

template <typename DerivedT>
class AsyncFuncToAsyncRuntimeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncFuncToAsyncRuntimeBase;

  AsyncFuncToAsyncRuntimeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncFuncToAsyncRuntimeBase(const AsyncFuncToAsyncRuntimeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-func-to-async-runtime");
  }
  ::llvm::StringRef getArgument() const override { return "async-func-to-async-runtime"; }

  ::llvm::StringRef getDescription() const override { return "Lower async.func operations to the explicit async.runtime andasync.coro operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncFuncToAsyncRuntime");
  }
  ::llvm::StringRef getName() const override { return "AsyncFuncToAsyncRuntime"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncFuncToAsyncRuntimeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ASYNCFUNCTOASYNCRUNTIME
#endif // GEN_PASS_DEF_ASYNCFUNCTOASYNCRUNTIME

//===----------------------------------------------------------------------===//
// AsyncParallelFor
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ASYNCPARALLELFOR
struct AsyncParallelForOptions {
  bool asyncDispatch = true;
  int32_t numWorkerThreads = 8;
  int32_t minTaskSize = 1000;
};
#undef GEN_PASS_DECL_ASYNCPARALLELFOR
#endif // GEN_PASS_DECL_ASYNCPARALLELFOR
#ifdef GEN_PASS_DEF_ASYNCPARALLELFOR
namespace impl {

template <typename DerivedT>
class AsyncParallelForBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncParallelForBase;

  AsyncParallelForBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncParallelForBase(const AsyncParallelForBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-parallel-for");
  }
  ::llvm::StringRef getArgument() const override { return "async-parallel-for"; }

  ::llvm::StringRef getDescription() const override { return "Convert scf.parallel operations to multiple async compute ops executed concurrently for non-overlapping iteration ranges"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncParallelFor");
  }
  ::llvm::StringRef getName() const override { return "AsyncParallelFor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<async::AsyncDialect>();

  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncParallelForBase<DerivedT>)

  AsyncParallelForBase(const AsyncParallelForOptions &options) : AsyncParallelForBase() {
    asyncDispatch = options.asyncDispatch;
    numWorkerThreads = options.numWorkerThreads;
    minTaskSize = options.minTaskSize;
  }
protected:
  ::mlir::Pass::Option<bool> asyncDispatch{*this, "async-dispatch", ::llvm::cl::desc("Dispatch async compute tasks using recursive work splitting. If `false` async compute tasks will be launched using simple for loop in the caller thread."), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int32_t> numWorkerThreads{*this, "num-workers", ::llvm::cl::desc("The number of available workers to execute async operations. If `-1` the value will be retrieved from the runtime."), ::llvm::cl::init(8)};
  ::mlir::Pass::Option<int32_t> minTaskSize{*this, "min-task-size", ::llvm::cl::desc("The minimum task size for sharding parallel operation."), ::llvm::cl::init(1000)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ASYNCPARALLELFOR
#endif // GEN_PASS_DEF_ASYNCPARALLELFOR

//===----------------------------------------------------------------------===//
// AsyncRuntimePolicyBasedRefCounting
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING
#undef GEN_PASS_DECL_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING
#endif // GEN_PASS_DECL_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING
#ifdef GEN_PASS_DEF_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING
namespace impl {

template <typename DerivedT>
class AsyncRuntimePolicyBasedRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimePolicyBasedRefCountingBase;

  AsyncRuntimePolicyBasedRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimePolicyBasedRefCountingBase(const AsyncRuntimePolicyBasedRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-policy-based-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-policy-based-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Policy based reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimePolicyBasedRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimePolicyBasedRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncRuntimePolicyBasedRefCountingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING
#endif // GEN_PASS_DEF_ASYNCRUNTIMEPOLICYBASEDREFCOUNTING

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCounting
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTING
#undef GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTING
#endif // GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTING
#ifdef GEN_PASS_DEF_ASYNCRUNTIMEREFCOUNTING
namespace impl {

template <typename DerivedT>
class AsyncRuntimeRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingBase;

  AsyncRuntimeRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingBase(const AsyncRuntimeRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Automatic reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncRuntimeRefCountingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ASYNCRUNTIMEREFCOUNTING
#endif // GEN_PASS_DEF_ASYNCRUNTIMEREFCOUNTING

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCountingOpt
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTINGOPT
#undef GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTINGOPT
#endif // GEN_PASS_DECL_ASYNCRUNTIMEREFCOUNTINGOPT
#ifdef GEN_PASS_DEF_ASYNCRUNTIMEREFCOUNTINGOPT
namespace impl {

template <typename DerivedT>
class AsyncRuntimeRefCountingOptBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingOptBase;

  AsyncRuntimeRefCountingOptBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingOptBase(const AsyncRuntimeRefCountingOptBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting-opt");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting-opt"; }

  ::llvm::StringRef getDescription() const override { return "Optimize automatic reference counting operations for theAsync runtime by removing redundant operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCountingOpt");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCountingOpt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncRuntimeRefCountingOptBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ASYNCRUNTIMEREFCOUNTINGOPT
#endif // GEN_PASS_DEF_ASYNCRUNTIMEREFCOUNTINGOPT

//===----------------------------------------------------------------------===//
// AsyncToAsyncRuntime
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ASYNCTOASYNCRUNTIME
#undef GEN_PASS_DECL_ASYNCTOASYNCRUNTIME
#endif // GEN_PASS_DECL_ASYNCTOASYNCRUNTIME
#ifdef GEN_PASS_DEF_ASYNCTOASYNCRUNTIME
namespace impl {

template <typename DerivedT>
class AsyncToAsyncRuntimeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncToAsyncRuntimeBase;

  AsyncToAsyncRuntimeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncToAsyncRuntimeBase(const AsyncToAsyncRuntimeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-to-async-runtime");
  }
  ::llvm::StringRef getArgument() const override { return "async-to-async-runtime"; }

  ::llvm::StringRef getDescription() const override { return "Lower all high level async operations (e.g. async.execute) tothe explicit async.runtime and async.coro operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncToAsyncRuntime");
  }
  ::llvm::StringRef getName() const override { return "AsyncToAsyncRuntime"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncToAsyncRuntimeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ASYNCTOASYNCRUNTIME
#endif // GEN_PASS_DEF_ASYNCTOASYNCRUNTIME
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AsyncFuncToAsyncRuntime Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncFuncToAsyncRuntime() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncFuncToAsyncRuntimePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAsyncFuncToAsyncRuntimePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncFuncToAsyncRuntimePass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncParallelFor Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncParallelFor() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncParallelForPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAsyncParallelForPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncParallelForPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimePolicyBasedRefCounting Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimePolicyBasedRefCounting() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimePolicyBasedRefCountingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAsyncRuntimePolicyBasedRefCountingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimePolicyBasedRefCountingPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCounting Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimeRefCounting() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAsyncRuntimeRefCountingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCountingOpt Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimeRefCountingOpt() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingOptPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAsyncRuntimeRefCountingOptPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingOptPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncToAsyncRuntime Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncToAsyncRuntime() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncToAsyncRuntimePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAsyncToAsyncRuntimePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncToAsyncRuntimePass();
  });
}

//===----------------------------------------------------------------------===//
// Async Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncPasses() {
  registerAsyncFuncToAsyncRuntime();
  registerAsyncParallelFor();
  registerAsyncRuntimePolicyBasedRefCounting();
  registerAsyncRuntimeRefCounting();
  registerAsyncRuntimeRefCountingOpt();
  registerAsyncToAsyncRuntime();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AsyncFuncToAsyncRuntimeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncFuncToAsyncRuntimeBase;

  AsyncFuncToAsyncRuntimeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncFuncToAsyncRuntimeBase(const AsyncFuncToAsyncRuntimeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-func-to-async-runtime");
  }
  ::llvm::StringRef getArgument() const override { return "async-func-to-async-runtime"; }

  ::llvm::StringRef getDescription() const override { return "Lower async.func operations to the explicit async.runtime andasync.coro operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncFuncToAsyncRuntime");
  }
  ::llvm::StringRef getName() const override { return "AsyncFuncToAsyncRuntime"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncFuncToAsyncRuntimeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AsyncParallelForBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncParallelForBase;

  AsyncParallelForBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncParallelForBase(const AsyncParallelForBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-parallel-for");
  }
  ::llvm::StringRef getArgument() const override { return "async-parallel-for"; }

  ::llvm::StringRef getDescription() const override { return "Convert scf.parallel operations to multiple async compute ops executed concurrently for non-overlapping iteration ranges"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncParallelFor");
  }
  ::llvm::StringRef getName() const override { return "AsyncParallelFor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<async::AsyncDialect>();

  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncParallelForBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> asyncDispatch{*this, "async-dispatch", ::llvm::cl::desc("Dispatch async compute tasks using recursive work splitting. If `false` async compute tasks will be launched using simple for loop in the caller thread."), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int32_t> numWorkerThreads{*this, "num-workers", ::llvm::cl::desc("The number of available workers to execute async operations. If `-1` the value will be retrieved from the runtime."), ::llvm::cl::init(8)};
  ::mlir::Pass::Option<int32_t> minTaskSize{*this, "min-task-size", ::llvm::cl::desc("The minimum task size for sharding parallel operation."), ::llvm::cl::init(1000)};
};

template <typename DerivedT>
class AsyncRuntimePolicyBasedRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimePolicyBasedRefCountingBase;

  AsyncRuntimePolicyBasedRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimePolicyBasedRefCountingBase(const AsyncRuntimePolicyBasedRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-policy-based-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-policy-based-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Policy based reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimePolicyBasedRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimePolicyBasedRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncRuntimePolicyBasedRefCountingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AsyncRuntimeRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingBase;

  AsyncRuntimeRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingBase(const AsyncRuntimeRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Automatic reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncRuntimeRefCountingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AsyncRuntimeRefCountingOptBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingOptBase;

  AsyncRuntimeRefCountingOptBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingOptBase(const AsyncRuntimeRefCountingOptBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting-opt");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting-opt"; }

  ::llvm::StringRef getDescription() const override { return "Optimize automatic reference counting operations for theAsync runtime by removing redundant operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCountingOpt");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCountingOpt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncRuntimeRefCountingOptBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AsyncToAsyncRuntimeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncToAsyncRuntimeBase;

  AsyncToAsyncRuntimeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncToAsyncRuntimeBase(const AsyncToAsyncRuntimeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-to-async-runtime");
  }
  ::llvm::StringRef getArgument() const override { return "async-to-async-runtime"; }

  ::llvm::StringRef getDescription() const override { return "Lower all high level async operations (e.g. async.execute) tothe explicit async.runtime and async.coro operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncToAsyncRuntime");
  }
  ::llvm::StringRef getName() const override { return "AsyncToAsyncRuntime"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AsyncToAsyncRuntimeBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
