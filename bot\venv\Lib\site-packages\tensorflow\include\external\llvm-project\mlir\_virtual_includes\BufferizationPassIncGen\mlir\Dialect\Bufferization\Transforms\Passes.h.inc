/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_BUFFERDEALLOCATION
#define GEN_PASS_DECL_BUFFERHOISTING
#define GEN_PASS_DECL_BUFFERLOOPHOISTING
#define GEN_PASS_DECL_BUFFERRESULTSTOOUTPARAMS
#define GEN_PASS_DECL_BUFFERIZATIONBUFFERIZE
#define GEN_PASS_DECL_DROPEQUIVALENTBUFFERRESULTS
#define GEN_PASS_DECL_EMPTYTENSORELIMINATION
#define GEN_PASS_DECL_EMPTYTENSORTOALLOCTENSOR
#define GEN_PASS_DECL_FINALIZINGBUFFERIZE
#define GEN_PASS_DECL_ONESHOTBUFFERIZE
#define GEN_PASS_DECL_PROMOTEBUFFERSTOSTACK
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// BufferDeallocation
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BUFFERDEALLOCATION
#undef GEN_PASS_DECL_BUFFERDEALLOCATION
#endif // GEN_PASS_DECL_BUFFERDEALLOCATION
#ifdef GEN_PASS_DEF_BUFFERDEALLOCATION
namespace impl {

template <typename DerivedT>
class BufferDeallocationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferDeallocationBase;

  BufferDeallocationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferDeallocationBase(const BufferDeallocationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-deallocation");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-deallocation"; }

  ::llvm::StringRef getDescription() const override { return "Adds all required dealloc operations for all allocations in the input program"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferDeallocation");
  }
  ::llvm::StringRef getName() const override { return "BufferDeallocation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferDeallocationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BUFFERDEALLOCATION
#endif // GEN_PASS_DEF_BUFFERDEALLOCATION

//===----------------------------------------------------------------------===//
// BufferHoisting
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BUFFERHOISTING
#undef GEN_PASS_DECL_BUFFERHOISTING
#endif // GEN_PASS_DECL_BUFFERHOISTING
#ifdef GEN_PASS_DEF_BUFFERHOISTING
namespace impl {

template <typename DerivedT>
class BufferHoistingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferHoistingBase;

  BufferHoistingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferHoistingBase(const BufferHoistingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them into common dominators and out of nested regions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferHoistingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BUFFERHOISTING
#endif // GEN_PASS_DEF_BUFFERHOISTING

//===----------------------------------------------------------------------===//
// BufferLoopHoisting
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BUFFERLOOPHOISTING
#undef GEN_PASS_DECL_BUFFERLOOPHOISTING
#endif // GEN_PASS_DECL_BUFFERLOOPHOISTING
#ifdef GEN_PASS_DEF_BUFFERLOOPHOISTING
namespace impl {

template <typename DerivedT>
class BufferLoopHoistingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferLoopHoistingBase;

  BufferLoopHoistingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferLoopHoistingBase(const BufferLoopHoistingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-loop-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-loop-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them out of loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferLoopHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferLoopHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferLoopHoistingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BUFFERLOOPHOISTING
#endif // GEN_PASS_DEF_BUFFERLOOPHOISTING

//===----------------------------------------------------------------------===//
// BufferResultsToOutParams
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BUFFERRESULTSTOOUTPARAMS
#undef GEN_PASS_DECL_BUFFERRESULTSTOOUTPARAMS
#endif // GEN_PASS_DECL_BUFFERRESULTSTOOUTPARAMS
#ifdef GEN_PASS_DEF_BUFFERRESULTSTOOUTPARAMS
namespace impl {

template <typename DerivedT>
class BufferResultsToOutParamsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = BufferResultsToOutParamsBase;

  BufferResultsToOutParamsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferResultsToOutParamsBase(const BufferResultsToOutParamsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-results-to-out-params");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-results-to-out-params"; }

  ::llvm::StringRef getDescription() const override { return "Converts memref-typed function results to out-params"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferResultsToOutParams");
  }
  ::llvm::StringRef getName() const override { return "BufferResultsToOutParams"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferResultsToOutParamsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BUFFERRESULTSTOOUTPARAMS
#endif // GEN_PASS_DEF_BUFFERRESULTSTOOUTPARAMS

//===----------------------------------------------------------------------===//
// BufferizationBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BUFFERIZATIONBUFFERIZE
#undef GEN_PASS_DECL_BUFFERIZATIONBUFFERIZE
#endif // GEN_PASS_DECL_BUFFERIZATIONBUFFERIZE
#ifdef GEN_PASS_DEF_BUFFERIZATIONBUFFERIZE
namespace impl {

template <typename DerivedT>
class BufferizationBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferizationBufferizeBase;

  BufferizationBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferizationBufferizeBase(const BufferizationBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("bufferization-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "bufferization-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the `bufferization` dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferizationBufferize");
  }
  ::llvm::StringRef getName() const override { return "BufferizationBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferizationBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BUFFERIZATIONBUFFERIZE
#endif // GEN_PASS_DEF_BUFFERIZATIONBUFFERIZE

//===----------------------------------------------------------------------===//
// DropEquivalentBufferResults
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DROPEQUIVALENTBUFFERRESULTS
#undef GEN_PASS_DECL_DROPEQUIVALENTBUFFERRESULTS
#endif // GEN_PASS_DECL_DROPEQUIVALENTBUFFERRESULTS
#ifdef GEN_PASS_DEF_DROPEQUIVALENTBUFFERRESULTS
namespace impl {

template <typename DerivedT>
class DropEquivalentBufferResultsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DropEquivalentBufferResultsBase;

  DropEquivalentBufferResultsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DropEquivalentBufferResultsBase(const DropEquivalentBufferResultsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("drop-equivalent-buffer-results");
  }
  ::llvm::StringRef getArgument() const override { return "drop-equivalent-buffer-results"; }

  ::llvm::StringRef getDescription() const override { return "Remove MemRef return values that are equivalent to a bbArg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropEquivalentBufferResults");
  }
  ::llvm::StringRef getName() const override { return "DropEquivalentBufferResults"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DropEquivalentBufferResultsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DROPEQUIVALENTBUFFERRESULTS
#endif // GEN_PASS_DEF_DROPEQUIVALENTBUFFERRESULTS

//===----------------------------------------------------------------------===//
// EmptyTensorElimination
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EMPTYTENSORELIMINATION
#undef GEN_PASS_DECL_EMPTYTENSORELIMINATION
#endif // GEN_PASS_DECL_EMPTYTENSORELIMINATION
#ifdef GEN_PASS_DEF_EMPTYTENSORELIMINATION
namespace impl {

template <typename DerivedT>
class EmptyTensorEliminationBase : public ::mlir::OperationPass<> {
public:
  using Base = EmptyTensorEliminationBase;

  EmptyTensorEliminationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  EmptyTensorEliminationBase(const EmptyTensorEliminationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("eliminate-empty-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "eliminate-empty-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Try to eliminate all tensor.empty ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmptyTensorElimination");
  }
  ::llvm::StringRef getName() const override { return "EmptyTensorElimination"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmptyTensorEliminationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EMPTYTENSORELIMINATION
#endif // GEN_PASS_DEF_EMPTYTENSORELIMINATION

//===----------------------------------------------------------------------===//
// EmptyTensorToAllocTensor
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EMPTYTENSORTOALLOCTENSOR
#undef GEN_PASS_DECL_EMPTYTENSORTOALLOCTENSOR
#endif // GEN_PASS_DECL_EMPTYTENSORTOALLOCTENSOR
#ifdef GEN_PASS_DEF_EMPTYTENSORTOALLOCTENSOR
namespace impl {

template <typename DerivedT>
class EmptyTensorToAllocTensorBase : public ::mlir::OperationPass<> {
public:
  using Base = EmptyTensorToAllocTensorBase;

  EmptyTensorToAllocTensorBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  EmptyTensorToAllocTensorBase(const EmptyTensorToAllocTensorBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("empty-tensor-to-alloc-tensor");
  }
  ::llvm::StringRef getArgument() const override { return "empty-tensor-to-alloc-tensor"; }

  ::llvm::StringRef getDescription() const override { return "Replace all empty ops by alloc_tensor ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmptyTensorToAllocTensor");
  }
  ::llvm::StringRef getName() const override { return "EmptyTensorToAllocTensor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmptyTensorToAllocTensorBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EMPTYTENSORTOALLOCTENSOR
#endif // GEN_PASS_DEF_EMPTYTENSORTOALLOCTENSOR

//===----------------------------------------------------------------------===//
// FinalizingBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FINALIZINGBUFFERIZE
#undef GEN_PASS_DECL_FINALIZINGBUFFERIZE
#endif // GEN_PASS_DECL_FINALIZINGBUFFERIZE
#ifdef GEN_PASS_DEF_FINALIZINGBUFFERIZE
namespace impl {

template <typename DerivedT>
class FinalizingBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = FinalizingBufferizeBase;

  FinalizingBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizingBufferizeBase(const FinalizingBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalizing-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "finalizing-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Finalize a partial bufferization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizingBufferize");
  }
  ::llvm::StringRef getName() const override { return "FinalizingBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalizingBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FINALIZINGBUFFERIZE
#endif // GEN_PASS_DEF_FINALIZINGBUFFERIZE

//===----------------------------------------------------------------------===//
// OneShotBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ONESHOTBUFFERIZE
struct OneShotBufferizeOptions {
  bool allowReturnAllocs = false;
  bool allowUnknownOps = false;
  unsigned analysisFuzzerSeed = 0;
  std::string analysisHeuristic = "bottom-up";
  bool bufferizeFunctionBoundaries = 0;
  bool copyBeforeWrite = false;
  bool createDeallocs = true;
  ::llvm::ArrayRef<std::string> dialectFilter;
  ::llvm::ArrayRef<std::string> noAnalysisFuncFilter;
  std::string functionBoundaryTypeConversion = "infer-layout-map";
  bool mustInferMemorySpace = false;
  bool testAnalysisOnly = false;
  bool printConflicts = false;
  std::string unknownTypeConversion = "fully-dynamic-layout-map";
};
#undef GEN_PASS_DECL_ONESHOTBUFFERIZE
#endif // GEN_PASS_DECL_ONESHOTBUFFERIZE
#ifdef GEN_PASS_DEF_ONESHOTBUFFERIZE
namespace impl {

template <typename DerivedT>
class OneShotBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OneShotBufferizeBase;

  OneShotBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OneShotBufferizeBase(const OneShotBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("one-shot-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "one-shot-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "One-Shot Bufferize"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OneShotBufferize");
  }
  ::llvm::StringRef getName() const override { return "OneShotBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OneShotBufferizeBase<DerivedT>)

  OneShotBufferizeBase(const OneShotBufferizeOptions &options) : OneShotBufferizeBase() {
    allowReturnAllocs = options.allowReturnAllocs;
    allowUnknownOps = options.allowUnknownOps;
    analysisFuzzerSeed = options.analysisFuzzerSeed;
    analysisHeuristic = options.analysisHeuristic;
    bufferizeFunctionBoundaries = options.bufferizeFunctionBoundaries;
    copyBeforeWrite = options.copyBeforeWrite;
    createDeallocs = options.createDeallocs;
    dialectFilter = options.dialectFilter;
    noAnalysisFuncFilter = options.noAnalysisFuncFilter;
    functionBoundaryTypeConversion = options.functionBoundaryTypeConversion;
    mustInferMemorySpace = options.mustInferMemorySpace;
    testAnalysisOnly = options.testAnalysisOnly;
    printConflicts = options.printConflicts;
    unknownTypeConversion = options.unknownTypeConversion;
  }
protected:
  ::mlir::Pass::Option<bool> allowReturnAllocs{*this, "allow-return-allocs", ::llvm::cl::desc("Allows returning/yielding new allocations from a block."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowUnknownOps{*this, "allow-unknown-ops", ::llvm::cl::desc("Allows unknown (not bufferizable) ops in the input IR."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> analysisFuzzerSeed{*this, "analysis-fuzzer-seed", ::llvm::cl::desc("Test only: Analyze ops in random order with a given seed (fuzzer)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<std::string> analysisHeuristic{*this, "analysis-heuristic", ::llvm::cl::desc("Heuristic that control the IR traversal during analysis"), ::llvm::cl::init("bottom-up")};
  ::mlir::Pass::Option<bool> bufferizeFunctionBoundaries{*this, "bufferize-function-boundaries", ::llvm::cl::desc("Bufferize function boundaries (experimental)."), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> copyBeforeWrite{*this, "copy-before-write", ::llvm::cl::desc("Skip the analysis. Make a buffer copy on every write."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> createDeallocs{*this, "create-deallocs", ::llvm::cl::desc("Specify if buffers should be deallocated. For compatibility with core bufferization passes."), ::llvm::cl::init(true)};
  ::mlir::Pass::ListOption<std::string> dialectFilter{*this, "dialect-filter", ::llvm::cl::desc("Restrict bufferization to ops from these dialects.")};
  ::mlir::Pass::ListOption<std::string> noAnalysisFuncFilter{*this, "no-analysis-func-filter", ::llvm::cl::desc("Skip analysis of functions with these symbol names.Set copyBeforeWrite to true when bufferizing them.")};
  ::mlir::Pass::Option<std::string> functionBoundaryTypeConversion{*this, "function-boundary-type-conversion", ::llvm::cl::desc("Controls layout maps when bufferizing function signatures."), ::llvm::cl::init("infer-layout-map")};
  ::mlir::Pass::Option<bool> mustInferMemorySpace{*this, "must-infer-memory-space", ::llvm::cl::desc("The memory space of an memref types must always be inferred. If unset, a default memory space of 0 is used otherwise."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> testAnalysisOnly{*this, "test-analysis-only", ::llvm::cl::desc("Test only: Only run inplaceability analysis and annotate IR"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printConflicts{*this, "print-conflicts", ::llvm::cl::desc("Test only: Annotate IR with RaW conflicts. Requires test-analysis-only."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> unknownTypeConversion{*this, "unknown-type-conversion", ::llvm::cl::desc("Controls layout maps for non-inferrable memref types."), ::llvm::cl::init("fully-dynamic-layout-map")};
  ::mlir::Pass::Statistic numBufferAlloc{this, "num-buffer-alloc", "Number of buffer allocations"};
  ::mlir::Pass::Statistic numBufferDealloc{this, "num-buffer-dealloc", "Number of buffer deallocations"};
  ::mlir::Pass::Statistic numTensorInPlace{this, "num-tensor-in-place", "Number of in-place tensor OpOperands"};
  ::mlir::Pass::Statistic numTensorOutOfPlace{this, "num-tensor-out-of-place", "Number of out-of-place tensor OpOperands"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ONESHOTBUFFERIZE
#endif // GEN_PASS_DEF_ONESHOTBUFFERIZE

//===----------------------------------------------------------------------===//
// PromoteBuffersToStack
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PROMOTEBUFFERSTOSTACK
struct PromoteBuffersToStackOptions {
  unsigned maxAllocSizeInBytes = 1024;
  unsigned maxRankOfAllocatedMemRef = 1;
};
#undef GEN_PASS_DECL_PROMOTEBUFFERSTOSTACK
#endif // GEN_PASS_DECL_PROMOTEBUFFERSTOSTACK
#ifdef GEN_PASS_DEF_PROMOTEBUFFERSTOSTACK
namespace impl {

template <typename DerivedT>
class PromoteBuffersToStackBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = PromoteBuffersToStackBase;

  PromoteBuffersToStackBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PromoteBuffersToStackBase(const PromoteBuffersToStackBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("promote-buffers-to-stack");
  }
  ::llvm::StringRef getArgument() const override { return "promote-buffers-to-stack"; }

  ::llvm::StringRef getDescription() const override { return "Promotes heap-based allocations to automatically managed stack-based allocations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PromoteBuffersToStack");
  }
  ::llvm::StringRef getName() const override { return "PromoteBuffersToStack"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PromoteBuffersToStackBase<DerivedT>)

  PromoteBuffersToStackBase(const PromoteBuffersToStackOptions &options) : PromoteBuffersToStackBase() {
    maxAllocSizeInBytes = options.maxAllocSizeInBytes;
    maxRankOfAllocatedMemRef = options.maxRankOfAllocatedMemRef;
  }
protected:
  ::mlir::Pass::Option<unsigned> maxAllocSizeInBytes{*this, "max-alloc-size-in-bytes", ::llvm::cl::desc("Maximal size in bytes to promote allocations to stack."), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> maxRankOfAllocatedMemRef{*this, "max-rank-of-allocated-memref", ::llvm::cl::desc("Maximal memref rank to promote dynamic buffers."), ::llvm::cl::init(1)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PROMOTEBUFFERSTOSTACK
#endif // GEN_PASS_DEF_PROMOTEBUFFERSTOSTACK
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// BufferDeallocation Registration
//===----------------------------------------------------------------------===//

inline void registerBufferDeallocation() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferDeallocationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBufferDeallocationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferDeallocationPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerBufferHoisting() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferHoistingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBufferHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferLoopHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerBufferLoopHoisting() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferLoopHoistingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBufferLoopHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferLoopHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferResultsToOutParams Registration
//===----------------------------------------------------------------------===//

inline void registerBufferResultsToOutParams() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferResultsToOutParamsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBufferResultsToOutParamsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferResultsToOutParamsPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferizationBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerBufferizationBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferizationBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBufferizationBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferizationBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// DropEquivalentBufferResults Registration
//===----------------------------------------------------------------------===//

inline void registerDropEquivalentBufferResults() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createDropEquivalentBufferResultsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDropEquivalentBufferResultsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createDropEquivalentBufferResultsPass();
  });
}

//===----------------------------------------------------------------------===//
// EmptyTensorElimination Registration
//===----------------------------------------------------------------------===//

inline void registerEmptyTensorElimination() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createEmptyTensorEliminationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEmptyTensorEliminationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createEmptyTensorEliminationPass();
  });
}

//===----------------------------------------------------------------------===//
// EmptyTensorToAllocTensor Registration
//===----------------------------------------------------------------------===//

inline void registerEmptyTensorToAllocTensor() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createEmptyTensorToAllocTensorPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEmptyTensorToAllocTensorPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createEmptyTensorToAllocTensorPass();
  });
}

//===----------------------------------------------------------------------===//
// FinalizingBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerFinalizingBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createFinalizingBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFinalizingBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createFinalizingBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// OneShotBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerOneShotBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createOneShotBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOneShotBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createOneShotBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// PromoteBuffersToStack Registration
//===----------------------------------------------------------------------===//

inline void registerPromoteBuffersToStack() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createPromoteBuffersToStackPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPromoteBuffersToStackPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createPromoteBuffersToStackPass();
  });
}

//===----------------------------------------------------------------------===//
// Bufferization Registration
//===----------------------------------------------------------------------===//

inline void registerBufferizationPasses() {
  registerBufferDeallocation();
  registerBufferHoisting();
  registerBufferLoopHoisting();
  registerBufferResultsToOutParams();
  registerBufferizationBufferize();
  registerDropEquivalentBufferResults();
  registerEmptyTensorElimination();
  registerEmptyTensorToAllocTensor();
  registerFinalizingBufferize();
  registerOneShotBufferize();
  registerPromoteBuffersToStack();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class BufferDeallocationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferDeallocationBase;

  BufferDeallocationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferDeallocationBase(const BufferDeallocationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-deallocation");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-deallocation"; }

  ::llvm::StringRef getDescription() const override { return "Adds all required dealloc operations for all allocations in the input program"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferDeallocation");
  }
  ::llvm::StringRef getName() const override { return "BufferDeallocation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferDeallocationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class BufferHoistingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferHoistingBase;

  BufferHoistingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferHoistingBase(const BufferHoistingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them into common dominators and out of nested regions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferHoistingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class BufferLoopHoistingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferLoopHoistingBase;

  BufferLoopHoistingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferLoopHoistingBase(const BufferLoopHoistingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-loop-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-loop-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them out of loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferLoopHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferLoopHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferLoopHoistingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class BufferResultsToOutParamsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = BufferResultsToOutParamsBase;

  BufferResultsToOutParamsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferResultsToOutParamsBase(const BufferResultsToOutParamsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-results-to-out-params");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-results-to-out-params"; }

  ::llvm::StringRef getDescription() const override { return "Converts memref-typed function results to out-params"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferResultsToOutParams");
  }
  ::llvm::StringRef getName() const override { return "BufferResultsToOutParams"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferResultsToOutParamsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class BufferizationBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferizationBufferizeBase;

  BufferizationBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferizationBufferizeBase(const BufferizationBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("bufferization-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "bufferization-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the `bufferization` dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferizationBufferize");
  }
  ::llvm::StringRef getName() const override { return "BufferizationBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferizationBufferizeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DropEquivalentBufferResultsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DropEquivalentBufferResultsBase;

  DropEquivalentBufferResultsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DropEquivalentBufferResultsBase(const DropEquivalentBufferResultsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("drop-equivalent-buffer-results");
  }
  ::llvm::StringRef getArgument() const override { return "drop-equivalent-buffer-results"; }

  ::llvm::StringRef getDescription() const override { return "Remove MemRef return values that are equivalent to a bbArg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropEquivalentBufferResults");
  }
  ::llvm::StringRef getName() const override { return "DropEquivalentBufferResults"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DropEquivalentBufferResultsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class EmptyTensorEliminationBase : public ::mlir::OperationPass<> {
public:
  using Base = EmptyTensorEliminationBase;

  EmptyTensorEliminationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  EmptyTensorEliminationBase(const EmptyTensorEliminationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("eliminate-empty-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "eliminate-empty-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Try to eliminate all tensor.empty ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmptyTensorElimination");
  }
  ::llvm::StringRef getName() const override { return "EmptyTensorElimination"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmptyTensorEliminationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class EmptyTensorToAllocTensorBase : public ::mlir::OperationPass<> {
public:
  using Base = EmptyTensorToAllocTensorBase;

  EmptyTensorToAllocTensorBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  EmptyTensorToAllocTensorBase(const EmptyTensorToAllocTensorBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("empty-tensor-to-alloc-tensor");
  }
  ::llvm::StringRef getArgument() const override { return "empty-tensor-to-alloc-tensor"; }

  ::llvm::StringRef getDescription() const override { return "Replace all empty ops by alloc_tensor ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmptyTensorToAllocTensor");
  }
  ::llvm::StringRef getName() const override { return "EmptyTensorToAllocTensor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmptyTensorToAllocTensorBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FinalizingBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = FinalizingBufferizeBase;

  FinalizingBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizingBufferizeBase(const FinalizingBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalizing-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "finalizing-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Finalize a partial bufferization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizingBufferize");
  }
  ::llvm::StringRef getName() const override { return "FinalizingBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalizingBufferizeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class OneShotBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OneShotBufferizeBase;

  OneShotBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OneShotBufferizeBase(const OneShotBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("one-shot-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "one-shot-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "One-Shot Bufferize"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OneShotBufferize");
  }
  ::llvm::StringRef getName() const override { return "OneShotBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OneShotBufferizeBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> allowReturnAllocs{*this, "allow-return-allocs", ::llvm::cl::desc("Allows returning/yielding new allocations from a block."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowUnknownOps{*this, "allow-unknown-ops", ::llvm::cl::desc("Allows unknown (not bufferizable) ops in the input IR."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> analysisFuzzerSeed{*this, "analysis-fuzzer-seed", ::llvm::cl::desc("Test only: Analyze ops in random order with a given seed (fuzzer)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<std::string> analysisHeuristic{*this, "analysis-heuristic", ::llvm::cl::desc("Heuristic that control the IR traversal during analysis"), ::llvm::cl::init("bottom-up")};
  ::mlir::Pass::Option<bool> bufferizeFunctionBoundaries{*this, "bufferize-function-boundaries", ::llvm::cl::desc("Bufferize function boundaries (experimental)."), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> copyBeforeWrite{*this, "copy-before-write", ::llvm::cl::desc("Skip the analysis. Make a buffer copy on every write."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> createDeallocs{*this, "create-deallocs", ::llvm::cl::desc("Specify if buffers should be deallocated. For compatibility with core bufferization passes."), ::llvm::cl::init(true)};
  ::mlir::Pass::ListOption<std::string> dialectFilter{*this, "dialect-filter", ::llvm::cl::desc("Restrict bufferization to ops from these dialects.")};
  ::mlir::Pass::ListOption<std::string> noAnalysisFuncFilter{*this, "no-analysis-func-filter", ::llvm::cl::desc("Skip analysis of functions with these symbol names.Set copyBeforeWrite to true when bufferizing them.")};
  ::mlir::Pass::Option<std::string> functionBoundaryTypeConversion{*this, "function-boundary-type-conversion", ::llvm::cl::desc("Controls layout maps when bufferizing function signatures."), ::llvm::cl::init("infer-layout-map")};
  ::mlir::Pass::Option<bool> mustInferMemorySpace{*this, "must-infer-memory-space", ::llvm::cl::desc("The memory space of an memref types must always be inferred. If unset, a default memory space of 0 is used otherwise."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> testAnalysisOnly{*this, "test-analysis-only", ::llvm::cl::desc("Test only: Only run inplaceability analysis and annotate IR"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printConflicts{*this, "print-conflicts", ::llvm::cl::desc("Test only: Annotate IR with RaW conflicts. Requires test-analysis-only."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> unknownTypeConversion{*this, "unknown-type-conversion", ::llvm::cl::desc("Controls layout maps for non-inferrable memref types."), ::llvm::cl::init("fully-dynamic-layout-map")};
  ::mlir::Pass::Statistic numBufferAlloc{this, "num-buffer-alloc", "Number of buffer allocations"};
  ::mlir::Pass::Statistic numBufferDealloc{this, "num-buffer-dealloc", "Number of buffer deallocations"};
  ::mlir::Pass::Statistic numTensorInPlace{this, "num-tensor-in-place", "Number of in-place tensor OpOperands"};
  ::mlir::Pass::Statistic numTensorOutOfPlace{this, "num-tensor-out-of-place", "Number of out-of-place tensor OpOperands"};
};

template <typename DerivedT>
class PromoteBuffersToStackBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = PromoteBuffersToStackBase;

  PromoteBuffersToStackBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PromoteBuffersToStackBase(const PromoteBuffersToStackBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("promote-buffers-to-stack");
  }
  ::llvm::StringRef getArgument() const override { return "promote-buffers-to-stack"; }

  ::llvm::StringRef getDescription() const override { return "Promotes heap-based allocations to automatically managed stack-based allocations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PromoteBuffersToStack");
  }
  ::llvm::StringRef getName() const override { return "PromoteBuffersToStack"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PromoteBuffersToStackBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> maxAllocSizeInBytes{*this, "max-alloc-size-in-bytes", ::llvm::cl::desc("Maximal size in bytes to promote allocations to stack."), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> maxRankOfAllocatedMemRef{*this, "max-rank-of-allocated-memref", ::llvm::cl::desc("Maximal memref rank to promote dynamic buffers."), ::llvm::cl::init(1)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
