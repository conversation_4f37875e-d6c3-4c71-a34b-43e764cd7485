
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorPasses(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorPostSparsificationRewrite(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorPostSparsificationRewrite(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorPreSparsificationRewrite(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorPreSparsificationRewrite(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparseB<PERSON><PERSON><PERSON><PERSON><PERSON>(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparseBufferRewrite(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparseGPUCodegen(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparseGPUCodegen(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparseTensorCodegen(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparseTensorCodegen(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparseTensorConversionPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparseTensorConversionPass(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparseVectorization(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparseVectorization(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparsificationPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparsificationPass(void);


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorStorageSpecifierToLLVM(void);
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorStorageSpecifierToLLVM(void);



#ifdef __cplusplus
}
#endif
