/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_OUTLINESHAPECOMPUTATION
#define GEN_PASS_DECL_REMOVESHAPECONSTRAINTS
#define GEN_PASS_DECL_SHAPEBUFFERIZE
#define GEN_PASS_DECL_SHAPETOSHAPELOWERING
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// OutlineShapeComputation
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_OUTLINESHAPECOMPUTATION
#undef GEN_PASS_DECL_OUTLINESHAPECOMPUTATION
#endif // GEN_PASS_DECL_OUTLINESHAPECOMPUTATION
#ifdef GEN_PASS_DEF_OUTLINESHAPECOMPUTATION
namespace impl {

template <typename DerivedT>
class OutlineShapeComputationBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OutlineShapeComputationBase;

  OutlineShapeComputationBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OutlineShapeComputationBase(const OutlineShapeComputationBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("outline-shape-computation");
  }
  ::llvm::StringRef getArgument() const override { return "outline-shape-computation"; }

  ::llvm::StringRef getDescription() const override { return "Using shape.func to preserve shape computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OutlineShapeComputation");
  }
  ::llvm::StringRef getName() const override { return "OutlineShapeComputation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<shape::ShapeDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OutlineShapeComputationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_OUTLINESHAPECOMPUTATION
#endif // GEN_PASS_DEF_OUTLINESHAPECOMPUTATION

//===----------------------------------------------------------------------===//
// RemoveShapeConstraints
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_REMOVESHAPECONSTRAINTS
#undef GEN_PASS_DECL_REMOVESHAPECONSTRAINTS
#endif // GEN_PASS_DECL_REMOVESHAPECONSTRAINTS
#ifdef GEN_PASS_DEF_REMOVESHAPECONSTRAINTS
namespace impl {

template <typename DerivedT>
class RemoveShapeConstraintsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = RemoveShapeConstraintsBase;

  RemoveShapeConstraintsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveShapeConstraintsBase(const RemoveShapeConstraintsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("remove-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "remove-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Replace all cstr_ ops with a true witness"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "RemoveShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveShapeConstraintsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_REMOVESHAPECONSTRAINTS
#endif // GEN_PASS_DEF_REMOVESHAPECONSTRAINTS

//===----------------------------------------------------------------------===//
// ShapeBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHAPEBUFFERIZE
#undef GEN_PASS_DECL_SHAPEBUFFERIZE
#endif // GEN_PASS_DECL_SHAPEBUFFERIZE
#ifdef GEN_PASS_DEF_SHAPEBUFFERIZE
namespace impl {

template <typename DerivedT>
class ShapeBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeBufferizeBase;

  ShapeBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeBufferizeBase(const ShapeBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "shape-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the shape dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeBufferize");
  }
  ::llvm::StringRef getName() const override { return "ShapeBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SHAPEBUFFERIZE
#endif // GEN_PASS_DEF_SHAPEBUFFERIZE

//===----------------------------------------------------------------------===//
// ShapeToShapeLowering
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHAPETOSHAPELOWERING
#undef GEN_PASS_DECL_SHAPETOSHAPELOWERING
#endif // GEN_PASS_DECL_SHAPETOSHAPELOWERING
#ifdef GEN_PASS_DEF_SHAPETOSHAPELOWERING
namespace impl {

template <typename DerivedT>
class ShapeToShapeLoweringBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeToShapeLoweringBase;

  ShapeToShapeLoweringBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeToShapeLoweringBase(const ShapeToShapeLoweringBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-to-shape-lowering");
  }
  ::llvm::StringRef getArgument() const override { return "shape-to-shape-lowering"; }

  ::llvm::StringRef getDescription() const override { return "Legalize Shape dialect to be convertible to Arith"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeToShapeLowering");
  }
  ::llvm::StringRef getName() const override { return "ShapeToShapeLowering"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeToShapeLoweringBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SHAPETOSHAPELOWERING
#endif // GEN_PASS_DEF_SHAPETOSHAPELOWERING
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// OutlineShapeComputation Registration
//===----------------------------------------------------------------------===//

inline void registerOutlineShapeComputation() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createOutlineShapeComputationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOutlineShapeComputationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createOutlineShapeComputationPass();
  });
}

//===----------------------------------------------------------------------===//
// RemoveShapeConstraints Registration
//===----------------------------------------------------------------------===//

inline void registerRemoveShapeConstraints() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createRemoveShapeConstraintsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerRemoveShapeConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createRemoveShapeConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerShapeBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createShapeBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShapeBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createShapeBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeToShapeLowering Registration
//===----------------------------------------------------------------------===//

inline void registerShapeToShapeLowering() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createShapeToShapeLowering();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShapeToShapeLoweringPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createShapeToShapeLowering();
  });
}

//===----------------------------------------------------------------------===//
// Shape Registration
//===----------------------------------------------------------------------===//

inline void registerShapePasses() {
  registerOutlineShapeComputation();
  registerRemoveShapeConstraints();
  registerShapeBufferize();
  registerShapeToShapeLowering();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class OutlineShapeComputationBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OutlineShapeComputationBase;

  OutlineShapeComputationBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OutlineShapeComputationBase(const OutlineShapeComputationBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("outline-shape-computation");
  }
  ::llvm::StringRef getArgument() const override { return "outline-shape-computation"; }

  ::llvm::StringRef getDescription() const override { return "Using shape.func to preserve shape computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OutlineShapeComputation");
  }
  ::llvm::StringRef getName() const override { return "OutlineShapeComputation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<shape::ShapeDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OutlineShapeComputationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class RemoveShapeConstraintsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = RemoveShapeConstraintsBase;

  RemoveShapeConstraintsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveShapeConstraintsBase(const RemoveShapeConstraintsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("remove-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "remove-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Replace all cstr_ ops with a true witness"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "RemoveShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveShapeConstraintsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ShapeBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeBufferizeBase;

  ShapeBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeBufferizeBase(const ShapeBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "shape-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the shape dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeBufferize");
  }
  ::llvm::StringRef getName() const override { return "ShapeBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeBufferizeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ShapeToShapeLoweringBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeToShapeLoweringBase;

  ShapeToShapeLoweringBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeToShapeLoweringBase(const ShapeToShapeLoweringBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-to-shape-lowering");
  }
  ::llvm::StringRef getArgument() const override { return "shape-to-shape-lowering"; }

  ::llvm::StringRef getDescription() const override { return "Legalize Shape dialect to be convertible to Arith"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeToShapeLowering");
  }
  ::llvm::StringRef getName() const override { return "ShapeToShapeLowering"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeToShapeLoweringBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
