/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// GPU Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterGPUPasses(void) {
  registerGPUPasses();
}

MlirPass mlirCreateGPUGpuAsyncRegionPass(void) {
  return wrap(mlir::createGpuAsyncRegionPass().release());
}
void mlirRegisterGPUGpuAsyncRegionPass(void) {
  registerGpuAsyncRegionPass();
}


MlirPass mlirCreateGPUGpuKernelOutlining(void) {
  return wrap(mlir::createGpuKernelOutliningPass().release());
}
void mlirRegisterGPUGpuKernelOutlining(void) {
  registerGpuKernelOutlining();
}


MlirPass mlirCreateGPUGpuLaunchSinkIndexComputations(void) {
  return wrap(mlir::createGpuLauchSinkIndexComputationsPass().release());
}
void mlirRegisterGPUGpuLaunchSinkIndexComputations(void) {
  registerGpuLaunchSinkIndexComputations();
}


MlirPass mlirCreateGPUGpuMapParallelLoopsPass(void) {
  return wrap(mlir::createGpuMapParallelLoopsPass().release());
}
void mlirRegisterGPUGpuMapParallelLoopsPass(void) {
  registerGpuMapParallelLoopsPass();
}

