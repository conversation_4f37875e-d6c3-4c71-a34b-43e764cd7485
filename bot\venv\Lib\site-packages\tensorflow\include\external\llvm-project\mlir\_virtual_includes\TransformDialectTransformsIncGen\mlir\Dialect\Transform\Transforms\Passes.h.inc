/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CHECKUSESPASS
#define GEN_PASS_DECL_INFEREFFECTSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// CheckUsesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CHECKUSESPASS
std::unique_ptr<::mlir::Pass> createCheckUsesPass();
#undef GEN_PA<PERSON>_DECL_CHECKUSESPASS
#endif // GEN_PASS_DECL_CHECKUSESPASS
#ifdef GEN_PASS_DEF_CHECKUSESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createCheckUsesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class CheckUsesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CheckUsesPassBase;

  CheckUsesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CheckUsesPassBase(const CheckUsesPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-dialect-check-uses");
  }
  ::llvm::StringRef getArgument() const override { return "transform-dialect-check-uses"; }

  ::llvm::StringRef getDescription() const override { return "warn about potential use-after-free in the transform dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CheckUsesPass");
  }
  ::llvm::StringRef getName() const override { return "CheckUsesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CheckUsesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createCheckUsesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createCheckUsesPass() {
  return impl::createCheckUsesPass();
}
#undef GEN_PASS_DEF_CHECKUSESPASS
#endif // GEN_PASS_DEF_CHECKUSESPASS

//===----------------------------------------------------------------------===//
// InferEffectsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INFEREFFECTSPASS
std::unique_ptr<::mlir::Pass> createInferEffectsPass();
#undef GEN_PASS_DECL_INFEREFFECTSPASS
#endif // GEN_PASS_DECL_INFEREFFECTSPASS
#ifdef GEN_PASS_DEF_INFEREFFECTSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createInferEffectsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class InferEffectsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = InferEffectsPassBase;

  InferEffectsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InferEffectsPassBase(const InferEffectsPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-infer-effects");
  }
  ::llvm::StringRef getArgument() const override { return "transform-infer-effects"; }

  ::llvm::StringRef getDescription() const override { return "infer transform side effects for symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InferEffectsPass");
  }
  ::llvm::StringRef getName() const override { return "InferEffectsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InferEffectsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createInferEffectsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createInferEffectsPass() {
  return impl::createInferEffectsPass();
}
#undef GEN_PASS_DEF_INFEREFFECTSPASS
#endif // GEN_PASS_DEF_INFEREFFECTSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// CheckUsesPass Registration
//===----------------------------------------------------------------------===//

inline void registerCheckUsesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCheckUsesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCheckUsesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCheckUsesPass();
  });
}

//===----------------------------------------------------------------------===//
// InferEffectsPass Registration
//===----------------------------------------------------------------------===//

inline void registerInferEffectsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInferEffectsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInferEffectsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInferEffectsPass();
  });
}

//===----------------------------------------------------------------------===//
// Transform Registration
//===----------------------------------------------------------------------===//

inline void registerTransformPasses() {
  registerCheckUsesPass();
  registerInferEffectsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class CheckUsesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CheckUsesPassBase;

  CheckUsesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CheckUsesPassBase(const CheckUsesPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-dialect-check-uses");
  }
  ::llvm::StringRef getArgument() const override { return "transform-dialect-check-uses"; }

  ::llvm::StringRef getDescription() const override { return "warn about potential use-after-free in the transform dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CheckUsesPass");
  }
  ::llvm::StringRef getName() const override { return "CheckUsesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CheckUsesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class InferEffectsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = InferEffectsPassBase;

  InferEffectsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InferEffectsPassBase(const InferEffectsPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-infer-effects");
  }
  ::llvm::StringRef getArgument() const override { return "transform-infer-effects"; }

  ::llvm::StringRef getDescription() const override { return "infer transform side effects for symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InferEffectsPass");
  }
  ::llvm::StringRef getName() const override { return "InferEffectsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InferEffectsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
