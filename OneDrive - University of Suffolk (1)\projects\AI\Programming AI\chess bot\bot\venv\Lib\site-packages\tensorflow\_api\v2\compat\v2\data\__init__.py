# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""`tf.data.Dataset` API for input pipelines.

See [Importing Data](https://tensorflow.org/guide/data) for an overview.

"""

import sys as _sys

from . import experimental
from tensorflow.python.data.ops.dataset_ops import AUTOTUNE
from tensorflow.python.data.ops.dataset_ops import DatasetSpec
from tensorflow.python.data.ops.dataset_ops import DatasetV2 as Dataset
from tensorflow.python.data.ops.dataset_ops import INFINITE as INFINITE_CARDINALITY
from tensorflow.python.data.ops.dataset_ops import UNKNOWN as UNK<PERSON>OWN_CARDINALITY
from tensorflow.python.data.ops.iterator_ops import IteratorBase as Iterator
from tensorflow.python.data.ops.iterator_ops import IteratorSpec
from tensorflow.python.data.ops.options import Options
from tensorflow.python.data.ops.options import ThreadingOptions
from tensorflow.python.data.ops.readers import FixedLengthRecordDatasetV2 as FixedLengthRecordDataset
from tensorflow.python.data.ops.readers import TFRecordDatasetV2 as TFRecordDataset
from tensorflow.python.data.ops.readers import TextLineDatasetV2 as TextLineDataset