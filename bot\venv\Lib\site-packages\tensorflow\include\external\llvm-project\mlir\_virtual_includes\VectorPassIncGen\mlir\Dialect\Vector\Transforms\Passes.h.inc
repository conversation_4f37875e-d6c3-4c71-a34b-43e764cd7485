/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_LOWERVECTORMASKPASS
#define GEN_PASS_DECL_VECTORBUFFERIZE
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// LowerVectorMaskPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERVECTORMASKPASS
#undef GEN_PASS_DECL_LOWERVECTORMASKPASS
#endif // GEN_PASS_DECL_LOWERVECTORMASKPASS
#ifdef GEN_PASS_DEF_LOWERVECTORMASKPASS
namespace impl {

template <typename DerivedT>
class LowerVectorMaskPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LowerVectorMaskPassBase;

  LowerVectorMaskPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVectorMaskPassBase(const LowerVectorMaskPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-vector-mask");
  }
  ::llvm::StringRef getArgument() const override { return "lower-vector-mask"; }

  ::llvm::StringRef getDescription() const override { return "Lower 'vector.mask' operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVectorMaskPass");
  }
  ::llvm::StringRef getName() const override { return "LowerVectorMaskPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVectorMaskPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOWERVECTORMASKPASS
#endif // GEN_PASS_DEF_LOWERVECTORMASKPASS

//===----------------------------------------------------------------------===//
// VectorBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VECTORBUFFERIZE
#undef GEN_PASS_DECL_VECTORBUFFERIZE
#endif // GEN_PASS_DECL_VECTORBUFFERIZE
#ifdef GEN_PASS_DEF_VECTORBUFFERIZE
namespace impl {

template <typename DerivedT>
class VectorBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = VectorBufferizeBase;

  VectorBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorBufferizeBase(const VectorBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vector-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "vector-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize Vector dialect ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorBufferize");
  }
  ::llvm::StringRef getName() const override { return "VectorBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VectorBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VECTORBUFFERIZE
#endif // GEN_PASS_DEF_VECTORBUFFERIZE
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// LowerVectorMaskPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerVectorMaskPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createLowerVectorMaskPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerVectorMaskPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createLowerVectorMaskPass();
  });
}

//===----------------------------------------------------------------------===//
// VectorBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerVectorBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createVectorBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVectorBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createVectorBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Vector Registration
//===----------------------------------------------------------------------===//

inline void registerVectorPasses() {
  registerLowerVectorMaskPass();
  registerVectorBufferize();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class LowerVectorMaskPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LowerVectorMaskPassBase;

  LowerVectorMaskPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVectorMaskPassBase(const LowerVectorMaskPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-vector-mask");
  }
  ::llvm::StringRef getArgument() const override { return "lower-vector-mask"; }

  ::llvm::StringRef getDescription() const override { return "Lower 'vector.mask' operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVectorMaskPass");
  }
  ::llvm::StringRef getName() const override { return "LowerVectorMaskPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVectorMaskPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VectorBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = VectorBufferizeBase;

  VectorBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorBufferizeBase(const VectorBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vector-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "vector-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize Vector dialect ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorBufferize");
  }
  ::llvm::StringRef getName() const override { return "VectorBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VectorBufferizeBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
