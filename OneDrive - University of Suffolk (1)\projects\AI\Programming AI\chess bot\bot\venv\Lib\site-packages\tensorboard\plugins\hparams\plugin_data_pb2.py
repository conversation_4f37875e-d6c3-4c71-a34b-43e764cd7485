# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/hparams/plugin_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.plugins.hparams import api_pb2 as tensorboard_dot_plugins_dot_hparams_dot_api__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-tensorboard/plugins/hparams/plugin_data.proto\x12\x13tensorboard.hparams\x1a%tensorboard/plugins/hparams/api.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xe9\x01\n\x11HParamsPluginData\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x35\n\nexperiment\x18\x02 \x01(\x0b\x32\x1f.tensorboard.hparams.ExperimentH\x00\x12\x43\n\x12session_start_info\x18\x03 \x01(\x0b\x32%.tensorboard.hparams.SessionStartInfoH\x00\x12?\n\x10session_end_info\x18\x04 \x01(\x0b\x32#.tensorboard.hparams.SessionEndInfoH\x00\x42\x06\n\x04\x64\x61ta\"\xf4\x01\n\x10SessionStartInfo\x12\x43\n\x07hparams\x18\x01 \x03(\x0b\x32\x32.tensorboard.hparams.SessionStartInfo.HparamsEntry\x12\x11\n\tmodel_uri\x18\x02 \x01(\t\x12\x13\n\x0bmonitor_url\x18\x03 \x01(\t\x12\x12\n\ngroup_name\x18\x04 \x01(\t\x12\x17\n\x0fstart_time_secs\x18\x05 \x01(\x01\x1a\x46\n\x0cHparamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"T\n\x0eSessionEndInfo\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.tensorboard.hparams.Status\x12\x15\n\rend_time_secs\x18\x02 \x01(\x01\x62\x06proto3')



_HPARAMSPLUGINDATA = DESCRIPTOR.message_types_by_name['HParamsPluginData']
_SESSIONSTARTINFO = DESCRIPTOR.message_types_by_name['SessionStartInfo']
_SESSIONSTARTINFO_HPARAMSENTRY = _SESSIONSTARTINFO.nested_types_by_name['HparamsEntry']
_SESSIONENDINFO = DESCRIPTOR.message_types_by_name['SessionEndInfo']
HParamsPluginData = _reflection.GeneratedProtocolMessageType('HParamsPluginData', (_message.Message,), {
  'DESCRIPTOR' : _HPARAMSPLUGINDATA,
  '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParamsPluginData)
  })
_sym_db.RegisterMessage(HParamsPluginData)

SessionStartInfo = _reflection.GeneratedProtocolMessageType('SessionStartInfo', (_message.Message,), {

  'HparamsEntry' : _reflection.GeneratedProtocolMessageType('HparamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _SESSIONSTARTINFO_HPARAMSENTRY,
    '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionStartInfo.HparamsEntry)
    })
  ,
  'DESCRIPTOR' : _SESSIONSTARTINFO,
  '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionStartInfo)
  })
_sym_db.RegisterMessage(SessionStartInfo)
_sym_db.RegisterMessage(SessionStartInfo.HparamsEntry)

SessionEndInfo = _reflection.GeneratedProtocolMessageType('SessionEndInfo', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONENDINFO,
  '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionEndInfo)
  })
_sym_db.RegisterMessage(SessionEndInfo)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SESSIONSTARTINFO_HPARAMSENTRY._options = None
  _SESSIONSTARTINFO_HPARAMSENTRY._serialized_options = b'8\001'
  _HPARAMSPLUGINDATA._serialized_start=140
  _HPARAMSPLUGINDATA._serialized_end=373
  _SESSIONSTARTINFO._serialized_start=376
  _SESSIONSTARTINFO._serialized_end=620
  _SESSIONSTARTINFO_HPARAMSENTRY._serialized_start=550
  _SESSIONSTARTINFO_HPARAMSENTRY._serialized_end=620
  _SESSIONENDINFO._serialized_start=622
  _SESSIONENDINFO._serialized_end=706
# @@protoc_insertion_point(module_scope)
