
web_app: (use augment code - see latest thread)






- get AI to work properly


- get AI to be smarter (could use AI model like ChatGPT)




---------------------------



done: 

- get undo to work
- get <PERSON> to be able to move all the pieces
- get train ai to work (get Train AI button to work)

notes
- when pawn reaches the opponent side, it turns into a queen but then changes back to a pawn again
- game should be over when the king is checkmated
- when the king is checked, AI should defend it