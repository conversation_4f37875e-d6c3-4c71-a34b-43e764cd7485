
web_app: (use augment code - see latest thread)





- train AI functionality: is it actually doing anything?

i'm not happy with the AI's performance, too many random moves and false finish, don't see what effect difficulty and speed is having on the game 

- AI vs AI - making lots of random moves after errors are encountered which ruins the game
   {git commit - smarter AI vs AI (error tolerant) }



- make sure AI trains correctly and improves based on more games and reflects improvement in game against the user
- get AI to work properly




- get AI to be smarter




---------------------------



done: 

- get undo to work
- get AI to be able to move all the pieces
- get train ai to work (get Train AI button to work)
- evaluation score should show correctly, positive for winning, negative for losing, right now i'm getting massive numbers like -47998


notes
- when pawn reaches the opponent side, it turns into a queen but then changes back to a pawn again
- game should be over when the king is checkmated
- when the king is checked, AI should defend it