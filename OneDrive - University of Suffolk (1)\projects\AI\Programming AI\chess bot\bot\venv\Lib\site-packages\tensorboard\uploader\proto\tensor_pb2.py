# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/uploader/proto/tensor.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from tensorboard.compat.proto import summary_pb2 as tensorboard_dot_compat_dot_proto_dot_summary__pb2
try:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard_dot_compat_dot_proto_dot_histogram__pb2
except AttributeError:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard.compat.proto.histogram_pb2
from tensorboard.compat.proto import tensor_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'tensorboard/uploader/proto/tensor.proto\x12\x13tensorboard.service\x1a\x1fgoogle/protobuf/timestamp.proto\x1a&tensorboard/compat/proto/summary.proto\x1a%tensorboard/compat/proto/tensor.proto\"s\n\x0bTensorPoint\x12\x0c\n\x04step\x18\x01 \x01(\x03\x12-\n\twall_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x05value\x18\x03 \x01(\x0b\x32\x18.tensorboard.TensorProto\"\x88\x01\n\tTensorTag\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x31\n\rmax_wall_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x10summary_metadata\x18\x03 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadatab\x06proto3')



_TENSORPOINT = DESCRIPTOR.message_types_by_name['TensorPoint']
_TENSORTAG = DESCRIPTOR.message_types_by_name['TensorTag']
TensorPoint = _reflection.GeneratedProtocolMessageType('TensorPoint', (_message.Message,), {
  'DESCRIPTOR' : _TENSORPOINT,
  '__module__' : 'tensorboard.uploader.proto.tensor_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.TensorPoint)
  })
_sym_db.RegisterMessage(TensorPoint)

TensorTag = _reflection.GeneratedProtocolMessageType('TensorTag', (_message.Message,), {
  'DESCRIPTOR' : _TENSORTAG,
  '__module__' : 'tensorboard.uploader.proto.tensor_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.TensorTag)
  })
_sym_db.RegisterMessage(TensorTag)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _TENSORPOINT._serialized_start=176
  _TENSORPOINT._serialized_end=291
  _TENSORTAG._serialized_start=294
  _TENSORTAG._serialized_end=430
# @@protoc_insertion_point(module_scope)
