/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_TOSAINFERSHAPES
#define GEN_PASS_DECL_TOSALAYERWISECONSTANTFOLDPASS
#define GEN_PASS_DECL_TOSAMAKEBROADCASTABLE
#define GEN_PASS_DECL_TOSAOPTIONALDECOMPOSITIONS
#define GEN_PASS_DECL_TOSAVALIDATION
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// TosaInferShapes
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSAINFERSHAPES
#undef GEN_PASS_DECL_TOSAINFERSHAPES
#endif // GEN_PASS_DECL_TOSAINFERSHAPES
#ifdef GEN_PASS_DEF_TOSAINFERSHAPES
namespace impl {

template <typename DerivedT>
class TosaInferShapesBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaInferShapesBase;

  TosaInferShapesBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaInferShapesBase(const TosaInferShapesBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-infer-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-infer-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Propagate shapes across TOSA operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaInferShapes");
  }
  ::llvm::StringRef getName() const override { return "TosaInferShapes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<tosa::TosaDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaInferShapesBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSAINFERSHAPES
#endif // GEN_PASS_DEF_TOSAINFERSHAPES

//===----------------------------------------------------------------------===//
// TosaLayerwiseConstantFoldPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSALAYERWISECONSTANTFOLDPASS
#undef GEN_PASS_DECL_TOSALAYERWISECONSTANTFOLDPASS
#endif // GEN_PASS_DECL_TOSALAYERWISECONSTANTFOLDPASS
#ifdef GEN_PASS_DEF_TOSALAYERWISECONSTANTFOLDPASS
namespace impl {

template <typename DerivedT>
class TosaLayerwiseConstantFoldPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaLayerwiseConstantFoldPassBase;

  TosaLayerwiseConstantFoldPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaLayerwiseConstantFoldPassBase(const TosaLayerwiseConstantFoldPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-layerwise-constant-fold");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-layerwise-constant-fold"; }

  ::llvm::StringRef getDescription() const override { return "Fold layerwise operations on constant tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaLayerwiseConstantFoldPass");
  }
  ::llvm::StringRef getName() const override { return "TosaLayerwiseConstantFoldPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaLayerwiseConstantFoldPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSALAYERWISECONSTANTFOLDPASS
#endif // GEN_PASS_DEF_TOSALAYERWISECONSTANTFOLDPASS

//===----------------------------------------------------------------------===//
// TosaMakeBroadcastable
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSAMAKEBROADCASTABLE
#undef GEN_PASS_DECL_TOSAMAKEBROADCASTABLE
#endif // GEN_PASS_DECL_TOSAMAKEBROADCASTABLE
#ifdef GEN_PASS_DEF_TOSAMAKEBROADCASTABLE
namespace impl {

template <typename DerivedT>
class TosaMakeBroadcastableBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaMakeBroadcastableBase;

  TosaMakeBroadcastableBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaMakeBroadcastableBase(const TosaMakeBroadcastableBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-make-broadcastable");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-make-broadcastable"; }

  ::llvm::StringRef getDescription() const override { return "TOSA rank Reshape to enable Broadcasting"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaMakeBroadcastable");
  }
  ::llvm::StringRef getName() const override { return "TosaMakeBroadcastable"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaMakeBroadcastableBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSAMAKEBROADCASTABLE
#endif // GEN_PASS_DEF_TOSAMAKEBROADCASTABLE

//===----------------------------------------------------------------------===//
// TosaOptionalDecompositions
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSAOPTIONALDECOMPOSITIONS
#undef GEN_PASS_DECL_TOSAOPTIONALDECOMPOSITIONS
#endif // GEN_PASS_DECL_TOSAOPTIONALDECOMPOSITIONS
#ifdef GEN_PASS_DEF_TOSAOPTIONALDECOMPOSITIONS
namespace impl {

template <typename DerivedT>
class TosaOptionalDecompositionsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaOptionalDecompositionsBase;

  TosaOptionalDecompositionsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaOptionalDecompositionsBase(const TosaOptionalDecompositionsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-optional-decompositions");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-optional-decompositions"; }

  ::llvm::StringRef getDescription() const override { return "Applies Tosa operations optional decompositions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaOptionalDecompositions");
  }
  ::llvm::StringRef getName() const override { return "TosaOptionalDecompositions"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaOptionalDecompositionsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSAOPTIONALDECOMPOSITIONS
#endif // GEN_PASS_DEF_TOSAOPTIONALDECOMPOSITIONS

//===----------------------------------------------------------------------===//
// TosaValidation
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSAVALIDATION
struct TosaValidationOptions {
  std::string profileName = "undefined";
  bool StrictOperationSpecAlignment = false;
};
#undef GEN_PASS_DECL_TOSAVALIDATION
#endif // GEN_PASS_DECL_TOSAVALIDATION
#ifdef GEN_PASS_DEF_TOSAVALIDATION
namespace impl {

template <typename DerivedT>
class TosaValidationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaValidationBase;

  TosaValidationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaValidationBase(const TosaValidationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-validate");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-validate"; }

  ::llvm::StringRef getDescription() const override { return "Validates TOSA dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaValidation");
  }
  ::llvm::StringRef getName() const override { return "TosaValidation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaValidationBase<DerivedT>)

  TosaValidationBase(const TosaValidationOptions &options) : TosaValidationBase() {
    profileName = options.profileName;
    StrictOperationSpecAlignment = options.StrictOperationSpecAlignment;
  }
protected:
  ::mlir::Pass::Option<std::string> profileName{*this, "profile", ::llvm::cl::desc("Validate if operations match for the given profile"), ::llvm::cl::init("undefined")};
  ::mlir::Pass::Option<bool> StrictOperationSpecAlignment{*this, "strict-op-spec-alignment", ::llvm::cl::desc("Verify if the properties of certain operations align the spec requirement"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSAVALIDATION
#endif // GEN_PASS_DEF_TOSAVALIDATION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// TosaInferShapes Registration
//===----------------------------------------------------------------------===//

inline void registerTosaInferShapes() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaInferShapesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaInferShapesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaInferShapesPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaLayerwiseConstantFoldPass Registration
//===----------------------------------------------------------------------===//

inline void registerTosaLayerwiseConstantFoldPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaLayerwiseConstantFoldPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaLayerwiseConstantFoldPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaLayerwiseConstantFoldPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaMakeBroadcastable Registration
//===----------------------------------------------------------------------===//

inline void registerTosaMakeBroadcastable() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaMakeBroadcastablePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaMakeBroadcastablePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaMakeBroadcastablePass();
  });
}

//===----------------------------------------------------------------------===//
// TosaOptionalDecompositions Registration
//===----------------------------------------------------------------------===//

inline void registerTosaOptionalDecompositions() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaOptionalDecompositions();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaOptionalDecompositionsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaOptionalDecompositions();
  });
}

//===----------------------------------------------------------------------===//
// TosaValidation Registration
//===----------------------------------------------------------------------===//

inline void registerTosaValidation() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaValidationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaValidationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaValidationPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaOpt Registration
//===----------------------------------------------------------------------===//

inline void registerTosaOptPasses() {
  registerTosaInferShapes();
  registerTosaLayerwiseConstantFoldPass();
  registerTosaMakeBroadcastable();
  registerTosaOptionalDecompositions();
  registerTosaValidation();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class TosaInferShapesBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaInferShapesBase;

  TosaInferShapesBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaInferShapesBase(const TosaInferShapesBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-infer-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-infer-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Propagate shapes across TOSA operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaInferShapes");
  }
  ::llvm::StringRef getName() const override { return "TosaInferShapes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<tosa::TosaDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaInferShapesBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaLayerwiseConstantFoldPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaLayerwiseConstantFoldPassBase;

  TosaLayerwiseConstantFoldPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaLayerwiseConstantFoldPassBase(const TosaLayerwiseConstantFoldPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-layerwise-constant-fold");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-layerwise-constant-fold"; }

  ::llvm::StringRef getDescription() const override { return "Fold layerwise operations on constant tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaLayerwiseConstantFoldPass");
  }
  ::llvm::StringRef getName() const override { return "TosaLayerwiseConstantFoldPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaLayerwiseConstantFoldPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaMakeBroadcastableBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaMakeBroadcastableBase;

  TosaMakeBroadcastableBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaMakeBroadcastableBase(const TosaMakeBroadcastableBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-make-broadcastable");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-make-broadcastable"; }

  ::llvm::StringRef getDescription() const override { return "TOSA rank Reshape to enable Broadcasting"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaMakeBroadcastable");
  }
  ::llvm::StringRef getName() const override { return "TosaMakeBroadcastable"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaMakeBroadcastableBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaOptionalDecompositionsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaOptionalDecompositionsBase;

  TosaOptionalDecompositionsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaOptionalDecompositionsBase(const TosaOptionalDecompositionsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-optional-decompositions");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-optional-decompositions"; }

  ::llvm::StringRef getDescription() const override { return "Applies Tosa operations optional decompositions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaOptionalDecompositions");
  }
  ::llvm::StringRef getName() const override { return "TosaOptionalDecompositions"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaOptionalDecompositionsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaValidationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TosaValidationBase;

  TosaValidationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaValidationBase(const TosaValidationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-validate");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-validate"; }

  ::llvm::StringRef getDescription() const override { return "Validates TOSA dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaValidation");
  }
  ::llvm::StringRef getName() const override { return "TosaValidation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaValidationBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> profileName{*this, "profile", ::llvm::cl::desc("Validate if operations match for the given profile"), ::llvm::cl::init("undefined")};
  ::mlir::Pass::Option<bool> StrictOperationSpecAlignment{*this, "strict-op-spec-alignment", ::llvm::cl::desc("Verify if the properties of certain operations align the spec requirement"), ::llvm::cl::init(false)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
