
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterLinalgPasses(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgConvertElementwiseToLinalg(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgConvertElementwiseToLinalg(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgBufferize(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgBufferize(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgDetensorize(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgDetensorize(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgElementwiseOpFusion(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgElementwiseOpFusion(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgFoldUnitExtentDims(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgFoldUnitExtentDims(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgGeneralization(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgGeneralization(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgInlineScalarOperands(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgInlineScalarOperands(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerToAffineLoops(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerToAffineLoops(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerToLoops(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerToLoops(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerToParallelLoops(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerToParallelLoops(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgNamedOpConversion(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgNamedOpConversion(void);



#ifdef __cplusplus
}
#endif
