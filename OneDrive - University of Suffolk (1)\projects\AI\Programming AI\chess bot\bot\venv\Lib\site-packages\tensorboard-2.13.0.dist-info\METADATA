Metadata-Version: 2.1
Name: tensorboard
Version: 2.13.0
Summary: TensorBoard lets you watch Tensors Flow
Home-page: https://github.com/tensorflow/tensorboard
Author: Google Inc.
Author-email: <EMAIL>
License: Apache 2.0
Keywords: tensorflow tensorboard tensor machine learning visualizer
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.8
License-File: LICENSE
Requires-Dist: absl-py (>=0.4)
Requires-Dist: grpcio (>=1.48.2)
Requires-Dist: google-auth (<3,>=1.6.3)
Requires-Dist: google-auth-oauthlib (<1.1,>=0.5)
Requires-Dist: markdown (>=2.6.8)
Requires-Dist: numpy (>=1.12.0)
Requires-Dist: protobuf (>=3.19.6)
Requires-Dist: requests (<3,>=2.21.0)
Requires-Dist: setuptools (>=41.0.0)
Requires-Dist: tensorboard-data-server (<0.8.0,>=0.7.0)
Requires-Dist: werkzeug (>=1.0.1)
Requires-Dist: wheel (>=0.26)

TensorBoard is a suite of web applications for inspecting and understanding
your TensorFlow runs and graphs.

Releases prior to 1.6.0 were published under the ``tensorflow-tensorboard`` name
and may be found at https://pypi.python.org/pypi/tensorflow-tensorboard.
