/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_SCFBUFFERIZE
#define GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#define GEN_PASS_DECL_SCFFORLOOPPEELING
#define GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#define GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#define GEN_PASS_DECL_SCFFORTOWHILELOOP
#define GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#define GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#define GEN_PASS_DECL_SCFPARALLELLOOPTILING
#define GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// SCFBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFBUFFERIZE
#undef GEN_PASS_DECL_SCFBUFFERIZE
#endif // GEN_PASS_DECL_SCFBUFFERIZE
#ifdef GEN_PASS_DEF_SCFBUFFERIZE
namespace impl {

template <typename DerivedT>
class SCFBufferizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFBufferizeBase;

  SCFBufferizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFBufferizeBase(const SCFBufferizeBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "scf-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the scf dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFBufferize");
  }
  ::llvm::StringRef getName() const override { return "SCFBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFBUFFERIZE
#endif // GEN_PASS_DEF_SCFBUFFERIZE

//===----------------------------------------------------------------------===//
// SCFForLoopCanonicalization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#undef GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#endif // GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#ifdef GEN_PASS_DEF_SCFFORLOOPCANONICALIZATION
namespace impl {

template <typename DerivedT>
class SCFForLoopCanonicalizationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopCanonicalizationBase;

  SCFForLoopCanonicalizationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopCanonicalizationBase(const SCFForLoopCanonicalizationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-canonicalization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-canonicalization"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations within scf.for loop bodies"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopCanonicalization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopCanonicalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopCanonicalizationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPCANONICALIZATION
#endif // GEN_PASS_DEF_SCFFORLOOPCANONICALIZATION

//===----------------------------------------------------------------------===//
// SCFForLoopPeeling
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPPEELING
struct SCFForLoopPeelingOptions {
  bool skipPartial = true;
};
#undef GEN_PASS_DECL_SCFFORLOOPPEELING
#endif // GEN_PASS_DECL_SCFFORLOOPPEELING
#ifdef GEN_PASS_DEF_SCFFORLOOPPEELING
namespace impl {

template <typename DerivedT>
class SCFForLoopPeelingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopPeelingBase;

  SCFForLoopPeelingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopPeelingBase(const SCFForLoopPeelingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-peeling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-peeling"; }

  ::llvm::StringRef getDescription() const override { return "Peel `for` loops at their upper bounds."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopPeeling");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopPeeling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopPeelingBase<DerivedT>)

  SCFForLoopPeelingBase(const SCFForLoopPeelingOptions &options) : SCFForLoopPeelingBase() {
    skipPartial = options.skipPartial;
  }
protected:
  ::mlir::Pass::Option<bool> skipPartial{*this, "skip-partial", ::llvm::cl::desc("Do not peel loops inside of the last, partial iteration of another already peeled loop."), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPPEELING
#endif // GEN_PASS_DEF_SCFFORLOOPPEELING

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#undef GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#endif // GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#ifdef GEN_PASS_DEF_SCFFORLOOPRANGEFOLDING
namespace impl {

template <typename DerivedT>
class SCFForLoopRangeFoldingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopRangeFoldingBase;

  SCFForLoopRangeFoldingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopRangeFoldingBase(const SCFForLoopRangeFoldingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-range-folding");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-range-folding"; }

  ::llvm::StringRef getDescription() const override { return "Fold add/mul ops into loop range"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopRangeFolding");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopRangeFolding"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopRangeFoldingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPRANGEFOLDING
#endif // GEN_PASS_DEF_SCFFORLOOPRANGEFOLDING

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#undef GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#endif // GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#ifdef GEN_PASS_DEF_SCFFORLOOPSPECIALIZATION
namespace impl {

template <typename DerivedT>
class SCFForLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopSpecializationBase;

  SCFForLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopSpecializationBase(const SCFForLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize `for` loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopSpecializationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPSPECIALIZATION
#endif // GEN_PASS_DEF_SCFFORLOOPSPECIALIZATION

//===----------------------------------------------------------------------===//
// SCFForToWhileLoop
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORTOWHILELOOP
#undef GEN_PASS_DECL_SCFFORTOWHILELOOP
#endif // GEN_PASS_DECL_SCFFORTOWHILELOOP
#ifdef GEN_PASS_DEF_SCFFORTOWHILELOOP
namespace impl {

template <typename DerivedT>
class SCFForToWhileLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForToWhileLoopBase;

  SCFForToWhileLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForToWhileLoopBase(const SCFForToWhileLoopBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-to-while");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-to-while"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF for loops to SCF while loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForToWhileLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForToWhileLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForToWhileLoopBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORTOWHILELOOP
#endif // GEN_PASS_DEF_SCFFORTOWHILELOOP

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#undef GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#endif // GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#ifdef GEN_PASS_DEF_SCFPARALLELLOOPFUSION
namespace impl {

template <typename DerivedT>
class SCFParallelLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopFusionBase;

  SCFParallelLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopFusionBase(const SCFParallelLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse adjacent parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopFusionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFPARALLELLOOPFUSION
#endif // GEN_PASS_DEF_SCFPARALLELLOOPFUSION

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#undef GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#endif // GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#ifdef GEN_PASS_DEF_SCFPARALLELLOOPSPECIALIZATION
namespace impl {

template <typename DerivedT>
class SCFParallelLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopSpecializationBase;

  SCFParallelLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopSpecializationBase(const SCFParallelLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize parallel loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopSpecializationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFPARALLELLOOPSPECIALIZATION
#endif // GEN_PASS_DEF_SCFPARALLELLOOPSPECIALIZATION

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFPARALLELLOOPTILING
struct SCFParallelLoopTilingOptions {
  ::llvm::ArrayRef<int64_t> tileSizes;
  bool noMinMaxBounds = false;
};
#undef GEN_PASS_DECL_SCFPARALLELLOOPTILING
#endif // GEN_PASS_DECL_SCFPARALLELLOOPTILING
#ifdef GEN_PASS_DEF_SCFPARALLELLOOPTILING
namespace impl {

template <typename DerivedT>
class SCFParallelLoopTilingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopTilingBase;

  SCFParallelLoopTilingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopTilingBase(const SCFParallelLoopTilingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-tiling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-tiling"; }

  ::llvm::StringRef getDescription() const override { return "Tile parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopTilingBase<DerivedT>)

  SCFParallelLoopTilingBase(const SCFParallelLoopTilingOptions &options) : SCFParallelLoopTilingBase() {
    tileSizes = options.tileSizes;
    noMinMaxBounds = options.noMinMaxBounds;
  }
protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "parallel-loop-tile-sizes", ::llvm::cl::desc("Factors to tile parallel loops by")};
  ::mlir::Pass::Option<bool> noMinMaxBounds{*this, "no-min-max-bounds", ::llvm::cl::desc("Perform tiling with fixed upper bound with inbound check inside the internal loops"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFPARALLELLOOPTILING
#endif // GEN_PASS_DEF_SCFPARALLELLOOPTILING

//===----------------------------------------------------------------------===//
// TestSCFParallelLoopCollapsing
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
struct TestSCFParallelLoopCollapsingOptions {
  ::llvm::ArrayRef<unsigned> clCollapsedIndices0;
  ::llvm::ArrayRef<unsigned> clCollapsedIndices1;
  ::llvm::ArrayRef<unsigned> clCollapsedIndices2;
};
#undef GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
#endif // GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
#ifdef GEN_PASS_DEF_TESTSCFPARALLELLOOPCOLLAPSING
namespace impl {

template <typename DerivedT>
class TestSCFParallelLoopCollapsingBase : public ::mlir::OperationPass<> {
public:
  using Base = TestSCFParallelLoopCollapsingBase;

  TestSCFParallelLoopCollapsingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TestSCFParallelLoopCollapsingBase(const TestSCFParallelLoopCollapsingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("test-scf-parallel-loop-collapsing");
  }
  ::llvm::StringRef getArgument() const override { return "test-scf-parallel-loop-collapsing"; }

  ::llvm::StringRef getDescription() const override { return "Test parallel loops collapsing transformation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestSCFParallelLoopCollapsing");
  }
  ::llvm::StringRef getName() const override { return "TestSCFParallelLoopCollapsing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestSCFParallelLoopCollapsingBase<DerivedT>)

  TestSCFParallelLoopCollapsingBase(const TestSCFParallelLoopCollapsingOptions &options) : TestSCFParallelLoopCollapsingBase() {
    clCollapsedIndices0 = options.clCollapsedIndices0;
    clCollapsedIndices1 = options.clCollapsedIndices1;
    clCollapsedIndices2 = options.clCollapsedIndices2;
  }
protected:
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices0{*this, "collapsed-indices-0", ::llvm::cl::desc("Which loop indices to combine 0th loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices1{*this, "collapsed-indices-1", ::llvm::cl::desc("Which loop indices to combine into the position 1 loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices2{*this, "collapsed-indices-2", ::llvm::cl::desc("Which loop indices to combine into the position 2 loop index")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TESTSCFPARALLELLOOPCOLLAPSING
#endif // GEN_PASS_DEF_TESTSCFPARALLELLOOPCOLLAPSING
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SCFBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerSCFBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopCanonicalization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopCanonicalization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFForLoopCanonicalizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopCanonicalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFForLoopCanonicalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopPeeling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopPeeling() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopPeelingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopPeelingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopPeelingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopRangeFolding() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopRangeFoldingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopRangeFoldingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopRangeFoldingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopSpecialization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopSpecializationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForToWhileLoop Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForToWhileLoop() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForToWhileLoopPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForToWhileLoopPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForToWhileLoopPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopFusion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopFusionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFParallelLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopSpecialization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopSpecializationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFParallelLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopTiling() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopTilingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFParallelLoopTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// TestSCFParallelLoopCollapsing Registration
//===----------------------------------------------------------------------===//

inline void registerTestSCFParallelLoopCollapsing() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTestSCFParallelLoopCollapsingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTestSCFParallelLoopCollapsingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTestSCFParallelLoopCollapsingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCF Registration
//===----------------------------------------------------------------------===//

inline void registerSCFPasses() {
  registerSCFBufferize();
  registerSCFForLoopCanonicalization();
  registerSCFForLoopPeeling();
  registerSCFForLoopRangeFolding();
  registerSCFForLoopSpecialization();
  registerSCFForToWhileLoop();
  registerSCFParallelLoopFusion();
  registerSCFParallelLoopSpecialization();
  registerSCFParallelLoopTiling();
  registerTestSCFParallelLoopCollapsing();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class SCFBufferizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFBufferizeBase;

  SCFBufferizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFBufferizeBase(const SCFBufferizeBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "scf-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the scf dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFBufferize");
  }
  ::llvm::StringRef getName() const override { return "SCFBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFBufferizeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForLoopCanonicalizationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopCanonicalizationBase;

  SCFForLoopCanonicalizationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopCanonicalizationBase(const SCFForLoopCanonicalizationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-canonicalization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-canonicalization"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations within scf.for loop bodies"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopCanonicalization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopCanonicalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopCanonicalizationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForLoopPeelingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopPeelingBase;

  SCFForLoopPeelingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopPeelingBase(const SCFForLoopPeelingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-peeling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-peeling"; }

  ::llvm::StringRef getDescription() const override { return "Peel `for` loops at their upper bounds."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopPeeling");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopPeeling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopPeelingBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> skipPartial{*this, "skip-partial", ::llvm::cl::desc("Do not peel loops inside of the last, partial iteration of another already peeled loop."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class SCFForLoopRangeFoldingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopRangeFoldingBase;

  SCFForLoopRangeFoldingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopRangeFoldingBase(const SCFForLoopRangeFoldingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-range-folding");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-range-folding"; }

  ::llvm::StringRef getDescription() const override { return "Fold add/mul ops into loop range"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopRangeFolding");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopRangeFolding"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopRangeFoldingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopSpecializationBase;

  SCFForLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopSpecializationBase(const SCFForLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize `for` loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopSpecializationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForToWhileLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForToWhileLoopBase;

  SCFForToWhileLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForToWhileLoopBase(const SCFForToWhileLoopBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-to-while");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-to-while"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF for loops to SCF while loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForToWhileLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForToWhileLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForToWhileLoopBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFParallelLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopFusionBase;

  SCFParallelLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopFusionBase(const SCFParallelLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse adjacent parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopFusionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFParallelLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopSpecializationBase;

  SCFParallelLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopSpecializationBase(const SCFParallelLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize parallel loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopSpecializationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFParallelLoopTilingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopTilingBase;

  SCFParallelLoopTilingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopTilingBase(const SCFParallelLoopTilingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-tiling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-tiling"; }

  ::llvm::StringRef getDescription() const override { return "Tile parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopTilingBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "parallel-loop-tile-sizes", ::llvm::cl::desc("Factors to tile parallel loops by")};
  ::mlir::Pass::Option<bool> noMinMaxBounds{*this, "no-min-max-bounds", ::llvm::cl::desc("Perform tiling with fixed upper bound with inbound check inside the internal loops"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TestSCFParallelLoopCollapsingBase : public ::mlir::OperationPass<> {
public:
  using Base = TestSCFParallelLoopCollapsingBase;

  TestSCFParallelLoopCollapsingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TestSCFParallelLoopCollapsingBase(const TestSCFParallelLoopCollapsingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("test-scf-parallel-loop-collapsing");
  }
  ::llvm::StringRef getArgument() const override { return "test-scf-parallel-loop-collapsing"; }

  ::llvm::StringRef getDescription() const override { return "Test parallel loops collapsing transformation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestSCFParallelLoopCollapsing");
  }
  ::llvm::StringRef getName() const override { return "TestSCFParallelLoopCollapsing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestSCFParallelLoopCollapsingBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices0{*this, "collapsed-indices-0", ::llvm::cl::desc("Which loop indices to combine 0th loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices1{*this, "collapsed-indices-1", ::llvm::cl::desc("Which loop indices to combine into the position 1 loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices2{*this, "collapsed-indices-2", ::llvm::cl::desc("Which loop indices to combine into the position 2 loop index")};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
