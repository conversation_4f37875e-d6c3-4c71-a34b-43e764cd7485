/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_LIB_HASH_CRC32C_H_
#define TENSORFLOW_CORE_LIB_HASH_CRC32C_H_

#include <stddef.h>

#include "tensorflow/core/platform/cord.h"
#include "tensorflow/core/platform/platform.h"
#include "tensorflow/core/platform/types.h"
#include "tensorflow/tsl/lib/hash/crc32c.h"

namespace tensorflow {
namespace crc32c {
// NOLINTBEGIN(misc-unused-using-decls)
using tsl::crc32c::Extend;
using tsl::crc32c::kMaskDelta;
using tsl::crc32c::Mask;
using tsl::crc32c::Unmask;
using tsl::crc32c::Value;
// NOLINTEND(misc-unused-using-decls)
}  // namespace crc32c
}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_LIB_HASH_CRC32C_H_
