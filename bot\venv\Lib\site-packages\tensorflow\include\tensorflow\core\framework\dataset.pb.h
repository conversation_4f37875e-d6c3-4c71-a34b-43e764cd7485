// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/dataset.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fdataset_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fdataset_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fdataset_2eproto;
namespace tensorflow {
namespace data {
class CompressedComponentMetadata;
struct CompressedComponentMetadataDefaultTypeInternal;
extern CompressedComponentMetadataDefaultTypeInternal _CompressedComponentMetadata_default_instance_;
class CompressedElement;
struct CompressedElementDefaultTypeInternal;
extern CompressedElementDefaultTypeInternal _CompressedElement_default_instance_;
class UncompressedElement;
struct UncompressedElementDefaultTypeInternal;
extern UncompressedElementDefaultTypeInternal _UncompressedElement_default_instance_;
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::CompressedComponentMetadata* Arena::CreateMaybeMessage<::tensorflow::data::CompressedComponentMetadata>(Arena*);
template<> ::tensorflow::data::CompressedElement* Arena::CreateMaybeMessage<::tensorflow::data::CompressedElement>(Arena*);
template<> ::tensorflow::data::UncompressedElement* Arena::CreateMaybeMessage<::tensorflow::data::UncompressedElement>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {

// ===================================================================

class CompressedComponentMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.CompressedComponentMetadata) */ {
 public:
  inline CompressedComponentMetadata() : CompressedComponentMetadata(nullptr) {}
  ~CompressedComponentMetadata() override;
  explicit PROTOBUF_CONSTEXPR CompressedComponentMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompressedComponentMetadata(const CompressedComponentMetadata& from);
  CompressedComponentMetadata(CompressedComponentMetadata&& from) noexcept
    : CompressedComponentMetadata() {
    *this = ::std::move(from);
  }

  inline CompressedComponentMetadata& operator=(const CompressedComponentMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompressedComponentMetadata& operator=(CompressedComponentMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompressedComponentMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompressedComponentMetadata* internal_default_instance() {
    return reinterpret_cast<const CompressedComponentMetadata*>(
               &_CompressedComponentMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CompressedComponentMetadata& a, CompressedComponentMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(CompressedComponentMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompressedComponentMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompressedComponentMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompressedComponentMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompressedComponentMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompressedComponentMetadata& from) {
    CompressedComponentMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompressedComponentMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.CompressedComponentMetadata";
  }
  protected:
  explicit CompressedComponentMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUncompressedBytesFieldNumber = 4,
    kTensorShapeFieldNumber = 2,
    kDtypeFieldNumber = 1,
  };
  // repeated uint64 uncompressed_bytes = 4;
  int uncompressed_bytes_size() const;
  private:
  int _internal_uncompressed_bytes_size() const;
  public:
  void clear_uncompressed_bytes();
  private:
  uint64_t _internal_uncompressed_bytes(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_uncompressed_bytes() const;
  void _internal_add_uncompressed_bytes(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_uncompressed_bytes();
  public:
  uint64_t uncompressed_bytes(int index) const;
  void set_uncompressed_bytes(int index, uint64_t value);
  void add_uncompressed_bytes(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      uncompressed_bytes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_uncompressed_bytes();

  // .tensorflow.TensorShapeProto tensor_shape = 2;
  bool has_tensor_shape() const;
  private:
  bool _internal_has_tensor_shape() const;
  public:
  void clear_tensor_shape();
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_tensor_shape();
  public:
  void unsafe_arena_set_allocated_tensor_shape(
      ::tensorflow::TensorShapeProto* tensor_shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.CompressedComponentMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > uncompressed_bytes_;
    mutable std::atomic<int> _uncompressed_bytes_cached_byte_size_;
    ::tensorflow::TensorShapeProto* tensor_shape_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_2eproto;
};
// -------------------------------------------------------------------

class CompressedElement final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.CompressedElement) */ {
 public:
  inline CompressedElement() : CompressedElement(nullptr) {}
  ~CompressedElement() override;
  explicit PROTOBUF_CONSTEXPR CompressedElement(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompressedElement(const CompressedElement& from);
  CompressedElement(CompressedElement&& from) noexcept
    : CompressedElement() {
    *this = ::std::move(from);
  }

  inline CompressedElement& operator=(const CompressedElement& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompressedElement& operator=(CompressedElement&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompressedElement& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompressedElement* internal_default_instance() {
    return reinterpret_cast<const CompressedElement*>(
               &_CompressedElement_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CompressedElement& a, CompressedElement& b) {
    a.Swap(&b);
  }
  inline void Swap(CompressedElement* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompressedElement* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompressedElement* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompressedElement>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompressedElement& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompressedElement& from) {
    CompressedElement::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompressedElement* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.CompressedElement";
  }
  protected:
  explicit CompressedElement(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentMetadataFieldNumber = 2,
    kDataFieldNumber = 1,
    kVersionFieldNumber = 3,
  };
  // repeated .tensorflow.data.CompressedComponentMetadata component_metadata = 2;
  int component_metadata_size() const;
  private:
  int _internal_component_metadata_size() const;
  public:
  void clear_component_metadata();
  ::tensorflow::data::CompressedComponentMetadata* mutable_component_metadata(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::CompressedComponentMetadata >*
      mutable_component_metadata();
  private:
  const ::tensorflow::data::CompressedComponentMetadata& _internal_component_metadata(int index) const;
  ::tensorflow::data::CompressedComponentMetadata* _internal_add_component_metadata();
  public:
  const ::tensorflow::data::CompressedComponentMetadata& component_metadata(int index) const;
  ::tensorflow::data::CompressedComponentMetadata* add_component_metadata();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::CompressedComponentMetadata >&
      component_metadata() const;

  // bytes data = 1;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // int32 version = 3;
  void clear_version();
  int32_t version() const;
  void set_version(int32_t value);
  private:
  int32_t _internal_version() const;
  void _internal_set_version(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.CompressedElement)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::CompressedComponentMetadata > component_metadata_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    int32_t version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_2eproto;
};
// -------------------------------------------------------------------

class UncompressedElement final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.UncompressedElement) */ {
 public:
  inline UncompressedElement() : UncompressedElement(nullptr) {}
  ~UncompressedElement() override;
  explicit PROTOBUF_CONSTEXPR UncompressedElement(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UncompressedElement(const UncompressedElement& from);
  UncompressedElement(UncompressedElement&& from) noexcept
    : UncompressedElement() {
    *this = ::std::move(from);
  }

  inline UncompressedElement& operator=(const UncompressedElement& from) {
    CopyFrom(from);
    return *this;
  }
  inline UncompressedElement& operator=(UncompressedElement&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UncompressedElement& default_instance() {
    return *internal_default_instance();
  }
  static inline const UncompressedElement* internal_default_instance() {
    return reinterpret_cast<const UncompressedElement*>(
               &_UncompressedElement_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UncompressedElement& a, UncompressedElement& b) {
    a.Swap(&b);
  }
  inline void Swap(UncompressedElement* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UncompressedElement* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UncompressedElement* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UncompressedElement>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UncompressedElement& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UncompressedElement& from) {
    UncompressedElement::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UncompressedElement* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.UncompressedElement";
  }
  protected:
  explicit UncompressedElement(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentsFieldNumber = 1,
  };
  // repeated .tensorflow.TensorProto components = 1;
  int components_size() const;
  private:
  int _internal_components_size() const;
  public:
  void clear_components();
  ::tensorflow::TensorProto* mutable_components(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_components();
  private:
  const ::tensorflow::TensorProto& _internal_components(int index) const;
  ::tensorflow::TensorProto* _internal_add_components();
  public:
  const ::tensorflow::TensorProto& components(int index) const;
  ::tensorflow::TensorProto* add_components();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      components() const;

  // @@protoc_insertion_point(class_scope:tensorflow.data.UncompressedElement)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > components_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CompressedComponentMetadata

// .tensorflow.DataType dtype = 1;
inline void CompressedComponentMetadata::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType CompressedComponentMetadata::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType CompressedComponentMetadata::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CompressedComponentMetadata.dtype)
  return _internal_dtype();
}
inline void CompressedComponentMetadata::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void CompressedComponentMetadata::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.CompressedComponentMetadata.dtype)
}

// .tensorflow.TensorShapeProto tensor_shape = 2;
inline bool CompressedComponentMetadata::_internal_has_tensor_shape() const {
  return this != internal_default_instance() && _impl_.tensor_shape_ != nullptr;
}
inline bool CompressedComponentMetadata::has_tensor_shape() const {
  return _internal_has_tensor_shape();
}
inline const ::tensorflow::TensorShapeProto& CompressedComponentMetadata::_internal_tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.tensor_shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& CompressedComponentMetadata::tensor_shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CompressedComponentMetadata.tensor_shape)
  return _internal_tensor_shape();
}
inline void CompressedComponentMetadata::unsafe_arena_set_allocated_tensor_shape(
    ::tensorflow::TensorShapeProto* tensor_shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  _impl_.tensor_shape_ = tensor_shape;
  if (tensor_shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.CompressedComponentMetadata.tensor_shape)
}
inline ::tensorflow::TensorShapeProto* CompressedComponentMetadata::release_tensor_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompressedComponentMetadata::unsafe_arena_release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.data.CompressedComponentMetadata.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompressedComponentMetadata::_internal_mutable_tensor_shape() {
  
  if (_impl_.tensor_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.tensor_shape_ = p;
  }
  return _impl_.tensor_shape_;
}
inline ::tensorflow::TensorShapeProto* CompressedComponentMetadata::mutable_tensor_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_tensor_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.CompressedComponentMetadata.tensor_shape)
  return _msg;
}
inline void CompressedComponentMetadata::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  if (tensor_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_shape));
    if (message_arena != submessage_arena) {
      tensor_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.CompressedComponentMetadata.tensor_shape)
}

// repeated uint64 uncompressed_bytes = 4;
inline int CompressedComponentMetadata::_internal_uncompressed_bytes_size() const {
  return _impl_.uncompressed_bytes_.size();
}
inline int CompressedComponentMetadata::uncompressed_bytes_size() const {
  return _internal_uncompressed_bytes_size();
}
inline void CompressedComponentMetadata::clear_uncompressed_bytes() {
  _impl_.uncompressed_bytes_.Clear();
}
inline uint64_t CompressedComponentMetadata::_internal_uncompressed_bytes(int index) const {
  return _impl_.uncompressed_bytes_.Get(index);
}
inline uint64_t CompressedComponentMetadata::uncompressed_bytes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CompressedComponentMetadata.uncompressed_bytes)
  return _internal_uncompressed_bytes(index);
}
inline void CompressedComponentMetadata::set_uncompressed_bytes(int index, uint64_t value) {
  _impl_.uncompressed_bytes_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.data.CompressedComponentMetadata.uncompressed_bytes)
}
inline void CompressedComponentMetadata::_internal_add_uncompressed_bytes(uint64_t value) {
  _impl_.uncompressed_bytes_.Add(value);
}
inline void CompressedComponentMetadata::add_uncompressed_bytes(uint64_t value) {
  _internal_add_uncompressed_bytes(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.CompressedComponentMetadata.uncompressed_bytes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
CompressedComponentMetadata::_internal_uncompressed_bytes() const {
  return _impl_.uncompressed_bytes_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
CompressedComponentMetadata::uncompressed_bytes() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.CompressedComponentMetadata.uncompressed_bytes)
  return _internal_uncompressed_bytes();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
CompressedComponentMetadata::_internal_mutable_uncompressed_bytes() {
  return &_impl_.uncompressed_bytes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
CompressedComponentMetadata::mutable_uncompressed_bytes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.CompressedComponentMetadata.uncompressed_bytes)
  return _internal_mutable_uncompressed_bytes();
}

// -------------------------------------------------------------------

// CompressedElement

// bytes data = 1;
inline void CompressedElement::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& CompressedElement::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CompressedElement.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressedElement::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.CompressedElement.data)
}
inline std::string* CompressedElement::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.CompressedElement.data)
  return _s;
}
inline const std::string& CompressedElement::_internal_data() const {
  return _impl_.data_.Get();
}
inline void CompressedElement::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressedElement::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressedElement::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.data.CompressedElement.data)
  return _impl_.data_.Release();
}
inline void CompressedElement::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.CompressedElement.data)
}

// repeated .tensorflow.data.CompressedComponentMetadata component_metadata = 2;
inline int CompressedElement::_internal_component_metadata_size() const {
  return _impl_.component_metadata_.size();
}
inline int CompressedElement::component_metadata_size() const {
  return _internal_component_metadata_size();
}
inline void CompressedElement::clear_component_metadata() {
  _impl_.component_metadata_.Clear();
}
inline ::tensorflow::data::CompressedComponentMetadata* CompressedElement::mutable_component_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.CompressedElement.component_metadata)
  return _impl_.component_metadata_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::CompressedComponentMetadata >*
CompressedElement::mutable_component_metadata() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.CompressedElement.component_metadata)
  return &_impl_.component_metadata_;
}
inline const ::tensorflow::data::CompressedComponentMetadata& CompressedElement::_internal_component_metadata(int index) const {
  return _impl_.component_metadata_.Get(index);
}
inline const ::tensorflow::data::CompressedComponentMetadata& CompressedElement::component_metadata(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CompressedElement.component_metadata)
  return _internal_component_metadata(index);
}
inline ::tensorflow::data::CompressedComponentMetadata* CompressedElement::_internal_add_component_metadata() {
  return _impl_.component_metadata_.Add();
}
inline ::tensorflow::data::CompressedComponentMetadata* CompressedElement::add_component_metadata() {
  ::tensorflow::data::CompressedComponentMetadata* _add = _internal_add_component_metadata();
  // @@protoc_insertion_point(field_add:tensorflow.data.CompressedElement.component_metadata)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::CompressedComponentMetadata >&
CompressedElement::component_metadata() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.CompressedElement.component_metadata)
  return _impl_.component_metadata_;
}

// int32 version = 3;
inline void CompressedElement::clear_version() {
  _impl_.version_ = 0;
}
inline int32_t CompressedElement::_internal_version() const {
  return _impl_.version_;
}
inline int32_t CompressedElement::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CompressedElement.version)
  return _internal_version();
}
inline void CompressedElement::_internal_set_version(int32_t value) {
  
  _impl_.version_ = value;
}
inline void CompressedElement::set_version(int32_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.CompressedElement.version)
}

// -------------------------------------------------------------------

// UncompressedElement

// repeated .tensorflow.TensorProto components = 1;
inline int UncompressedElement::_internal_components_size() const {
  return _impl_.components_.size();
}
inline int UncompressedElement::components_size() const {
  return _internal_components_size();
}
inline ::tensorflow::TensorProto* UncompressedElement::mutable_components(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.UncompressedElement.components)
  return _impl_.components_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
UncompressedElement::mutable_components() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.UncompressedElement.components)
  return &_impl_.components_;
}
inline const ::tensorflow::TensorProto& UncompressedElement::_internal_components(int index) const {
  return _impl_.components_.Get(index);
}
inline const ::tensorflow::TensorProto& UncompressedElement::components(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.UncompressedElement.components)
  return _internal_components(index);
}
inline ::tensorflow::TensorProto* UncompressedElement::_internal_add_components() {
  return _impl_.components_.Add();
}
inline ::tensorflow::TensorProto* UncompressedElement::add_components() {
  ::tensorflow::TensorProto* _add = _internal_add_components();
  // @@protoc_insertion_point(field_add:tensorflow.data.UncompressedElement.components)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
UncompressedElement::components() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.UncompressedElement.components)
  return _impl_.components_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace data
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_2eproto
