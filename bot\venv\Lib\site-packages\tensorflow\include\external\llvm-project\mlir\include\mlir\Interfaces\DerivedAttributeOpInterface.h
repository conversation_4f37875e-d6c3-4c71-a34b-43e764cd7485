//===- DerivedAttributeOpInterface.h ----------------------------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file contains a set of interfaces for derived attribute op interface.
//
//===----------------------------------------------------------------------===//

#ifndef MLIR_INTERFACES_DERIVEDATTRIBUTEOPINTERFACE_H_
#define MLIR_INTERFACES_DERIVEDATTRIBUTEOPINTERFACE_H_

#include "mlir/IR/OpDefinition.h"

/// Include the generated interface declarations.
#include "mlir/Interfaces/DerivedAttributeOpInterface.h.inc"

#endif // MLIR_INTERFACES_DERIVEDATTRIBUTEOPINTERFACE_H_
