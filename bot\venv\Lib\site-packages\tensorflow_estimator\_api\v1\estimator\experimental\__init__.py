# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.estimator.experimental namespace.
"""

import sys as _sys

from tensorflow_estimator.python.estimator.canned.dnn import dnn_logit_fn_builder
from tensorflow_estimator.python.estimator.canned.kmeans import KMeansClustering as KMeans
from tensorflow_estimator.python.estimator.canned.linear import LinearSDCA
from tensorflow_estimator.python.estimator.canned.linear import linear_logit_fn_builder
from tensorflow_estimator.python.estimator.early_stopping import make_early_stopping_hook
from tensorflow_estimator.python.estimator.early_stopping import stop_if_higher_hook
from tensorflow_estimator.python.estimator.early_stopping import stop_if_lower_hook
from tensorflow_estimator.python.estimator.early_stopping import stop_if_no_decrease_hook
from tensorflow_estimator.python.estimator.early_stopping import stop_if_no_increase_hook
from tensorflow_estimator.python.estimator.export.export import build_raw_supervised_input_receiver_fn
from tensorflow_estimator.python.estimator.hooks.hooks import InMemoryEvaluatorHook
from tensorflow_estimator.python.estimator.hooks.hooks import make_stop_at_checkpoint_step_hook
from tensorflow_estimator.python.estimator.model_fn import call_logit_fn
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "estimator.experimental", public_apis=None, deprecation=True,
      has_lite=False)
