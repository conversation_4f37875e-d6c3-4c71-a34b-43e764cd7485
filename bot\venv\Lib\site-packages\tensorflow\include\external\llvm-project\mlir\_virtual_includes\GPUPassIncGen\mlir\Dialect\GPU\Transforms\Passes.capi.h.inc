
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterGPUPasses(void);


/* Create GPU Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateGPUGpuAsyncRegionPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterGPUGpuAsyncRegionPass(void);


/* Create GPU Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateGPUGpuKernelOutlining(void);
MLIR_CAPI_EXPORTED void mlirRegisterGPUGpuKernelOutlining(void);


/* Create GPU Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateGPUGpuLaunchSinkIndexComputations(void);
MLIR_CAPI_EXPORTED void mlirRegisterGPUGpuLaunchSinkIndexComputations(void);


/* Create GPU Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateGPUGpuMapParallelLoopsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterGPUGpuMapParallelLoopsPass(void);



#ifdef __cplusplus
}
#endif
