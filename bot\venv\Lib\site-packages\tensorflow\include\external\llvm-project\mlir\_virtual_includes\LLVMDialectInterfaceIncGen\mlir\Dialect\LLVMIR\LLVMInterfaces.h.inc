/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Interface Declarations                                                     *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace LLVM {
class FastmathFlagsInterface;
namespace detail {
struct FastmathFlagsInterfaceInterfaceTraits {
  struct Concept {
    /// The methods defined by the interface.
    FastmathFlagsAttr (*getFastmathAttr)(const Concept *impl, ::mlir::Operation *);
    StringRef (*getFastmathAttrName)();
  };
  template<typename ConcreteOp>
  class Model : public Concept {
  public:
    using Interface = ::mlir::LLVM::FastmathFlagsInterface;
    Model() : Concept{getFastmathAttr, getFastmathAttrName} {}

    static inline FastmathFlagsAttr getFastmathAttr(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline StringRef getFastmathAttrName();
  };
  template<typename ConcreteOp>
  class FallbackModel : public Concept {
  public:
    using Interface = ::mlir::LLVM::FastmathFlagsInterface;
    FallbackModel() : Concept{getFastmathAttr, getFastmathAttrName} {}

    static inline FastmathFlagsAttr getFastmathAttr(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline StringRef getFastmathAttrName();
  };
  template<typename ConcreteModel, typename ConcreteOp>
  class ExternalModel : public FallbackModel<ConcreteModel> {
  public:
    using ConcreteEntity = ConcreteOp;
    FastmathFlagsAttr getFastmathAttr(::mlir::Operation *tablegen_opaque_val) const;
    static StringRef getFastmathAttrName();
  };
};template <typename ConcreteOp>
struct FastmathFlagsInterfaceTrait;

} // namespace detail
class FastmathFlagsInterface : public ::mlir::OpInterface<FastmathFlagsInterface, detail::FastmathFlagsInterfaceInterfaceTraits> {
public:
  using ::mlir::OpInterface<FastmathFlagsInterface, detail::FastmathFlagsInterfaceInterfaceTraits>::OpInterface;
  template <typename ConcreteOp>
  struct Trait : public detail::FastmathFlagsInterfaceTrait<ConcreteOp> {};
  /// Returns a FastmathFlagsAttr attribute for the operation
  FastmathFlagsAttr getFastmathAttr();
  /// Returns the name of the FastmathFlagsAttr attribute
  ///                          for the operation
  StringRef getFastmathAttrName();
};
namespace detail {
  template <typename ConcreteOp>
  struct FastmathFlagsInterfaceTrait : public ::mlir::OpInterface<FastmathFlagsInterface, detail::FastmathFlagsInterfaceInterfaceTraits>::Trait<ConcreteOp> {
    /// Returns a FastmathFlagsAttr attribute for the operation
    FastmathFlagsAttr getFastmathAttr() {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getFastmathFlagsAttr();
    }
    /// Returns the name of the FastmathFlagsAttr attribute
    ///                          for the operation
    static StringRef getFastmathAttrName() {
      return "fastmathFlags";
    }
  };
}// namespace detail
} // namespace LLVM
} // namespace mlir
namespace mlir {
namespace LLVM {
class AccessGroupOpInterface;
namespace detail {
struct AccessGroupOpInterfaceInterfaceTraits {
  struct Concept {
    /// The methods defined by the interface.
    ArrayAttr (*getAccessGroupsOrNull)(const Concept *impl, ::mlir::Operation *);
    void (*setAccessGroups)(const Concept *impl, ::mlir::Operation *, const ArrayAttr);
  };
  template<typename ConcreteOp>
  class Model : public Concept {
  public:
    using Interface = ::mlir::LLVM::AccessGroupOpInterface;
    Model() : Concept{getAccessGroupsOrNull, setAccessGroups} {}

    static inline ArrayAttr getAccessGroupsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setAccessGroups(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
  };
  template<typename ConcreteOp>
  class FallbackModel : public Concept {
  public:
    using Interface = ::mlir::LLVM::AccessGroupOpInterface;
    FallbackModel() : Concept{getAccessGroupsOrNull, setAccessGroups} {}

    static inline ArrayAttr getAccessGroupsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setAccessGroups(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
  };
  template<typename ConcreteModel, typename ConcreteOp>
  class ExternalModel : public FallbackModel<ConcreteModel> {
  public:
    using ConcreteEntity = ConcreteOp;
    ArrayAttr getAccessGroupsOrNull(::mlir::Operation *tablegen_opaque_val) const;
    void setAccessGroups(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const;
  };
};template <typename ConcreteOp>
struct AccessGroupOpInterfaceTrait;

} // namespace detail
class AccessGroupOpInterface : public ::mlir::OpInterface<AccessGroupOpInterface, detail::AccessGroupOpInterfaceInterfaceTraits> {
public:
  using ::mlir::OpInterface<AccessGroupOpInterface, detail::AccessGroupOpInterfaceInterfaceTraits>::OpInterface;
  template <typename ConcreteOp>
  struct Trait : public detail::AccessGroupOpInterfaceTrait<ConcreteOp> {};
  /// Returns the access groups attribute or nullptr
  ArrayAttr getAccessGroupsOrNull();
  /// Sets the access groups attribute
  void setAccessGroups(const ArrayAttr attr);
};
namespace detail {
  template <typename ConcreteOp>
  struct AccessGroupOpInterfaceTrait : public ::mlir::OpInterface<AccessGroupOpInterface, detail::AccessGroupOpInterfaceInterfaceTraits>::Trait<ConcreteOp> {
    /// Returns the access groups attribute or nullptr
    ArrayAttr getAccessGroupsOrNull() {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getAccessGroupsAttr();
    }
    /// Sets the access groups attribute
    void setAccessGroups(const ArrayAttr attr) {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setAccessGroupsAttr(attr);
    }
    static ::mlir::LogicalResult verifyTrait(::mlir::Operation *op) {
      return detail::verifyAccessGroupOpInterface(op);
    }
  };
}// namespace detail
} // namespace LLVM
} // namespace mlir
namespace mlir {
namespace LLVM {
class AliasAnalysisOpInterface;
namespace detail {
struct AliasAnalysisOpInterfaceInterfaceTraits {
  struct Concept {
    /// The methods defined by the interface.
    ArrayAttr (*getAliasScopesOrNull)(const Concept *impl, ::mlir::Operation *);
    void (*setAliasScopes)(const Concept *impl, ::mlir::Operation *, const ArrayAttr);
    ArrayAttr (*getNoAliasScopesOrNull)(const Concept *impl, ::mlir::Operation *);
    void (*setNoAliasScopes)(const Concept *impl, ::mlir::Operation *, const ArrayAttr);
    ArrayAttr (*getTBAATagsOrNull)(const Concept *impl, ::mlir::Operation *);
    void (*setTBAATags)(const Concept *impl, ::mlir::Operation *, const ArrayAttr);
  };
  template<typename ConcreteOp>
  class Model : public Concept {
  public:
    using Interface = ::mlir::LLVM::AliasAnalysisOpInterface;
    Model() : Concept{getAliasScopesOrNull, setAliasScopes, getNoAliasScopesOrNull, setNoAliasScopes, getTBAATagsOrNull, setTBAATags} {}

    static inline ArrayAttr getAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
    static inline ArrayAttr getNoAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setNoAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
    static inline ArrayAttr getTBAATagsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setTBAATags(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
  };
  template<typename ConcreteOp>
  class FallbackModel : public Concept {
  public:
    using Interface = ::mlir::LLVM::AliasAnalysisOpInterface;
    FallbackModel() : Concept{getAliasScopesOrNull, setAliasScopes, getNoAliasScopesOrNull, setNoAliasScopes, getTBAATagsOrNull, setTBAATags} {}

    static inline ArrayAttr getAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
    static inline ArrayAttr getNoAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setNoAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
    static inline ArrayAttr getTBAATagsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val);
    static inline void setTBAATags(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr);
  };
  template<typename ConcreteModel, typename ConcreteOp>
  class ExternalModel : public FallbackModel<ConcreteModel> {
  public:
    using ConcreteEntity = ConcreteOp;
    ArrayAttr getAliasScopesOrNull(::mlir::Operation *tablegen_opaque_val) const;
    void setAliasScopes(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const;
    ArrayAttr getNoAliasScopesOrNull(::mlir::Operation *tablegen_opaque_val) const;
    void setNoAliasScopes(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const;
    ArrayAttr getTBAATagsOrNull(::mlir::Operation *tablegen_opaque_val) const;
    void setTBAATags(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const;
  };
};template <typename ConcreteOp>
struct AliasAnalysisOpInterfaceTrait;

} // namespace detail
class AliasAnalysisOpInterface : public ::mlir::OpInterface<AliasAnalysisOpInterface, detail::AliasAnalysisOpInterfaceInterfaceTraits> {
public:
  using ::mlir::OpInterface<AliasAnalysisOpInterface, detail::AliasAnalysisOpInterfaceInterfaceTraits>::OpInterface;
  template <typename ConcreteOp>
  struct Trait : public detail::AliasAnalysisOpInterfaceTrait<ConcreteOp> {};
  /// Returns the alias scopes attribute or nullptr
  ArrayAttr getAliasScopesOrNull();
  /// Sets the alias scopes attribute
  void setAliasScopes(const ArrayAttr attr);
  /// Returns the noalias scopes attribute or nullptr
  ArrayAttr getNoAliasScopesOrNull();
  /// Sets the noalias scopes attribute
  void setNoAliasScopes(const ArrayAttr attr);
  /// Returns the tbaa attribute or nullptr
  ArrayAttr getTBAATagsOrNull();
  /// Sets the tbaa attribute
  void setTBAATags(const ArrayAttr attr);
};
namespace detail {
  template <typename ConcreteOp>
  struct AliasAnalysisOpInterfaceTrait : public ::mlir::OpInterface<AliasAnalysisOpInterface, detail::AliasAnalysisOpInterfaceInterfaceTraits>::Trait<ConcreteOp> {
    /// Returns the alias scopes attribute or nullptr
    ArrayAttr getAliasScopesOrNull() {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getAliasScopesAttr();
    }
    /// Sets the alias scopes attribute
    void setAliasScopes(const ArrayAttr attr) {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setAliasScopesAttr(attr);
    }
    /// Returns the noalias scopes attribute or nullptr
    ArrayAttr getNoAliasScopesOrNull() {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getNoaliasScopesAttr();
    }
    /// Sets the noalias scopes attribute
    void setNoAliasScopes(const ArrayAttr attr) {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setNoaliasScopesAttr(attr);
    }
    /// Returns the tbaa attribute or nullptr
    ArrayAttr getTBAATagsOrNull() {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getTbaaAttr();
    }
    /// Sets the tbaa attribute
    void setTBAATags(const ArrayAttr attr) {
      ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setTbaaAttr(attr);
    }
    static ::mlir::LogicalResult verifyTrait(::mlir::Operation *op) {
      return detail::verifyAliasAnalysisOpInterface(op);
    }
  };
}// namespace detail
} // namespace LLVM
} // namespace mlir
namespace mlir {
namespace LLVM {
template<typename ConcreteOp>
FastmathFlagsAttr detail::FastmathFlagsInterfaceInterfaceTraits::Model<ConcreteOp>::getFastmathAttr(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).getFastmathAttr();
}
template<typename ConcreteOp>
StringRef detail::FastmathFlagsInterfaceInterfaceTraits::Model<ConcreteOp>::getFastmathAttrName() {
  return ConcreteOp::getFastmathAttrName();
}
template<typename ConcreteOp>
FastmathFlagsAttr detail::FastmathFlagsInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::getFastmathAttr(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return static_cast<const ConcreteOp *>(impl)->getFastmathAttr(tablegen_opaque_val);
}
template<typename ConcreteOp>
StringRef detail::FastmathFlagsInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::getFastmathAttrName() {
  return ConcreteOp::getFastmathAttrName();
}
template<typename ConcreteModel, typename ConcreteOp>
FastmathFlagsAttr detail::FastmathFlagsInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::getFastmathAttr(::mlir::Operation *tablegen_opaque_val) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getFastmathFlagsAttr();
}
template<typename ConcreteModel, typename ConcreteOp>
StringRef detail::FastmathFlagsInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::getFastmathAttrName() {
return "fastmathFlags";
}
} // namespace LLVM
} // namespace mlir
namespace mlir {
namespace LLVM {
template<typename ConcreteOp>
ArrayAttr detail::AccessGroupOpInterfaceInterfaceTraits::Model<ConcreteOp>::getAccessGroupsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).getAccessGroupsOrNull();
}
template<typename ConcreteOp>
void detail::AccessGroupOpInterfaceInterfaceTraits::Model<ConcreteOp>::setAccessGroups(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).setAccessGroups(attr);
}
template<typename ConcreteOp>
ArrayAttr detail::AccessGroupOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::getAccessGroupsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return static_cast<const ConcreteOp *>(impl)->getAccessGroupsOrNull(tablegen_opaque_val);
}
template<typename ConcreteOp>
void detail::AccessGroupOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::setAccessGroups(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return static_cast<const ConcreteOp *>(impl)->setAccessGroups(tablegen_opaque_val, attr);
}
template<typename ConcreteModel, typename ConcreteOp>
ArrayAttr detail::AccessGroupOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::getAccessGroupsOrNull(::mlir::Operation *tablegen_opaque_val) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getAccessGroupsAttr();
}
template<typename ConcreteModel, typename ConcreteOp>
void detail::AccessGroupOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::setAccessGroups(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setAccessGroupsAttr(attr);
}
} // namespace LLVM
} // namespace mlir
namespace mlir {
namespace LLVM {
template<typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::Model<ConcreteOp>::getAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).getAliasScopesOrNull();
}
template<typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::Model<ConcreteOp>::setAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).setAliasScopes(attr);
}
template<typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::Model<ConcreteOp>::getNoAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).getNoAliasScopesOrNull();
}
template<typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::Model<ConcreteOp>::setNoAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).setNoAliasScopes(attr);
}
template<typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::Model<ConcreteOp>::getTBAATagsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).getTBAATagsOrNull();
}
template<typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::Model<ConcreteOp>::setTBAATags(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return (llvm::cast<ConcreteOp>(tablegen_opaque_val)).setTBAATags(attr);
}
template<typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::getAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return static_cast<const ConcreteOp *>(impl)->getAliasScopesOrNull(tablegen_opaque_val);
}
template<typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::setAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return static_cast<const ConcreteOp *>(impl)->setAliasScopes(tablegen_opaque_val, attr);
}
template<typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::getNoAliasScopesOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return static_cast<const ConcreteOp *>(impl)->getNoAliasScopesOrNull(tablegen_opaque_val);
}
template<typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::setNoAliasScopes(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return static_cast<const ConcreteOp *>(impl)->setNoAliasScopes(tablegen_opaque_val, attr);
}
template<typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::getTBAATagsOrNull(const Concept *impl, ::mlir::Operation *tablegen_opaque_val) {
  return static_cast<const ConcreteOp *>(impl)->getTBAATagsOrNull(tablegen_opaque_val);
}
template<typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::FallbackModel<ConcreteOp>::setTBAATags(const Concept *impl, ::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) {
  return static_cast<const ConcreteOp *>(impl)->setTBAATags(tablegen_opaque_val, attr);
}
template<typename ConcreteModel, typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::getAliasScopesOrNull(::mlir::Operation *tablegen_opaque_val) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getAliasScopesAttr();
}
template<typename ConcreteModel, typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::setAliasScopes(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setAliasScopesAttr(attr);
}
template<typename ConcreteModel, typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::getNoAliasScopesOrNull(::mlir::Operation *tablegen_opaque_val) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getNoaliasScopesAttr();
}
template<typename ConcreteModel, typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::setNoAliasScopes(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setNoaliasScopesAttr(attr);
}
template<typename ConcreteModel, typename ConcreteOp>
ArrayAttr detail::AliasAnalysisOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::getTBAATagsOrNull(::mlir::Operation *tablegen_opaque_val) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        return op.getTbaaAttr();
}
template<typename ConcreteModel, typename ConcreteOp>
void detail::AliasAnalysisOpInterfaceInterfaceTraits::ExternalModel<ConcreteModel, ConcreteOp>::setTBAATags(::mlir::Operation *tablegen_opaque_val, const ArrayAttr attr) const {
ConcreteOp op = cast<ConcreteOp>(this->getOperation());
        op.setTbaaAttr(attr);
}
} // namespace LLVM
} // namespace mlir
