/* Copyright 2022 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/
#ifndef TENSORFLOW_CORE_KERNELS_TENSOR_LIST_UTIL_H_
#define TENSORFLOW_CORE_KERNELS_TENSOR_LIST_UTIL_H_

#include <functional>

#include "tensorflow/core/platform/status.h"

namespace tensorflow {

class OpKernelContext;
class TensorList;
class Tensor;

Status TensorListBinaryAdd(
    OpKernelContext* c, const TensorList& a, const TensorList& b,
    TensorList* out,
    std::function<Status(OpKernelContext* ctx, const Tensor& a, const Tensor& b,
                         Tensor* out)>
        binary_add_func);

Status TensorListZerosLike(
    OpKernelContext* c, const TensorList& x, TensorList* y,
    std::function<Status(OpKernelContext* ctx, const Tensor& input,
                         Tensor* out)>
        zeros_like_func);

}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_KERNELS_TENSOR_LIST_UTIL_H_
