# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/cpp_shape_inference.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import full_type_pb2 as tensorboard_dot_compat_dot_proto_dot_full__type__pb2
from tensorboard.compat.proto import tensor_shape_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__shape__pb2
from tensorboard.compat.proto import types_pb2 as tensorboard_dot_compat_dot_proto_dot_types__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2tensorboard/compat/proto/cpp_shape_inference.proto\x12\x0btensorboard\x1a(tensorboard/compat/proto/full_type.proto\x1a+tensorboard/compat/proto/tensor_shape.proto\x1a$tensorboard/compat/proto/types.proto\"\xa1\x03\n\x17\x43ppShapeInferenceResult\x12,\n\x05shape\x18\x01 \x01(\x0b\x32\x1d.tensorboard.TensorShapeProto\x12\x44\n\x0bhandle_data\x18\x04 \x01(\x0b\x32/.tensorboard.CppShapeInferenceResult.HandleData\x1a\x96\x01\n\x12HandleShapeAndType\x12,\n\x05shape\x18\x01 \x01(\x0b\x32\x1d.tensorboard.TensorShapeProto\x12$\n\x05\x64type\x18\x02 \x01(\x0e\x32\x15.tensorboard.DataType\x12&\n\x04type\x18\x04 \x01(\x0b\x32\x18.tensorboard.FullTypeDefJ\x04\x08\x03\x10\x04\x1am\n\nHandleData\x12\x0e\n\x06is_set\x18\x01 \x01(\x08\x12O\n\x0eshape_and_type\x18\x02 \x03(\x0b\x32\x37.tensorboard.CppShapeInferenceResult.HandleShapeAndTypeJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\"e\n\x1d\x43ppShapeInferenceInputsNeeded\x12\x1c\n\x14input_tensors_needed\x18\x01 \x03(\x05\x12&\n\x1einput_tensors_as_shapes_needed\x18\x02 \x03(\x05\x42\x61Z\\github.com/tensorflow/tensorflow/tensorflow/go/python/framework/cpp_shape_inference_go_proto\xf8\x01\x01\x62\x06proto3')



_CPPSHAPEINFERENCERESULT = DESCRIPTOR.message_types_by_name['CppShapeInferenceResult']
_CPPSHAPEINFERENCERESULT_HANDLESHAPEANDTYPE = _CPPSHAPEINFERENCERESULT.nested_types_by_name['HandleShapeAndType']
_CPPSHAPEINFERENCERESULT_HANDLEDATA = _CPPSHAPEINFERENCERESULT.nested_types_by_name['HandleData']
_CPPSHAPEINFERENCEINPUTSNEEDED = DESCRIPTOR.message_types_by_name['CppShapeInferenceInputsNeeded']
CppShapeInferenceResult = _reflection.GeneratedProtocolMessageType('CppShapeInferenceResult', (_message.Message,), {

  'HandleShapeAndType' : _reflection.GeneratedProtocolMessageType('HandleShapeAndType', (_message.Message,), {
    'DESCRIPTOR' : _CPPSHAPEINFERENCERESULT_HANDLESHAPEANDTYPE,
    '__module__' : 'tensorboard.compat.proto.cpp_shape_inference_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.CppShapeInferenceResult.HandleShapeAndType)
    })
  ,

  'HandleData' : _reflection.GeneratedProtocolMessageType('HandleData', (_message.Message,), {
    'DESCRIPTOR' : _CPPSHAPEINFERENCERESULT_HANDLEDATA,
    '__module__' : 'tensorboard.compat.proto.cpp_shape_inference_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.CppShapeInferenceResult.HandleData)
    })
  ,
  'DESCRIPTOR' : _CPPSHAPEINFERENCERESULT,
  '__module__' : 'tensorboard.compat.proto.cpp_shape_inference_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.CppShapeInferenceResult)
  })
_sym_db.RegisterMessage(CppShapeInferenceResult)
_sym_db.RegisterMessage(CppShapeInferenceResult.HandleShapeAndType)
_sym_db.RegisterMessage(CppShapeInferenceResult.HandleData)

CppShapeInferenceInputsNeeded = _reflection.GeneratedProtocolMessageType('CppShapeInferenceInputsNeeded', (_message.Message,), {
  'DESCRIPTOR' : _CPPSHAPEINFERENCEINPUTSNEEDED,
  '__module__' : 'tensorboard.compat.proto.cpp_shape_inference_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.CppShapeInferenceInputsNeeded)
  })
_sym_db.RegisterMessage(CppShapeInferenceInputsNeeded)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\\github.com/tensorflow/tensorflow/tensorflow/go/python/framework/cpp_shape_inference_go_proto\370\001\001'
  _CPPSHAPEINFERENCERESULT._serialized_start=193
  _CPPSHAPEINFERENCERESULT._serialized_end=610
  _CPPSHAPEINFERENCERESULT_HANDLESHAPEANDTYPE._serialized_start=337
  _CPPSHAPEINFERENCERESULT_HANDLESHAPEANDTYPE._serialized_end=487
  _CPPSHAPEINFERENCERESULT_HANDLEDATA._serialized_start=489
  _CPPSHAPEINFERENCERESULT_HANDLEDATA._serialized_end=598
  _CPPSHAPEINFERENCEINPUTSNEEDED._serialized_start=612
  _CPPSHAPEINFERENCEINPUTSNEEDED._serialized_end=713
# @@protoc_insertion_point(module_scope)
