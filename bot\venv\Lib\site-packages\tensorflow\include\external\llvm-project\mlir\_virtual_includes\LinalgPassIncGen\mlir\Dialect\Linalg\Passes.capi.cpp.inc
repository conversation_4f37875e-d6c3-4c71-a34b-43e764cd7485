/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Linalg Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterLinalgPasses(void) {
  registerLinalgPasses();
}

MlirPass mlirCreateLinalgConvertElementwiseToLinalg(void) {
  return wrap(mlir::createConvertElementwiseToLinalgPass().release());
}
void mlirRegisterLinalgConvertElementwiseToLinalg(void) {
  registerConvertElementwiseToLinalg();
}


MlirPass mlirCreateLinalgLinalgBufferize(void) {
  return wrap(mlir::createLinalgBufferizePass().release());
}
void mlirRegisterLinalgLinalgBufferize(void) {
  registerLinalgBufferize();
}


MlirPass mlirCreateLinalgLinalgDetensorize(void) {
  return wrap(mlir::createLinalgDetensorizePass().release());
}
void mlirRegisterLinalgLinalgDetensorize(void) {
  registerLinalgDetensorize();
}


MlirPass mlirCreateLinalgLinalgElementwiseOpFusion(void) {
  return wrap(mlir::createLinalgElementwiseOpFusionPass().release());
}
void mlirRegisterLinalgLinalgElementwiseOpFusion(void) {
  registerLinalgElementwiseOpFusion();
}


MlirPass mlirCreateLinalgLinalgFoldUnitExtentDims(void) {
  return wrap(mlir::createLinalgFoldUnitExtentDimsPass().release());
}
void mlirRegisterLinalgLinalgFoldUnitExtentDims(void) {
  registerLinalgFoldUnitExtentDims();
}


MlirPass mlirCreateLinalgLinalgGeneralization(void) {
  return wrap(mlir::createLinalgGeneralizationPass().release());
}
void mlirRegisterLinalgLinalgGeneralization(void) {
  registerLinalgGeneralization();
}


MlirPass mlirCreateLinalgLinalgInlineScalarOperands(void) {
  return wrap(mlir::createLinalgInlineScalarOperandsPass().release());
}
void mlirRegisterLinalgLinalgInlineScalarOperands(void) {
  registerLinalgInlineScalarOperands();
}


MlirPass mlirCreateLinalgLinalgLowerToAffineLoops(void) {
  return wrap(mlir::createConvertLinalgToAffineLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToAffineLoops(void) {
  registerLinalgLowerToAffineLoops();
}


MlirPass mlirCreateLinalgLinalgLowerToLoops(void) {
  return wrap(mlir::createConvertLinalgToLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToLoops(void) {
  registerLinalgLowerToLoops();
}


MlirPass mlirCreateLinalgLinalgLowerToParallelLoops(void) {
  return wrap(mlir::createConvertLinalgToParallelLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToParallelLoops(void) {
  registerLinalgLowerToParallelLoops();
}


MlirPass mlirCreateLinalgLinalgNamedOpConversion(void) {
  return wrap(mlir::createLinalgNamedOpConversionPass().release());
}
void mlirRegisterLinalgLinalgNamedOpConversion(void) {
  registerLinalgNamedOpConversion();
}

