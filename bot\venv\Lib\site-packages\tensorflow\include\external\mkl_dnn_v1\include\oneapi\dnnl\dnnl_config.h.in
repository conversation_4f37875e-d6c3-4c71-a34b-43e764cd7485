/*******************************************************************************
* Copyright 2019-2022 Intel Corporation
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*******************************************************************************/

#ifndef ONEAPI_DNNL_DNNL_CONFIG_H
#define ONEAPI_DNNL_DNNL_CONFIG_H

#include "oneapi/dnnl/dnnl_types.h"

/// @cond DO_NOT_DOCUMENT_THIS

// All symbols shall be internal unless marked as DNNL_API
#if defined _WIN32 || defined __CYGWIN__
#define DNNL_HELPER_DLL_IMPORT __declspec(dllimport)
#define DNNL_HELPER_DLL_EXPORT __declspec(dllexport)
#else
#if __GNUC__ >= 4
#define DNNL_HELPER_DLL_IMPORT __attribute__((visibility("default")))
#define DNNL_HELPER_DLL_EXPORT __attribute__((visibility("default")))
#else
#define DNNL_HELPER_DLL_IMPORT
#define DNNL_HELPER_DLL_EXPORT
#endif
#endif

#ifdef DNNL_DLL
#ifdef DNNL_DLL_EXPORTS
#define DNNL_API DNNL_HELPER_DLL_EXPORT
#else
#define DNNL_API DNNL_HELPER_DLL_IMPORT
#endif
#else
#define DNNL_API
#endif

#if defined(__GNUC__)
#define DNNL_DEPRECATED __attribute__((deprecated))
#elif defined(_MSC_VER)
#define DNNL_DEPRECATED __declspec(deprecated)
#else
#define DNNL_DEPRECATED
#endif

/// @endcond

// clang-format off

// oneDNN CPU threading runtime
#cmakedefine DNNL_CPU_THREADING_RUNTIME DNNL_RUNTIME_${DNNL_CPU_THREADING_RUNTIME}

// oneDNN CPU engine runtime
#cmakedefine DNNL_CPU_RUNTIME DNNL_RUNTIME_${DNNL_CPU_RUNTIME}

// oneDNN GPU engine runtime
#cmakedefine DNNL_GPU_RUNTIME DNNL_RUNTIME_${DNNL_GPU_RUNTIME}

// clang-format on

#if defined(DNNL_CPU_RUNTIME) && defined(DNNL_GPU_RUNTIME)
#if (DNNL_CPU_RUNTIME == DNNL_RUNTIME_OCL)
#error "Unexpected DNNL_CPU_RUNTIME"
#endif
#if (DNNL_GPU_RUNTIME != DNNL_RUNTIME_NONE) \
        && (DNNL_GPU_RUNTIME != DNNL_RUNTIME_OCL) \
        && (DNNL_GPU_RUNTIME != DNNL_RUNTIME_SYCL)
#error "Unexpected DNNL_GPU_RUNTIME"
#endif
#if (DNNL_CPU_RUNTIME == DNNL_RUNTIME_NONE \
        && DNNL_GPU_RUNTIME == DNNL_RUNTIME_NONE)
#error "At least one runtime must be specified"
#endif
#else
#error "BOTH DNNL_CPU_RUNTIME and DNNL_GPU_RUNTIME must be defined"
#endif

// For SYCL CPU, a primitive may be created and executed in different threads
// hence the global scratchpad does not work. This enables concurrent execution
// when CPU runtime is SYCL to avoid the issue.
#if DNNL_CPU_RUNTIME == DNNL_RUNTIME_SYCL
#ifndef DNNL_ENABLE_CONCURRENT_EXEC
#define DNNL_ENABLE_CONCURRENT_EXEC
#endif
#endif

// When defined, primitive cache stores runtime objects.
#cmakedefine DNNL_USE_RT_OBJECTS_IN_PRIMITIVE_CACHE

// When defined, DPCPP is supported.
#cmakedefine DNNL_WITH_SYCL

// When defined, Level Zero is supported.
#cmakedefine DNNL_WITH_LEVEL_ZERO

// When defined, SYCL CUDA backend is used.
#cmakedefine DNNL_SYCL_CUDA

// When defined, SYCL HIP backend is used.
#cmakedefine DNNL_SYCL_HIP

// When defined, stack checker is enabled.
#cmakedefine DNNL_ENABLE_STACK_CHECKER

// When defined, experimental features are enabled.
#cmakedefine DNNL_EXPERIMENTAL

// List of configurating build controls
// Workload controls
#cmakedefine01 BUILD_TRAINING
#cmakedefine01 BUILD_INFERENCE
// Primitive controls
#cmakedefine01 BUILD_PRIMITIVE_ALL
#cmakedefine01 BUILD_BATCH_NORMALIZATION
#cmakedefine01 BUILD_BINARY
#cmakedefine01 BUILD_CONCAT
#cmakedefine01 BUILD_CONVOLUTION
#cmakedefine01 BUILD_DECONVOLUTION
#cmakedefine01 BUILD_ELTWISE
#cmakedefine01 BUILD_INNER_PRODUCT
#cmakedefine01 BUILD_LAYER_NORMALIZATION
#cmakedefine01 BUILD_LRN
#cmakedefine01 BUILD_MATMUL
#cmakedefine01 BUILD_POOLING
#cmakedefine01 BUILD_PRELU
#cmakedefine01 BUILD_REDUCTION
#cmakedefine01 BUILD_REORDER
#cmakedefine01 BUILD_RESAMPLING
#cmakedefine01 BUILD_RNN
#cmakedefine01 BUILD_SHUFFLE
#cmakedefine01 BUILD_SOFTMAX
#cmakedefine01 BUILD_SUM
// Primitives CPU ISA controls
#cmakedefine01 BUILD_PRIMITIVE_CPU_ISA_ALL
#cmakedefine01 BUILD_SSE41
#cmakedefine01 BUILD_AVX2
#cmakedefine01 BUILD_AVX512
#cmakedefine01 BUILD_AMX
// Primitives GPU ISA controls
#cmakedefine01 BUILD_PRIMITIVE_GPU_ISA_ALL
#cmakedefine01 BUILD_GEN9
#cmakedefine01 BUILD_GEN11
#cmakedefine01 BUILD_XELP
#cmakedefine01 BUILD_XEHP
#cmakedefine01 BUILD_XEHPG
#cmakedefine01 BUILD_XEHPC
#endif
