/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_AFFINEDATACOPYGENERATION
#define GEN_PASS_DECL_AFFINEEXPANDINDEXOPS
#define GEN_PASS_DECL_AFFINELOOPFUSION
#define GEN_PASS_DECL_AFFINELOOPINVARIANTCODEMOTION
#define GEN_PASS_DECL_AFFINELOOPNORMALIZE
#define GEN_PASS_DECL_AFFINELOOPTILING
#define GEN_PASS_DECL_AFFINELOOPUNROLL
#define GEN_PASS_DECL_AFFINELOOPUNROLLANDJAM
#define GEN_PASS_DECL_AFFINEPARALLELIZE
#define GEN_PASS_DECL_AFFINEPIPELINEDATATRANSFER
#define GEN_PASS_DECL_AFFINESCALARREPLACEMENT
#define GEN_PASS_DECL_AFFINEVECTORIZE
#define GEN_PASS_DECL_LOOPCOALESCING
#define GEN_PASS_DECL_SIMPLIFYAFFINESTRUCTURES
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AffineDataCopyGeneration
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINEDATACOPYGENERATION
struct AffineDataCopyGenerationOptions {
  uint64_t fastMemoryCapacity = std::numeric_limits<uint64_t>::max();
  unsigned fastMemorySpace = 1;
  bool generateDma = true;
  int minDmaTransferSize = 1024;
  unsigned slowMemorySpace = 0;
  bool skipNonUnitStrideLoops = false;
  unsigned tagMemorySpace = 0;
};
#undef GEN_PASS_DECL_AFFINEDATACOPYGENERATION
#endif // GEN_PASS_DECL_AFFINEDATACOPYGENERATION
#ifdef GEN_PASS_DEF_AFFINEDATACOPYGENERATION
namespace impl {

template <typename DerivedT>
class AffineDataCopyGenerationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineDataCopyGenerationBase;

  AffineDataCopyGenerationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineDataCopyGenerationBase(const AffineDataCopyGenerationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-data-copy-generate");
  }
  ::llvm::StringRef getArgument() const override { return "affine-data-copy-generate"; }

  ::llvm::StringRef getDescription() const override { return "Generate explicit copying for affine memory operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineDataCopyGeneration");
  }
  ::llvm::StringRef getName() const override { return "AffineDataCopyGeneration"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineDataCopyGenerationBase<DerivedT>)

  AffineDataCopyGenerationBase(const AffineDataCopyGenerationOptions &options) : AffineDataCopyGenerationBase() {
    fastMemoryCapacity = options.fastMemoryCapacity;
    fastMemorySpace = options.fastMemorySpace;
    generateDma = options.generateDma;
    minDmaTransferSize = options.minDmaTransferSize;
    slowMemorySpace = options.slowMemorySpace;
    skipNonUnitStrideLoops = options.skipNonUnitStrideLoops;
    tagMemorySpace = options.tagMemorySpace;
  }
protected:
  ::mlir::Pass::Option<uint64_t> fastMemoryCapacity{*this, "fast-mem-capacity", ::llvm::cl::desc("Set fast memory space capacity in KiB (default: unlimited)"), ::llvm::cl::init(std::numeric_limits<uint64_t>::max())};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fast-mem-space", ::llvm::cl::desc("Fast memory space identifier for copy generation (default: 1)"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> generateDma{*this, "generate-dma", ::llvm::cl::desc("Generate DMA instead of point-wise copy"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int> minDmaTransferSize{*this, "min-dma-transfer", ::llvm::cl::desc("Minimum DMA transfer size supported by the target in bytes"), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> slowMemorySpace{*this, "slow-mem-space", ::llvm::cl::desc("Slow memory space identifier for copy generation (default: 0)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> skipNonUnitStrideLoops{*this, "skip-non-unit-stride-loops", ::llvm::cl::desc("Testing purposes: avoid non-unit stride loop choice depths for copy placement"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> tagMemorySpace{*this, "tag-mem-space", ::llvm::cl::desc("Tag memory space identifier for copy generation (default: 0)"), ::llvm::cl::init(0)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINEDATACOPYGENERATION
#endif // GEN_PASS_DEF_AFFINEDATACOPYGENERATION

//===----------------------------------------------------------------------===//
// AffineExpandIndexOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINEEXPANDINDEXOPS
#undef GEN_PASS_DECL_AFFINEEXPANDINDEXOPS
#endif // GEN_PASS_DECL_AFFINEEXPANDINDEXOPS
#ifdef GEN_PASS_DEF_AFFINEEXPANDINDEXOPS
namespace impl {

template <typename DerivedT>
class AffineExpandIndexOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = AffineExpandIndexOpsBase;

  AffineExpandIndexOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AffineExpandIndexOpsBase(const AffineExpandIndexOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-expand-index-ops");
  }
  ::llvm::StringRef getArgument() const override { return "affine-expand-index-ops"; }

  ::llvm::StringRef getDescription() const override { return "Lower affine operations operating on indices into more fundamental operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineExpandIndexOps");
  }
  ::llvm::StringRef getName() const override { return "AffineExpandIndexOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineExpandIndexOpsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINEEXPANDINDEXOPS
#endif // GEN_PASS_DEF_AFFINEEXPANDINDEXOPS

//===----------------------------------------------------------------------===//
// AffineLoopFusion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINELOOPFUSION
struct AffineLoopFusionOptions {
  double computeToleranceThreshold = 0.30f;
  unsigned fastMemorySpace = 0;
  uint64_t localBufSizeThreshold = 0;
  bool maximalFusion = false;
  enum FusionMode affineFusionMode = mlir::affine::FusionMode::Greedy;
};
#undef GEN_PASS_DECL_AFFINELOOPFUSION
#endif // GEN_PASS_DECL_AFFINELOOPFUSION
#ifdef GEN_PASS_DEF_AFFINELOOPFUSION
namespace impl {

template <typename DerivedT>
class AffineLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = AffineLoopFusionBase;

  AffineLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopFusionBase(const AffineLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopFusionBase<DerivedT>)

  AffineLoopFusionBase(const AffineLoopFusionOptions &options) : AffineLoopFusionBase() {
    computeToleranceThreshold = options.computeToleranceThreshold;
    fastMemorySpace = options.fastMemorySpace;
    localBufSizeThreshold = options.localBufSizeThreshold;
    maximalFusion = options.maximalFusion;
    affineFusionMode = options.affineFusionMode;
  }
protected:
  ::mlir::Pass::Option<double> computeToleranceThreshold{*this, "fusion-compute-tolerance", ::llvm::cl::desc("Fractional increase in additional computation tolerated while fusing"), ::llvm::cl::init(0.30f)};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fusion-fast-mem-space", ::llvm::cl::desc("Faster memory space number to promote fusion buffers to"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<uint64_t> localBufSizeThreshold{*this, "fusion-local-buf-threshold", ::llvm::cl::desc("Threshold size (KiB) for promoting local buffers to fast memory space"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> maximalFusion{*this, "fusion-maximal", ::llvm::cl::desc("Enables maximal loop fusion"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<enum FusionMode> affineFusionMode{*this, "mode", ::llvm::cl::desc("fusion mode to attempt"), ::llvm::cl::init(mlir::affine::FusionMode::Greedy), llvm::cl::values(clEnumValN(mlir::affine::FusionMode::Greedy, "greedy", "Perform greedy (both producer-consumer and sibling)  fusion"), clEnumValN( mlir::affine::FusionMode::ProducerConsumer, "producer", "Perform only producer-consumer fusion"), clEnumValN( mlir::affine::FusionMode::Sibling, "sibling", "Perform only sibling fusion"))};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINELOOPFUSION
#endif // GEN_PASS_DEF_AFFINELOOPFUSION

//===----------------------------------------------------------------------===//
// AffineLoopInvariantCodeMotion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINELOOPINVARIANTCODEMOTION
#undef GEN_PASS_DECL_AFFINELOOPINVARIANTCODEMOTION
#endif // GEN_PASS_DECL_AFFINELOOPINVARIANTCODEMOTION
#ifdef GEN_PASS_DEF_AFFINELOOPINVARIANTCODEMOTION
namespace impl {

template <typename DerivedT>
class AffineLoopInvariantCodeMotionBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopInvariantCodeMotionBase;

  AffineLoopInvariantCodeMotionBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopInvariantCodeMotionBase(const AffineLoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopInvariantCodeMotionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINELOOPINVARIANTCODEMOTION
#endif // GEN_PASS_DEF_AFFINELOOPINVARIANTCODEMOTION

//===----------------------------------------------------------------------===//
// AffineLoopNormalize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINELOOPNORMALIZE
struct AffineLoopNormalizeOptions {
  bool promoteSingleIter = true;
};
#undef GEN_PASS_DECL_AFFINELOOPNORMALIZE
#endif // GEN_PASS_DECL_AFFINELOOPNORMALIZE
#ifdef GEN_PASS_DEF_AFFINELOOPNORMALIZE
namespace impl {

template <typename DerivedT>
class AffineLoopNormalizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopNormalizeBase;

  AffineLoopNormalizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopNormalizeBase(const AffineLoopNormalizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-normalize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-normalize"; }

  ::llvm::StringRef getDescription() const override { return "Apply normalization transformations to affine loop-like ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopNormalize");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopNormalize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopNormalizeBase<DerivedT>)

  AffineLoopNormalizeBase(const AffineLoopNormalizeOptions &options) : AffineLoopNormalizeBase() {
    promoteSingleIter = options.promoteSingleIter;
  }
protected:
  ::mlir::Pass::Option<bool> promoteSingleIter{*this, "promote-single-iter", ::llvm::cl::desc("Promote single iteration loops"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINELOOPNORMALIZE
#endif // GEN_PASS_DEF_AFFINELOOPNORMALIZE

//===----------------------------------------------------------------------===//
// AffineLoopTiling
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINELOOPTILING
struct AffineLoopTilingOptions {
  uint64_t cacheSizeInKiB = 512;
  bool separate = false;
  unsigned tileSize;
  ::llvm::ArrayRef<unsigned> tileSizes;
};
#undef GEN_PASS_DECL_AFFINELOOPTILING
#endif // GEN_PASS_DECL_AFFINELOOPTILING
#ifdef GEN_PASS_DEF_AFFINELOOPTILING
namespace impl {

template <typename DerivedT>
class AffineLoopTilingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopTilingBase;

  AffineLoopTilingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopTilingBase(const AffineLoopTilingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-tile");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-tile"; }

  ::llvm::StringRef getDescription() const override { return "Tile affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopTilingBase<DerivedT>)

  AffineLoopTilingBase(const AffineLoopTilingOptions &options) : AffineLoopTilingBase() {
    cacheSizeInKiB = options.cacheSizeInKiB;
    separate = options.separate;
    tileSize = options.tileSize;
    tileSizes = options.tileSizes;
  }
protected:
  ::mlir::Pass::Option<uint64_t> cacheSizeInKiB{*this, "cache-size", ::llvm::cl::desc("Set size of cache to tile for in KiB (default: 512)"), ::llvm::cl::init(512)};
  ::mlir::Pass::Option<bool> separate{*this, "separate", ::llvm::cl::desc("Separate full and partial tiles (default: false)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> tileSize{*this, "tile-size", ::llvm::cl::desc("Use this tile size for all loops")};
  ::mlir::Pass::ListOption<unsigned> tileSizes{*this, "tile-sizes", ::llvm::cl::desc("List of tile sizes for each perfect nest (overridden by -tile-size)")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINELOOPTILING
#endif // GEN_PASS_DEF_AFFINELOOPTILING

//===----------------------------------------------------------------------===//
// AffineLoopUnroll
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINELOOPUNROLL
struct AffineLoopUnrollOptions {
  unsigned unrollFactor = 4;
  bool unrollUpToFactor = false;
  bool unrollFull = false;
  unsigned numRepetitions = 1;
  unsigned unrollFullThreshold = 1;
  bool cleanUpUnroll = false;
};
#undef GEN_PASS_DECL_AFFINELOOPUNROLL
#endif // GEN_PASS_DECL_AFFINELOOPUNROLL
#ifdef GEN_PASS_DEF_AFFINELOOPUNROLL
namespace impl {

template <typename DerivedT>
class AffineLoopUnrollBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopUnrollBase;

  AffineLoopUnrollBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopUnrollBase(const AffineLoopUnrollBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-unroll");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-unroll"; }

  ::llvm::StringRef getDescription() const override { return "Unroll affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopUnroll");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopUnroll"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopUnrollBase<DerivedT>)

  AffineLoopUnrollBase(const AffineLoopUnrollOptions &options) : AffineLoopUnrollBase() {
    unrollFactor = options.unrollFactor;
    unrollUpToFactor = options.unrollUpToFactor;
    unrollFull = options.unrollFull;
    numRepetitions = options.numRepetitions;
    unrollFullThreshold = options.unrollFullThreshold;
    cleanUpUnroll = options.cleanUpUnroll;
  }
protected:
  ::mlir::Pass::Option<unsigned> unrollFactor{*this, "unroll-factor", ::llvm::cl::desc("Use this unroll factor for all loops being unrolled"), ::llvm::cl::init(4)};
  ::mlir::Pass::Option<bool> unrollUpToFactor{*this, "unroll-up-to-factor", ::llvm::cl::desc("Allow unrolling up to the factor specified"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> unrollFull{*this, "unroll-full", ::llvm::cl::desc("Fully unroll loops"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> numRepetitions{*this, "unroll-num-reps", ::llvm::cl::desc("Unroll innermost loops repeatedly this many times"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<unsigned> unrollFullThreshold{*this, "unroll-full-threshold", ::llvm::cl::desc("Unroll all loops with trip count less than or equal to this"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> cleanUpUnroll{*this, "cleanup-unroll", ::llvm::cl::desc("Fully unroll the cleanup loop when possible."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINELOOPUNROLL
#endif // GEN_PASS_DEF_AFFINELOOPUNROLL

//===----------------------------------------------------------------------===//
// AffineLoopUnrollAndJam
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINELOOPUNROLLANDJAM
struct AffineLoopUnrollAndJamOptions {
  unsigned unrollJamFactor = 4;
};
#undef GEN_PASS_DECL_AFFINELOOPUNROLLANDJAM
#endif // GEN_PASS_DECL_AFFINELOOPUNROLLANDJAM
#ifdef GEN_PASS_DEF_AFFINELOOPUNROLLANDJAM
namespace impl {

template <typename DerivedT>
class AffineLoopUnrollAndJamBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopUnrollAndJamBase;

  AffineLoopUnrollAndJamBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopUnrollAndJamBase(const AffineLoopUnrollAndJamBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-unroll-jam");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-unroll-jam"; }

  ::llvm::StringRef getDescription() const override { return "Unroll and jam affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopUnrollAndJam");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopUnrollAndJam"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopUnrollAndJamBase<DerivedT>)

  AffineLoopUnrollAndJamBase(const AffineLoopUnrollAndJamOptions &options) : AffineLoopUnrollAndJamBase() {
    unrollJamFactor = options.unrollJamFactor;
  }
protected:
  ::mlir::Pass::Option<unsigned> unrollJamFactor{*this, "unroll-jam-factor", ::llvm::cl::desc("Use this unroll jam factor for all loops (default 4)"), ::llvm::cl::init(4)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINELOOPUNROLLANDJAM
#endif // GEN_PASS_DEF_AFFINELOOPUNROLLANDJAM

//===----------------------------------------------------------------------===//
// AffineParallelize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINEPARALLELIZE
struct AffineParallelizeOptions {
  unsigned maxNested = -1u;
  bool parallelReductions = false;
};
#undef GEN_PASS_DECL_AFFINEPARALLELIZE
#endif // GEN_PASS_DECL_AFFINEPARALLELIZE
#ifdef GEN_PASS_DEF_AFFINEPARALLELIZE
namespace impl {

template <typename DerivedT>
class AffineParallelizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineParallelizeBase;

  AffineParallelizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineParallelizeBase(const AffineParallelizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-parallelize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-parallelize"; }

  ::llvm::StringRef getDescription() const override { return "Convert affine.for ops into 1-D affine.parallel"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineParallelize");
  }
  ::llvm::StringRef getName() const override { return "AffineParallelize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineParallelizeBase<DerivedT>)

  AffineParallelizeBase(const AffineParallelizeOptions &options) : AffineParallelizeBase() {
    maxNested = options.maxNested;
    parallelReductions = options.parallelReductions;
  }
protected:
  ::mlir::Pass::Option<unsigned> maxNested{*this, "max-nested", ::llvm::cl::desc("Maximum number of nested parallel loops to produce. Defaults to unlimited (UINT_MAX)."), ::llvm::cl::init(-1u)};
  ::mlir::Pass::Option<bool> parallelReductions{*this, "parallel-reductions", ::llvm::cl::desc("Whether to parallelize reduction loops. Defaults to false."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINEPARALLELIZE
#endif // GEN_PASS_DEF_AFFINEPARALLELIZE

//===----------------------------------------------------------------------===//
// AffinePipelineDataTransfer
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINEPIPELINEDATATRANSFER
#undef GEN_PASS_DECL_AFFINEPIPELINEDATATRANSFER
#endif // GEN_PASS_DECL_AFFINEPIPELINEDATATRANSFER
#ifdef GEN_PASS_DEF_AFFINEPIPELINEDATATRANSFER
namespace impl {

template <typename DerivedT>
class AffinePipelineDataTransferBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffinePipelineDataTransferBase;

  AffinePipelineDataTransferBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffinePipelineDataTransferBase(const AffinePipelineDataTransferBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-pipeline-data-transfer");
  }
  ::llvm::StringRef getArgument() const override { return "affine-pipeline-data-transfer"; }

  ::llvm::StringRef getDescription() const override { return "Pipeline non-blocking data transfers between explicitly managed levels of the memory hierarchy"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffinePipelineDataTransfer");
  }
  ::llvm::StringRef getName() const override { return "AffinePipelineDataTransfer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffinePipelineDataTransferBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINEPIPELINEDATATRANSFER
#endif // GEN_PASS_DEF_AFFINEPIPELINEDATATRANSFER

//===----------------------------------------------------------------------===//
// AffineScalarReplacement
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINESCALARREPLACEMENT
#undef GEN_PASS_DECL_AFFINESCALARREPLACEMENT
#endif // GEN_PASS_DECL_AFFINESCALARREPLACEMENT
#ifdef GEN_PASS_DEF_AFFINESCALARREPLACEMENT
namespace impl {

template <typename DerivedT>
class AffineScalarReplacementBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineScalarReplacementBase;

  AffineScalarReplacementBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineScalarReplacementBase(const AffineScalarReplacementBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-scalrep");
  }
  ::llvm::StringRef getArgument() const override { return "affine-scalrep"; }

  ::llvm::StringRef getDescription() const override { return "Replace affine memref accesses by scalars by forwarding stores to loads and eliminating redundant loads"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineScalarReplacement");
  }
  ::llvm::StringRef getName() const override { return "AffineScalarReplacement"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineScalarReplacementBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_AFFINESCALARREPLACEMENT
#endif // GEN_PASS_DEF_AFFINESCALARREPLACEMENT

//===----------------------------------------------------------------------===//
// AffineVectorize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AFFINEVECTORIZE
struct AffineVectorizeOptions {
  ::llvm::ArrayRef<int64_t> vectorSizes;
  ::llvm::ArrayRef<int64_t> fastestVaryingPattern;
  bool vectorizeReductions = false;
};
std::unique_ptr<::mlir::Pass> createAffineVectorize();
std::unique_ptr<::mlir::Pass> createAffineVectorize(const AffineVectorizeOptions &options);
#undef GEN_PASS_DECL_AFFINEVECTORIZE
#endif // GEN_PASS_DECL_AFFINEVECTORIZE
#ifdef GEN_PASS_DEF_AFFINEVECTORIZE

namespace impl {
  std::unique_ptr<::mlir::Pass> createAffineVectorize();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createAffineVectorize(const AffineVectorizeOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class AffineVectorizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineVectorizeBase;

  AffineVectorizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineVectorizeBase(const AffineVectorizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-super-vectorize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-super-vectorize"; }

  ::llvm::StringRef getDescription() const override { return "Vectorize to a target independent n-D vector abstraction"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineVectorize");
  }
  ::llvm::StringRef getName() const override { return "AffineVectorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineVectorizeBase<DerivedT>)

  AffineVectorizeBase(const AffineVectorizeOptions &options) : AffineVectorizeBase() {
    vectorSizes = options.vectorSizes;
    fastestVaryingPattern = options.fastestVaryingPattern;
    vectorizeReductions = options.vectorizeReductions;
  }
protected:
  ::mlir::Pass::ListOption<int64_t> vectorSizes{*this, "virtual-vector-size", ::llvm::cl::desc("Specify an n-D virtual vector size for vectorization")};
  ::mlir::Pass::ListOption<int64_t> fastestVaryingPattern{*this, "test-fastest-varying", ::llvm::cl::desc("Specify a 1-D, 2-D or 3-D pattern of fastest varying memory dimensions to match. See defaultPatterns in Vectorize.cpp for a description and examples. This is used for testing purposes")};
  ::mlir::Pass::Option<bool> vectorizeReductions{*this, "vectorize-reductions", ::llvm::cl::desc("Vectorize known reductions expressed via iter_args. Switched off by default."), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createAffineVectorize() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createAffineVectorize(const AffineVectorizeOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createAffineVectorize() {
  return impl::createAffineVectorize();
}

std::unique_ptr<::mlir::Pass> createAffineVectorize(const AffineVectorizeOptions &options) {
  return impl::createAffineVectorize(options);
}
#undef GEN_PASS_DEF_AFFINEVECTORIZE
#endif // GEN_PASS_DEF_AFFINEVECTORIZE

//===----------------------------------------------------------------------===//
// LoopCoalescing
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOOPCOALESCING
#undef GEN_PASS_DECL_LOOPCOALESCING
#endif // GEN_PASS_DECL_LOOPCOALESCING
#ifdef GEN_PASS_DEF_LOOPCOALESCING
namespace impl {

template <typename DerivedT>
class LoopCoalescingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LoopCoalescingBase;

  LoopCoalescingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LoopCoalescingBase(const LoopCoalescingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-coalescing");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-coalescing"; }

  ::llvm::StringRef getDescription() const override { return "Coalesce nested loops with independent bounds into a single loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopCoalescing");
  }
  ::llvm::StringRef getName() const override { return "LoopCoalescing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopCoalescingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOOPCOALESCING
#endif // GEN_PASS_DEF_LOOPCOALESCING

//===----------------------------------------------------------------------===//
// SimplifyAffineStructures
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SIMPLIFYAFFINESTRUCTURES
#undef GEN_PASS_DECL_SIMPLIFYAFFINESTRUCTURES
#endif // GEN_PASS_DECL_SIMPLIFYAFFINESTRUCTURES
#ifdef GEN_PASS_DEF_SIMPLIFYAFFINESTRUCTURES
namespace impl {

template <typename DerivedT>
class SimplifyAffineStructuresBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SimplifyAffineStructuresBase;

  SimplifyAffineStructuresBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SimplifyAffineStructuresBase(const SimplifyAffineStructuresBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-simplify-structures");
  }
  ::llvm::StringRef getArgument() const override { return "affine-simplify-structures"; }

  ::llvm::StringRef getDescription() const override { return "Simplify affine expressions in maps/sets and normalize memrefs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SimplifyAffineStructures");
  }
  ::llvm::StringRef getName() const override { return "SimplifyAffineStructures"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SimplifyAffineStructuresBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SIMPLIFYAFFINESTRUCTURES
#endif // GEN_PASS_DEF_SIMPLIFYAFFINESTRUCTURES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AffineDataCopyGeneration Registration
//===----------------------------------------------------------------------===//

inline void registerAffineDataCopyGeneration() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineDataCopyGenerationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineDataCopyGenerationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineDataCopyGenerationPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineExpandIndexOps Registration
//===----------------------------------------------------------------------===//

inline void registerAffineExpandIndexOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineExpandIndexOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineExpandIndexOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineExpandIndexOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopFusion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopFusionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopInvariantCodeMotion Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopInvariantCodeMotion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineLoopInvariantCodeMotionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineLoopInvariantCodeMotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineLoopInvariantCodeMotionPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopNormalize Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopNormalize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineLoopNormalizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineLoopNormalizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineLoopNormalizePass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopTiling Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopTiling() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopTilingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineLoopTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopUnroll Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopUnroll() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopUnrollPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineLoopUnrollPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopUnrollPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopUnrollAndJam Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopUnrollAndJam() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopUnrollAndJamPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineLoopUnrollAndJamPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopUnrollAndJamPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineParallelize Registration
//===----------------------------------------------------------------------===//

inline void registerAffineParallelize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineParallelizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineParallelizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineParallelizePass();
  });
}

//===----------------------------------------------------------------------===//
// AffinePipelineDataTransfer Registration
//===----------------------------------------------------------------------===//

inline void registerAffinePipelineDataTransfer() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createPipelineDataTransferPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffinePipelineDataTransferPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createPipelineDataTransferPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineScalarReplacement Registration
//===----------------------------------------------------------------------===//

inline void registerAffineScalarReplacement() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineScalarReplacementPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineScalarReplacementPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createAffineScalarReplacementPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineVectorize Registration
//===----------------------------------------------------------------------===//

inline void registerAffineVectorize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createAffineVectorize();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAffineVectorizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createAffineVectorize();
  });
}

//===----------------------------------------------------------------------===//
// LoopCoalescing Registration
//===----------------------------------------------------------------------===//

inline void registerLoopCoalescing() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopCoalescingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLoopCoalescingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createLoopCoalescingPass();
  });
}

//===----------------------------------------------------------------------===//
// SimplifyAffineStructures Registration
//===----------------------------------------------------------------------===//

inline void registerSimplifyAffineStructures() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createSimplifyAffineStructuresPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSimplifyAffineStructuresPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::affine::createSimplifyAffineStructuresPass();
  });
}

//===----------------------------------------------------------------------===//
// Affine Registration
//===----------------------------------------------------------------------===//

inline void registerAffinePasses() {
  registerAffineDataCopyGeneration();
  registerAffineExpandIndexOps();
  registerAffineLoopFusion();
  registerAffineLoopInvariantCodeMotion();
  registerAffineLoopNormalize();
  registerAffineLoopTiling();
  registerAffineLoopUnroll();
  registerAffineLoopUnrollAndJam();
  registerAffineParallelize();
  registerAffinePipelineDataTransfer();
  registerAffineScalarReplacement();
  registerAffineVectorize();
  registerLoopCoalescing();
  registerSimplifyAffineStructures();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AffineDataCopyGenerationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineDataCopyGenerationBase;

  AffineDataCopyGenerationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineDataCopyGenerationBase(const AffineDataCopyGenerationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-data-copy-generate");
  }
  ::llvm::StringRef getArgument() const override { return "affine-data-copy-generate"; }

  ::llvm::StringRef getDescription() const override { return "Generate explicit copying for affine memory operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineDataCopyGeneration");
  }
  ::llvm::StringRef getName() const override { return "AffineDataCopyGeneration"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineDataCopyGenerationBase<DerivedT>)

protected:
  ::mlir::Pass::Option<uint64_t> fastMemoryCapacity{*this, "fast-mem-capacity", ::llvm::cl::desc("Set fast memory space capacity in KiB (default: unlimited)"), ::llvm::cl::init(std::numeric_limits<uint64_t>::max())};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fast-mem-space", ::llvm::cl::desc("Fast memory space identifier for copy generation (default: 1)"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> generateDma{*this, "generate-dma", ::llvm::cl::desc("Generate DMA instead of point-wise copy"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int> minDmaTransferSize{*this, "min-dma-transfer", ::llvm::cl::desc("Minimum DMA transfer size supported by the target in bytes"), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> slowMemorySpace{*this, "slow-mem-space", ::llvm::cl::desc("Slow memory space identifier for copy generation (default: 0)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> skipNonUnitStrideLoops{*this, "skip-non-unit-stride-loops", ::llvm::cl::desc("Testing purposes: avoid non-unit stride loop choice depths for copy placement"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> tagMemorySpace{*this, "tag-mem-space", ::llvm::cl::desc("Tag memory space identifier for copy generation (default: 0)"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class AffineExpandIndexOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = AffineExpandIndexOpsBase;

  AffineExpandIndexOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AffineExpandIndexOpsBase(const AffineExpandIndexOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-expand-index-ops");
  }
  ::llvm::StringRef getArgument() const override { return "affine-expand-index-ops"; }

  ::llvm::StringRef getDescription() const override { return "Lower affine operations operating on indices into more fundamental operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineExpandIndexOps");
  }
  ::llvm::StringRef getName() const override { return "AffineExpandIndexOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineExpandIndexOpsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AffineLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = AffineLoopFusionBase;

  AffineLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopFusionBase(const AffineLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopFusionBase<DerivedT>)

protected:
  ::mlir::Pass::Option<double> computeToleranceThreshold{*this, "fusion-compute-tolerance", ::llvm::cl::desc("Fractional increase in additional computation tolerated while fusing"), ::llvm::cl::init(0.30f)};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fusion-fast-mem-space", ::llvm::cl::desc("Faster memory space number to promote fusion buffers to"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<uint64_t> localBufSizeThreshold{*this, "fusion-local-buf-threshold", ::llvm::cl::desc("Threshold size (KiB) for promoting local buffers to fast memory space"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> maximalFusion{*this, "fusion-maximal", ::llvm::cl::desc("Enables maximal loop fusion"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<enum FusionMode> affineFusionMode{*this, "mode", ::llvm::cl::desc("fusion mode to attempt"), ::llvm::cl::init(mlir::affine::FusionMode::Greedy), llvm::cl::values(clEnumValN(mlir::affine::FusionMode::Greedy, "greedy", "Perform greedy (both producer-consumer and sibling)  fusion"), clEnumValN( mlir::affine::FusionMode::ProducerConsumer, "producer", "Perform only producer-consumer fusion"), clEnumValN( mlir::affine::FusionMode::Sibling, "sibling", "Perform only sibling fusion"))};
};

template <typename DerivedT>
class AffineLoopInvariantCodeMotionBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopInvariantCodeMotionBase;

  AffineLoopInvariantCodeMotionBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopInvariantCodeMotionBase(const AffineLoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopInvariantCodeMotionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AffineLoopNormalizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopNormalizeBase;

  AffineLoopNormalizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopNormalizeBase(const AffineLoopNormalizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-normalize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-normalize"; }

  ::llvm::StringRef getDescription() const override { return "Apply normalization transformations to affine loop-like ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopNormalize");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopNormalize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopNormalizeBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> promoteSingleIter{*this, "promote-single-iter", ::llvm::cl::desc("Promote single iteration loops"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class AffineLoopTilingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopTilingBase;

  AffineLoopTilingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopTilingBase(const AffineLoopTilingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-tile");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-tile"; }

  ::llvm::StringRef getDescription() const override { return "Tile affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopTilingBase<DerivedT>)

protected:
  ::mlir::Pass::Option<uint64_t> cacheSizeInKiB{*this, "cache-size", ::llvm::cl::desc("Set size of cache to tile for in KiB (default: 512)"), ::llvm::cl::init(512)};
  ::mlir::Pass::Option<bool> separate{*this, "separate", ::llvm::cl::desc("Separate full and partial tiles (default: false)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> tileSize{*this, "tile-size", ::llvm::cl::desc("Use this tile size for all loops")};
  ::mlir::Pass::ListOption<unsigned> tileSizes{*this, "tile-sizes", ::llvm::cl::desc("List of tile sizes for each perfect nest (overridden by -tile-size)")};
};

template <typename DerivedT>
class AffineLoopUnrollBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopUnrollBase;

  AffineLoopUnrollBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopUnrollBase(const AffineLoopUnrollBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-unroll");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-unroll"; }

  ::llvm::StringRef getDescription() const override { return "Unroll affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopUnroll");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopUnroll"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopUnrollBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> unrollFactor{*this, "unroll-factor", ::llvm::cl::desc("Use this unroll factor for all loops being unrolled"), ::llvm::cl::init(4)};
  ::mlir::Pass::Option<bool> unrollUpToFactor{*this, "unroll-up-to-factor", ::llvm::cl::desc("Allow unrolling up to the factor specified"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> unrollFull{*this, "unroll-full", ::llvm::cl::desc("Fully unroll loops"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> numRepetitions{*this, "unroll-num-reps", ::llvm::cl::desc("Unroll innermost loops repeatedly this many times"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<unsigned> unrollFullThreshold{*this, "unroll-full-threshold", ::llvm::cl::desc("Unroll all loops with trip count less than or equal to this"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> cleanUpUnroll{*this, "cleanup-unroll", ::llvm::cl::desc("Fully unroll the cleanup loop when possible."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class AffineLoopUnrollAndJamBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineLoopUnrollAndJamBase;

  AffineLoopUnrollAndJamBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopUnrollAndJamBase(const AffineLoopUnrollAndJamBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-unroll-jam");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-unroll-jam"; }

  ::llvm::StringRef getDescription() const override { return "Unroll and jam affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopUnrollAndJam");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopUnrollAndJam"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineLoopUnrollAndJamBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> unrollJamFactor{*this, "unroll-jam-factor", ::llvm::cl::desc("Use this unroll jam factor for all loops (default 4)"), ::llvm::cl::init(4)};
};

template <typename DerivedT>
class AffineParallelizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineParallelizeBase;

  AffineParallelizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineParallelizeBase(const AffineParallelizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-parallelize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-parallelize"; }

  ::llvm::StringRef getDescription() const override { return "Convert affine.for ops into 1-D affine.parallel"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineParallelize");
  }
  ::llvm::StringRef getName() const override { return "AffineParallelize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineParallelizeBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> maxNested{*this, "max-nested", ::llvm::cl::desc("Maximum number of nested parallel loops to produce. Defaults to unlimited (UINT_MAX)."), ::llvm::cl::init(-1u)};
  ::mlir::Pass::Option<bool> parallelReductions{*this, "parallel-reductions", ::llvm::cl::desc("Whether to parallelize reduction loops. Defaults to false."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class AffinePipelineDataTransferBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffinePipelineDataTransferBase;

  AffinePipelineDataTransferBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffinePipelineDataTransferBase(const AffinePipelineDataTransferBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-pipeline-data-transfer");
  }
  ::llvm::StringRef getArgument() const override { return "affine-pipeline-data-transfer"; }

  ::llvm::StringRef getDescription() const override { return "Pipeline non-blocking data transfers between explicitly managed levels of the memory hierarchy"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffinePipelineDataTransfer");
  }
  ::llvm::StringRef getName() const override { return "AffinePipelineDataTransfer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffinePipelineDataTransferBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AffineScalarReplacementBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineScalarReplacementBase;

  AffineScalarReplacementBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineScalarReplacementBase(const AffineScalarReplacementBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-scalrep");
  }
  ::llvm::StringRef getArgument() const override { return "affine-scalrep"; }

  ::llvm::StringRef getDescription() const override { return "Replace affine memref accesses by scalars by forwarding stores to loads and eliminating redundant loads"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineScalarReplacement");
  }
  ::llvm::StringRef getName() const override { return "AffineScalarReplacement"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineScalarReplacementBase<DerivedT>)

protected:
};

template <typename DerivedT>
class AffineVectorizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AffineVectorizeBase;

  AffineVectorizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineVectorizeBase(const AffineVectorizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-super-vectorize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-super-vectorize"; }

  ::llvm::StringRef getDescription() const override { return "Vectorize to a target independent n-D vector abstraction"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineVectorize");
  }
  ::llvm::StringRef getName() const override { return "AffineVectorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AffineVectorizeBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<int64_t> vectorSizes{*this, "virtual-vector-size", ::llvm::cl::desc("Specify an n-D virtual vector size for vectorization")};
  ::mlir::Pass::ListOption<int64_t> fastestVaryingPattern{*this, "test-fastest-varying", ::llvm::cl::desc("Specify a 1-D, 2-D or 3-D pattern of fastest varying memory dimensions to match. See defaultPatterns in Vectorize.cpp for a description and examples. This is used for testing purposes")};
  ::mlir::Pass::Option<bool> vectorizeReductions{*this, "vectorize-reductions", ::llvm::cl::desc("Vectorize known reductions expressed via iter_args. Switched off by default."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LoopCoalescingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LoopCoalescingBase;

  LoopCoalescingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LoopCoalescingBase(const LoopCoalescingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-coalescing");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-coalescing"; }

  ::llvm::StringRef getDescription() const override { return "Coalesce nested loops with independent bounds into a single loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopCoalescing");
  }
  ::llvm::StringRef getName() const override { return "LoopCoalescing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopCoalescingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SimplifyAffineStructuresBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SimplifyAffineStructuresBase;

  SimplifyAffineStructuresBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SimplifyAffineStructuresBase(const SimplifyAffineStructuresBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-simplify-structures");
  }
  ::llvm::StringRef getArgument() const override { return "affine-simplify-structures"; }

  ::llvm::StringRef getDescription() const override { return "Simplify affine expressions in maps/sets and normalize memrefs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SimplifyAffineStructures");
  }
  ::llvm::StringRef getName() const override { return "SimplifyAffineStructures"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SimplifyAffineStructuresBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
