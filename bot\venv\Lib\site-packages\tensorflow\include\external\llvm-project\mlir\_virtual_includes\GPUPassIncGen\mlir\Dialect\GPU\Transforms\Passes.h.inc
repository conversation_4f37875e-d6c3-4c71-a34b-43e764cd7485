/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_GPUASYNCREGIONPASS
#define GEN_PASS_DECL_GPUKERNELOUTLINING
#define GEN_PASS_DECL_GPULAUNCHSINKINDEXCOMPUTATIONS
#define GEN_PASS_DECL_GPUMAPPARALLELLOOPSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// GpuAsyncRegionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GPUASYNCREGIONPASS
#undef GEN_PASS_DECL_GPUASYNCREGIONPASS
#endif // GEN_PASS_DECL_GPUASYNCREGIONPASS
#ifdef GEN_PASS_DEF_GPUASYNCREGIONPASS
namespace impl {

template <typename DerivedT>
class GpuAsyncRegionPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = GpuAsyncRegionPassBase;

  GpuAsyncRegionPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuAsyncRegionPassBase(const GpuAsyncRegionPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-async-region");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-async-region"; }

  ::llvm::StringRef getDescription() const override { return "Make GPU ops async"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuAsyncRegionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuAsyncRegionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuAsyncRegionPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GPUASYNCREGIONPASS
#endif // GEN_PASS_DEF_GPUASYNCREGIONPASS

//===----------------------------------------------------------------------===//
// GpuKernelOutlining
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GPUKERNELOUTLINING
#undef GEN_PASS_DECL_GPUKERNELOUTLINING
#endif // GEN_PASS_DECL_GPUKERNELOUTLINING
#ifdef GEN_PASS_DEF_GPUKERNELOUTLINING
namespace impl {

template <typename DerivedT>
class GpuKernelOutliningBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuKernelOutliningBase;

  GpuKernelOutliningBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuKernelOutliningBase(const GpuKernelOutliningBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-kernel-outlining");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-kernel-outlining"; }

  ::llvm::StringRef getDescription() const override { return "Outline gpu.launch bodies to kernel functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuKernelOutlining");
  }
  ::llvm::StringRef getName() const override { return "GpuKernelOutlining"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::DLTIDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuKernelOutliningBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GPUKERNELOUTLINING
#endif // GEN_PASS_DEF_GPUKERNELOUTLINING

//===----------------------------------------------------------------------===//
// GpuLaunchSinkIndexComputations
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GPULAUNCHSINKINDEXCOMPUTATIONS
#undef GEN_PASS_DECL_GPULAUNCHSINKINDEXCOMPUTATIONS
#endif // GEN_PASS_DECL_GPULAUNCHSINKINDEXCOMPUTATIONS
#ifdef GEN_PASS_DEF_GPULAUNCHSINKINDEXCOMPUTATIONS
namespace impl {

template <typename DerivedT>
class GpuLaunchSinkIndexComputationsBase : public ::mlir::OperationPass<> {
public:
  using Base = GpuLaunchSinkIndexComputationsBase;

  GpuLaunchSinkIndexComputationsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  GpuLaunchSinkIndexComputationsBase(const GpuLaunchSinkIndexComputationsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-launch-sink-index-computations");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-launch-sink-index-computations"; }

  ::llvm::StringRef getDescription() const override { return "Sink index computations into gpu.launch body"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuLaunchSinkIndexComputations");
  }
  ::llvm::StringRef getName() const override { return "GpuLaunchSinkIndexComputations"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuLaunchSinkIndexComputationsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GPULAUNCHSINKINDEXCOMPUTATIONS
#endif // GEN_PASS_DEF_GPULAUNCHSINKINDEXCOMPUTATIONS

//===----------------------------------------------------------------------===//
// GpuMapParallelLoopsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GPUMAPPARALLELLOOPSPASS
#undef GEN_PASS_DECL_GPUMAPPARALLELLOOPSPASS
#endif // GEN_PASS_DECL_GPUMAPPARALLELLOOPSPASS
#ifdef GEN_PASS_DEF_GPUMAPPARALLELLOOPSPASS
namespace impl {

template <typename DerivedT>
class GpuMapParallelLoopsPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = GpuMapParallelLoopsPassBase;

  GpuMapParallelLoopsPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuMapParallelLoopsPassBase(const GpuMapParallelLoopsPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-map-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-map-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Greedily maps loops to GPU hardware dimensions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuMapParallelLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "GpuMapParallelLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuMapParallelLoopsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GPUMAPPARALLELLOOPSPASS
#endif // GEN_PASS_DEF_GPUMAPPARALLELLOOPSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// GpuAsyncRegionPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuAsyncRegionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuAsyncRegionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGpuAsyncRegionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuAsyncRegionPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuKernelOutlining Registration
//===----------------------------------------------------------------------===//

inline void registerGpuKernelOutlining() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuKernelOutliningPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGpuKernelOutliningPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuKernelOutliningPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuLaunchSinkIndexComputations Registration
//===----------------------------------------------------------------------===//

inline void registerGpuLaunchSinkIndexComputations() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuLauchSinkIndexComputationsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGpuLaunchSinkIndexComputationsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuLauchSinkIndexComputationsPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuMapParallelLoopsPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuMapParallelLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuMapParallelLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGpuMapParallelLoopsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuMapParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// GPU Registration
//===----------------------------------------------------------------------===//

inline void registerGPUPasses() {
  registerGpuAsyncRegionPass();
  registerGpuKernelOutlining();
  registerGpuLaunchSinkIndexComputations();
  registerGpuMapParallelLoopsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class GpuAsyncRegionPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = GpuAsyncRegionPassBase;

  GpuAsyncRegionPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuAsyncRegionPassBase(const GpuAsyncRegionPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-async-region");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-async-region"; }

  ::llvm::StringRef getDescription() const override { return "Make GPU ops async"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuAsyncRegionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuAsyncRegionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuAsyncRegionPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class GpuKernelOutliningBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuKernelOutliningBase;

  GpuKernelOutliningBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuKernelOutliningBase(const GpuKernelOutliningBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-kernel-outlining");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-kernel-outlining"; }

  ::llvm::StringRef getDescription() const override { return "Outline gpu.launch bodies to kernel functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuKernelOutlining");
  }
  ::llvm::StringRef getName() const override { return "GpuKernelOutlining"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::DLTIDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuKernelOutliningBase<DerivedT>)

protected:
};

template <typename DerivedT>
class GpuLaunchSinkIndexComputationsBase : public ::mlir::OperationPass<> {
public:
  using Base = GpuLaunchSinkIndexComputationsBase;

  GpuLaunchSinkIndexComputationsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  GpuLaunchSinkIndexComputationsBase(const GpuLaunchSinkIndexComputationsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-launch-sink-index-computations");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-launch-sink-index-computations"; }

  ::llvm::StringRef getDescription() const override { return "Sink index computations into gpu.launch body"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuLaunchSinkIndexComputations");
  }
  ::llvm::StringRef getName() const override { return "GpuLaunchSinkIndexComputations"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuLaunchSinkIndexComputationsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class GpuMapParallelLoopsPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = GpuMapParallelLoopsPassBase;

  GpuMapParallelLoopsPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuMapParallelLoopsPassBase(const GpuMapParallelLoopsPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-map-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-map-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Greedily maps loops to GPU hardware dimensions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuMapParallelLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "GpuMapParallelLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuMapParallelLoopsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
