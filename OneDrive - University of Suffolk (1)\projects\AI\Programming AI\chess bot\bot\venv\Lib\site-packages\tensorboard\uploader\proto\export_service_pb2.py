# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/uploader/proto/export_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from tensorboard.uploader.proto import blob_pb2 as tensorboard_dot_uploader_dot_proto_dot_blob__pb2
from tensorboard.uploader.proto import experiment_pb2 as tensorboard_dot_uploader_dot_proto_dot_experiment__pb2
from tensorboard.compat.proto import summary_pb2 as tensorboard_dot_compat_dot_proto_dot_summary__pb2
try:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard_dot_compat_dot_proto_dot_histogram__pb2
except AttributeError:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard.compat.proto.histogram_pb2
from tensorboard.compat.proto import tensor_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/tensorboard/uploader/proto/export_service.proto\x12\x13tensorboard.service\x1a\x1fgoogle/protobuf/timestamp.proto\x1a%tensorboard/uploader/proto/blob.proto\x1a+tensorboard/uploader/proto/experiment.proto\x1a&tensorboard/compat/proto/summary.proto\x1a%tensorboard/compat/proto/tensor.proto\"\xad\x01\n\x18StreamExperimentsRequest\x12\x32\n\x0eread_timestamp\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x03\x12=\n\x10\x65xperiments_mask\x18\x04 \x01(\x0b\x32#.tensorboard.service.ExperimentMask\"i\n\x19StreamExperimentsResponse\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x12\x34\n\x0b\x65xperiments\x18\x02 \x03(\x0b\x32\x1f.tensorboard.service.Experiment\"h\n\x1bStreamExperimentDataRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x32\n\x0eread_timestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xe4\x05\n\x1cStreamExperimentDataResponse\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x10\n\x08run_name\x18\x02 \x01(\t\x12\x32\n\x0ctag_metadata\x18\x03 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\x12P\n\x06points\x18\x04 \x01(\x0b\x32>.tensorboard.service.StreamExperimentDataResponse.ScalarPointsH\x00\x12Q\n\x07tensors\x18\x05 \x01(\x0b\x32>.tensorboard.service.StreamExperimentDataResponse.TensorPointsH\x00\x12^\n\x0e\x62lob_sequences\x18\x06 \x01(\x0b\x32\x44.tensorboard.service.StreamExperimentDataResponse.BlobSequencePointsH\x00\x1a]\n\x0cScalarPoints\x12\r\n\x05steps\x18\x01 \x03(\x03\x12.\n\nwall_times\x18\x02 \x03(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06values\x18\x03 \x03(\x01\x1aw\n\x0cTensorPoints\x12\r\n\x05steps\x18\x01 \x03(\x03\x12.\n\nwall_times\x18\x02 \x03(\x0b\x32\x1a.google.protobuf.Timestamp\x12(\n\x06values\x18\x03 \x03(\x0b\x32\x18.tensorboard.TensorProto\x1a\x86\x01\n\x12\x42lobSequencePoints\x12\r\n\x05steps\x18\x01 \x03(\x03\x12.\n\nwall_times\x18\x02 \x03(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\x06values\x18\x03 \x03(\x0b\x32!.tensorboard.service.BlobSequenceB\x06\n\x04\x64\x61ta\"(\n\x15StreamBlobDataRequest\x12\x0f\n\x07\x62lob_id\x18\x01 \x01(\t\"g\n\x16StreamBlobDataResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x0e\n\x06offset\x18\x02 \x01(\x03\x12\x13\n\x0b\x66inal_chunk\x18\x04 \x01(\x08\x12\x14\n\x0c\x66inal_crc32c\x18\x05 \x01(\x07J\x04\x08\x03\x10\x04\x32\x84\x03\n\x1aTensorBoardExporterService\x12v\n\x11StreamExperiments\x12-.tensorboard.service.StreamExperimentsRequest\x1a..tensorboard.service.StreamExperimentsResponse\"\x00\x30\x01\x12\x7f\n\x14StreamExperimentData\x12\x30.tensorboard.service.StreamExperimentDataRequest\x1a\x31.tensorboard.service.StreamExperimentDataResponse\"\x00\x30\x01\x12m\n\x0eStreamBlobData\x12*.tensorboard.service.StreamBlobDataRequest\x1a+.tensorboard.service.StreamBlobDataResponse\"\x00\x30\x01\x62\x06proto3')



_STREAMEXPERIMENTSREQUEST = DESCRIPTOR.message_types_by_name['StreamExperimentsRequest']
_STREAMEXPERIMENTSRESPONSE = DESCRIPTOR.message_types_by_name['StreamExperimentsResponse']
_STREAMEXPERIMENTDATAREQUEST = DESCRIPTOR.message_types_by_name['StreamExperimentDataRequest']
_STREAMEXPERIMENTDATARESPONSE = DESCRIPTOR.message_types_by_name['StreamExperimentDataResponse']
_STREAMEXPERIMENTDATARESPONSE_SCALARPOINTS = _STREAMEXPERIMENTDATARESPONSE.nested_types_by_name['ScalarPoints']
_STREAMEXPERIMENTDATARESPONSE_TENSORPOINTS = _STREAMEXPERIMENTDATARESPONSE.nested_types_by_name['TensorPoints']
_STREAMEXPERIMENTDATARESPONSE_BLOBSEQUENCEPOINTS = _STREAMEXPERIMENTDATARESPONSE.nested_types_by_name['BlobSequencePoints']
_STREAMBLOBDATAREQUEST = DESCRIPTOR.message_types_by_name['StreamBlobDataRequest']
_STREAMBLOBDATARESPONSE = DESCRIPTOR.message_types_by_name['StreamBlobDataResponse']
StreamExperimentsRequest = _reflection.GeneratedProtocolMessageType('StreamExperimentsRequest', (_message.Message,), {
  'DESCRIPTOR' : _STREAMEXPERIMENTSREQUEST,
  '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentsRequest)
  })
_sym_db.RegisterMessage(StreamExperimentsRequest)

StreamExperimentsResponse = _reflection.GeneratedProtocolMessageType('StreamExperimentsResponse', (_message.Message,), {
  'DESCRIPTOR' : _STREAMEXPERIMENTSRESPONSE,
  '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentsResponse)
  })
_sym_db.RegisterMessage(StreamExperimentsResponse)

StreamExperimentDataRequest = _reflection.GeneratedProtocolMessageType('StreamExperimentDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _STREAMEXPERIMENTDATAREQUEST,
  '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentDataRequest)
  })
_sym_db.RegisterMessage(StreamExperimentDataRequest)

StreamExperimentDataResponse = _reflection.GeneratedProtocolMessageType('StreamExperimentDataResponse', (_message.Message,), {

  'ScalarPoints' : _reflection.GeneratedProtocolMessageType('ScalarPoints', (_message.Message,), {
    'DESCRIPTOR' : _STREAMEXPERIMENTDATARESPONSE_SCALARPOINTS,
    '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentDataResponse.ScalarPoints)
    })
  ,

  'TensorPoints' : _reflection.GeneratedProtocolMessageType('TensorPoints', (_message.Message,), {
    'DESCRIPTOR' : _STREAMEXPERIMENTDATARESPONSE_TENSORPOINTS,
    '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentDataResponse.TensorPoints)
    })
  ,

  'BlobSequencePoints' : _reflection.GeneratedProtocolMessageType('BlobSequencePoints', (_message.Message,), {
    'DESCRIPTOR' : _STREAMEXPERIMENTDATARESPONSE_BLOBSEQUENCEPOINTS,
    '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentDataResponse.BlobSequencePoints)
    })
  ,
  'DESCRIPTOR' : _STREAMEXPERIMENTDATARESPONSE,
  '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.StreamExperimentDataResponse)
  })
_sym_db.RegisterMessage(StreamExperimentDataResponse)
_sym_db.RegisterMessage(StreamExperimentDataResponse.ScalarPoints)
_sym_db.RegisterMessage(StreamExperimentDataResponse.TensorPoints)
_sym_db.RegisterMessage(StreamExperimentDataResponse.BlobSequencePoints)

StreamBlobDataRequest = _reflection.GeneratedProtocolMessageType('StreamBlobDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _STREAMBLOBDATAREQUEST,
  '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.StreamBlobDataRequest)
  })
_sym_db.RegisterMessage(StreamBlobDataRequest)

StreamBlobDataResponse = _reflection.GeneratedProtocolMessageType('StreamBlobDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _STREAMBLOBDATARESPONSE,
  '__module__' : 'tensorboard.uploader.proto.export_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.service.StreamBlobDataResponse)
  })
_sym_db.RegisterMessage(StreamBlobDataResponse)

_TENSORBOARDEXPORTERSERVICE = DESCRIPTOR.services_by_name['TensorBoardExporterService']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _STREAMEXPERIMENTSREQUEST._serialized_start=269
  _STREAMEXPERIMENTSREQUEST._serialized_end=442
  _STREAMEXPERIMENTSRESPONSE._serialized_start=444
  _STREAMEXPERIMENTSRESPONSE._serialized_end=549
  _STREAMEXPERIMENTDATAREQUEST._serialized_start=551
  _STREAMEXPERIMENTDATAREQUEST._serialized_end=655
  _STREAMEXPERIMENTDATARESPONSE._serialized_start=658
  _STREAMEXPERIMENTDATARESPONSE._serialized_end=1398
  _STREAMEXPERIMENTDATARESPONSE_SCALARPOINTS._serialized_start=1039
  _STREAMEXPERIMENTDATARESPONSE_SCALARPOINTS._serialized_end=1132
  _STREAMEXPERIMENTDATARESPONSE_TENSORPOINTS._serialized_start=1134
  _STREAMEXPERIMENTDATARESPONSE_TENSORPOINTS._serialized_end=1253
  _STREAMEXPERIMENTDATARESPONSE_BLOBSEQUENCEPOINTS._serialized_start=1256
  _STREAMEXPERIMENTDATARESPONSE_BLOBSEQUENCEPOINTS._serialized_end=1390
  _STREAMBLOBDATAREQUEST._serialized_start=1400
  _STREAMBLOBDATAREQUEST._serialized_end=1440
  _STREAMBLOBDATARESPONSE._serialized_start=1442
  _STREAMBLOBDATARESPONSE._serialized_end=1545
  _TENSORBOARDEXPORTERSERVICE._serialized_start=1548
  _TENSORBOARDEXPORTERSERVICE._serialized_end=1936
# @@protoc_insertion_point(module_scope)
