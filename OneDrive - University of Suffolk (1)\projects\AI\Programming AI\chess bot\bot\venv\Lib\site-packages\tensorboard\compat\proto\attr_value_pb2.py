# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/attr_value.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import tensor_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__pb2
from tensorboard.compat.proto import tensor_shape_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__shape__pb2
from tensorboard.compat.proto import types_pb2 as tensorboard_dot_compat_dot_proto_dot_types__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)tensorboard/compat/proto/attr_value.proto\x12\x0btensorboard\x1a%tensorboard/compat/proto/tensor.proto\x1a+tensorboard/compat/proto/tensor_shape.proto\x1a$tensorboard/compat/proto/types.proto\"\xaf\x04\n\tAttrValue\x12\x0b\n\x01s\x18\x02 \x01(\x0cH\x00\x12\x0b\n\x01i\x18\x03 \x01(\x03H\x00\x12\x0b\n\x01\x66\x18\x04 \x01(\x02H\x00\x12\x0b\n\x01\x62\x18\x05 \x01(\x08H\x00\x12%\n\x04type\x18\x06 \x01(\x0e\x32\x15.tensorboard.DataTypeH\x00\x12.\n\x05shape\x18\x07 \x01(\x0b\x32\x1d.tensorboard.TensorShapeProtoH\x00\x12*\n\x06tensor\x18\x08 \x01(\x0b\x32\x18.tensorboard.TensorProtoH\x00\x12\x30\n\x04list\x18\x01 \x01(\x0b\x32 .tensorboard.AttrValue.ListValueH\x00\x12)\n\x04\x66unc\x18\n \x01(\x0b\x32\x19.tensorboard.NameAttrListH\x00\x12\x15\n\x0bplaceholder\x18\t \x01(\tH\x00\x1a\xed\x01\n\tListValue\x12\t\n\x01s\x18\x02 \x03(\x0c\x12\r\n\x01i\x18\x03 \x03(\x03\x42\x02\x10\x01\x12\r\n\x01\x66\x18\x04 \x03(\x02\x42\x02\x10\x01\x12\r\n\x01\x62\x18\x05 \x03(\x08\x42\x02\x10\x01\x12\'\n\x04type\x18\x06 \x03(\x0e\x32\x15.tensorboard.DataTypeB\x02\x10\x01\x12,\n\x05shape\x18\x07 \x03(\x0b\x32\x1d.tensorboard.TensorShapeProto\x12(\n\x06tensor\x18\x08 \x03(\x0b\x32\x18.tensorboard.TensorProto\x12\'\n\x04\x66unc\x18\t \x03(\x0b\x32\x19.tensorboard.NameAttrListB\x07\n\x05value\"\x94\x01\n\x0cNameAttrList\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x31\n\x04\x61ttr\x18\x02 \x03(\x0b\x32#.tensorboard.NameAttrList.AttrEntry\x1a\x43\n\tAttrEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.tensorboard.AttrValue:\x02\x38\x01\x42\x83\x01\n\x18org.tensorflow.frameworkB\x0f\x41ttrValueProtosP\x01ZQgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto\xf8\x01\x01\x62\x06proto3')



_ATTRVALUE = DESCRIPTOR.message_types_by_name['AttrValue']
_ATTRVALUE_LISTVALUE = _ATTRVALUE.nested_types_by_name['ListValue']
_NAMEATTRLIST = DESCRIPTOR.message_types_by_name['NameAttrList']
_NAMEATTRLIST_ATTRENTRY = _NAMEATTRLIST.nested_types_by_name['AttrEntry']
AttrValue = _reflection.GeneratedProtocolMessageType('AttrValue', (_message.Message,), {

  'ListValue' : _reflection.GeneratedProtocolMessageType('ListValue', (_message.Message,), {
    'DESCRIPTOR' : _ATTRVALUE_LISTVALUE,
    '__module__' : 'tensorboard.compat.proto.attr_value_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.AttrValue.ListValue)
    })
  ,
  'DESCRIPTOR' : _ATTRVALUE,
  '__module__' : 'tensorboard.compat.proto.attr_value_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.AttrValue)
  })
_sym_db.RegisterMessage(AttrValue)
_sym_db.RegisterMessage(AttrValue.ListValue)

NameAttrList = _reflection.GeneratedProtocolMessageType('NameAttrList', (_message.Message,), {

  'AttrEntry' : _reflection.GeneratedProtocolMessageType('AttrEntry', (_message.Message,), {
    'DESCRIPTOR' : _NAMEATTRLIST_ATTRENTRY,
    '__module__' : 'tensorboard.compat.proto.attr_value_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.NameAttrList.AttrEntry)
    })
  ,
  'DESCRIPTOR' : _NAMEATTRLIST,
  '__module__' : 'tensorboard.compat.proto.attr_value_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.NameAttrList)
  })
_sym_db.RegisterMessage(NameAttrList)
_sym_db.RegisterMessage(NameAttrList.AttrEntry)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\017AttrValueProtosP\001ZQgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto\370\001\001'
  _ATTRVALUE_LISTVALUE.fields_by_name['i']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['i']._serialized_options = b'\020\001'
  _ATTRVALUE_LISTVALUE.fields_by_name['f']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['f']._serialized_options = b'\020\001'
  _ATTRVALUE_LISTVALUE.fields_by_name['b']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['b']._serialized_options = b'\020\001'
  _ATTRVALUE_LISTVALUE.fields_by_name['type']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['type']._serialized_options = b'\020\001'
  _NAMEATTRLIST_ATTRENTRY._options = None
  _NAMEATTRLIST_ATTRENTRY._serialized_options = b'8\001'
  _ATTRVALUE._serialized_start=181
  _ATTRVALUE._serialized_end=740
  _ATTRVALUE_LISTVALUE._serialized_start=494
  _ATTRVALUE_LISTVALUE._serialized_end=731
  _NAMEATTRLIST._serialized_start=743
  _NAMEATTRLIST._serialized_end=891
  _NAMEATTRLIST_ATTRENTRY._serialized_start=824
  _NAMEATTRLIST_ATTRENTRY._serialized_end=891
# @@protoc_insertion_point(module_scope)
