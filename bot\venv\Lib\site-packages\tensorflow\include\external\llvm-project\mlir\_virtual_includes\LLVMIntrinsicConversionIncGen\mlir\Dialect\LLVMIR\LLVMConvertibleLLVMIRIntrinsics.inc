llvm::Intrinsic::abs,
llvm::Intrinsic::assume,
llvm::Intrinsic::bitreverse,
llvm::Intrinsic::bswap,
llvm::Intrinsic::copysign,
llvm::Intrinsic::coro_align,
llvm::Intrinsic::coro_begin,
llvm::Intrinsic::coro_end,
llvm::Intrinsic::coro_free,
llvm::Intrinsic::coro_id,
llvm::Intrinsic::coro_resume,
llvm::Intrinsic::coro_save,
llvm::Intrinsic::coro_size,
llvm::Intrinsic::coro_suspend,
llvm::Intrinsic::cos,
llvm::Intrinsic::ctlz,
llvm::Intrinsic::cttz,
llvm::Intrinsic::ctpop,
llvm::Intrinsic::dbg_declare,
llvm::Intrinsic::dbg_value,
llvm::Intrinsic::eh_typeid_for,
llvm::Intrinsic::exp2,
llvm::Intrinsic::exp,
llvm::Intrinsic::fabs,
llvm::Intrinsic::ceil,
llvm::Intrinsic::floor,
llvm::Intrinsic::fma,
llvm::Intrinsic::fmuladd,
llvm::Intrinsic::trunc,
llvm::Intrinsic::fshl,
llvm::Intrinsic::fshr,
llvm::Intrinsic::get_active_lane_mask,
llvm::Intrinsic::is_fpclass,
llvm::Intrinsic::lifetime_end,
llvm::Intrinsic::lifetime_start,
llvm::Intrinsic::log2,
llvm::Intrinsic::log10,
llvm::Intrinsic::log,
llvm::Intrinsic::masked_load,
llvm::Intrinsic::masked_store,
llvm::Intrinsic::matrix_column_major_load,
llvm::Intrinsic::matrix_column_major_store,
llvm::Intrinsic::matrix_multiply,
llvm::Intrinsic::matrix_transpose,
llvm::Intrinsic::maxnum,
llvm::Intrinsic::maximum,
llvm::Intrinsic::memcpy_inline,
llvm::Intrinsic::memcpy,
llvm::Intrinsic::memmove,
llvm::Intrinsic::memset,
llvm::Intrinsic::minnum,
llvm::Intrinsic::minimum,
llvm::Intrinsic::experimental_noalias_scope_decl,
llvm::Intrinsic::powi,
llvm::Intrinsic::pow,
llvm::Intrinsic::prefetch,
llvm::Intrinsic::roundeven,
llvm::Intrinsic::round,
llvm::Intrinsic::sadd_with_overflow,
llvm::Intrinsic::smax,
llvm::Intrinsic::smin,
llvm::Intrinsic::smul_with_overflow,
llvm::Intrinsic::ssub_with_overflow,
llvm::Intrinsic::sin,
llvm::Intrinsic::sqrt,
llvm::Intrinsic::stackrestore,
llvm::Intrinsic::stacksave,
llvm::Intrinsic::experimental_stepvector,
llvm::Intrinsic::uadd_with_overflow,
llvm::Intrinsic::umax,
llvm::Intrinsic::umin,
llvm::Intrinsic::umul_with_overflow,
llvm::Intrinsic::usub_with_overflow,
llvm::Intrinsic::vp_ashr,
llvm::Intrinsic::vp_add,
llvm::Intrinsic::vp_and,
llvm::Intrinsic::vp_fadd,
llvm::Intrinsic::vp_fdiv,
llvm::Intrinsic::vp_fmuladd,
llvm::Intrinsic::vp_fmul,
llvm::Intrinsic::vp_fneg,
llvm::Intrinsic::vp_fpext,
llvm::Intrinsic::vp_fptosi,
llvm::Intrinsic::vp_fptoui,
llvm::Intrinsic::vp_fptrunc,
llvm::Intrinsic::vp_frem,
llvm::Intrinsic::vp_fsub,
llvm::Intrinsic::vp_fma,
llvm::Intrinsic::vp_inttoptr,
llvm::Intrinsic::vp_lshr,
llvm::Intrinsic::vp_load,
llvm::Intrinsic::vp_merge,
llvm::Intrinsic::vp_mul,
llvm::Intrinsic::vp_or,
llvm::Intrinsic::vp_ptrtoint,
llvm::Intrinsic::vp_reduce_add,
llvm::Intrinsic::vp_reduce_and,
llvm::Intrinsic::vp_reduce_fadd,
llvm::Intrinsic::vp_reduce_fmax,
llvm::Intrinsic::vp_reduce_fmin,
llvm::Intrinsic::vp_reduce_fmul,
llvm::Intrinsic::vp_reduce_mul,
llvm::Intrinsic::vp_reduce_or,
llvm::Intrinsic::vp_reduce_smax,
llvm::Intrinsic::vp_reduce_smin,
llvm::Intrinsic::vp_reduce_umax,
llvm::Intrinsic::vp_reduce_umin,
llvm::Intrinsic::vp_reduce_xor,
llvm::Intrinsic::vp_sdiv,
llvm::Intrinsic::vp_sext,
llvm::Intrinsic::vp_sitofp,
llvm::Intrinsic::vp_srem,
llvm::Intrinsic::vp_select,
llvm::Intrinsic::vp_shl,
llvm::Intrinsic::vp_store,
llvm::Intrinsic::experimental_vp_strided_load,
llvm::Intrinsic::experimental_vp_strided_store,
llvm::Intrinsic::vp_sub,
llvm::Intrinsic::vp_trunc,
llvm::Intrinsic::vp_udiv,
llvm::Intrinsic::vp_uitofp,
llvm::Intrinsic::vp_urem,
llvm::Intrinsic::vp_xor,
llvm::Intrinsic::vp_zext,
llvm::Intrinsic::vacopy,
llvm::Intrinsic::vaend,
llvm::Intrinsic::vastart,
llvm::Intrinsic::masked_compressstore,
llvm::Intrinsic::masked_expandload,
llvm::Intrinsic::masked_gather,
llvm::Intrinsic::masked_scatter,
llvm::Intrinsic::vector_reduce_add,
llvm::Intrinsic::vector_reduce_and,
llvm::Intrinsic::vector_reduce_fadd,
llvm::Intrinsic::vector_reduce_fmax,
llvm::Intrinsic::vector_reduce_fmin,
llvm::Intrinsic::vector_reduce_fmul,
llvm::Intrinsic::vector_reduce_mul,
llvm::Intrinsic::vector_reduce_or,
llvm::Intrinsic::vector_reduce_smax,
llvm::Intrinsic::vector_reduce_smin,
llvm::Intrinsic::vector_reduce_umax,
llvm::Intrinsic::vector_reduce_umin,
llvm::Intrinsic::vector_reduce_xor,
llvm::Intrinsic::vscale,
