/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CONVERTELEMENTWISETOLINALG
#define GEN_PASS_DECL_LINALGBUFFERIZE
#define GEN_PASS_DECL_LINALGDETENSORIZE
#define GEN_PASS_DECL_LINALGELEMENTWISEOPFUSION
#define GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMS
#define GEN_PASS_DECL_LINALGGENERALIZATION
#define GEN_PASS_DECL_LINALGINLINESCALAROPERANDS
#define GEN_PASS_DECL_LINALGLOWERTOAFFINELOOPS
#define GEN_PASS_DECL_LINALGLOWERTOLOOPS
#define GEN_PASS_DECL_LINALGLOWERTOPARALLELLOOPS
#define GEN_PASS_DECL_LINALGNAMEDOPCONVERSION
#undef GEN_PASS_DECL
#endif // GEN_PA<PERSON>_<PERSON>CL

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTELEMENTWISETOLINALG
#undef GEN_PASS_DECL_CONVERTELEMENTWISETOLINALG
#endif // GEN_PASS_DECL_CONVERTELEMENTWISETOLINALG
#ifdef GEN_PASS_DEF_CONVERTELEMENTWISETOLINALG
namespace impl {

template <typename DerivedT>
class ConvertElementwiseToLinalgBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertElementwiseToLinalgBase;

  ConvertElementwiseToLinalgBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertElementwiseToLinalgBase(const ConvertElementwiseToLinalgBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-elementwise-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-elementwise-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert ElementwiseMappable ops to linalg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertElementwiseToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertElementwiseToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertElementwiseToLinalgBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTELEMENTWISETOLINALG
#endif // GEN_PASS_DEF_CONVERTELEMENTWISETOLINALG

//===----------------------------------------------------------------------===//
// LinalgBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGBUFFERIZE
#undef GEN_PASS_DECL_LINALGBUFFERIZE
#endif // GEN_PASS_DECL_LINALGBUFFERIZE
#ifdef GEN_PASS_DEF_LINALGBUFFERIZE
namespace impl {

template <typename DerivedT>
class LinalgBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgBufferizeBase;

  LinalgBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgBufferizeBase(const LinalgBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgBufferize");
  }
  ::llvm::StringRef getName() const override { return "LinalgBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGBUFFERIZE
#endif // GEN_PASS_DEF_LINALGBUFFERIZE

//===----------------------------------------------------------------------===//
// LinalgDetensorize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGDETENSORIZE
struct LinalgDetensorizeOptions {
  bool aggressiveMode = false;
};
#undef GEN_PASS_DECL_LINALGDETENSORIZE
#endif // GEN_PASS_DECL_LINALGDETENSORIZE
#ifdef GEN_PASS_DEF_LINALGDETENSORIZE
namespace impl {

template <typename DerivedT>
class LinalgDetensorizeBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = LinalgDetensorizeBase;

  LinalgDetensorizeBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgDetensorizeBase(const LinalgDetensorizeBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-detensorize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-detensorize"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize linalg ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgDetensorize");
  }
  ::llvm::StringRef getName() const override { return "LinalgDetensorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgDetensorizeBase<DerivedT>)

  LinalgDetensorizeBase(const LinalgDetensorizeOptions &options) : LinalgDetensorizeBase() {
    aggressiveMode = options.aggressiveMode;
  }
protected:
  ::mlir::Pass::Option<bool> aggressiveMode{*this, "aggressive-mode", ::llvm::cl::desc("Detensorize all ops that qualify for detensoring along with branch operands and basic-block arguments."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGDETENSORIZE
#endif // GEN_PASS_DEF_LINALGDETENSORIZE

//===----------------------------------------------------------------------===//
// LinalgElementwiseOpFusion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGELEMENTWISEOPFUSION
#undef GEN_PASS_DECL_LINALGELEMENTWISEOPFUSION
#endif // GEN_PASS_DECL_LINALGELEMENTWISEOPFUSION
#ifdef GEN_PASS_DEF_LINALGELEMENTWISEOPFUSION
namespace impl {

template <typename DerivedT>
class LinalgElementwiseOpFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgElementwiseOpFusionBase;

  LinalgElementwiseOpFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgElementwiseOpFusionBase(const LinalgElementwiseOpFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fuse-elementwise-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fuse-elementwise-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fuse elementwise operations on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgElementwiseOpFusion");
  }
  ::llvm::StringRef getName() const override { return "LinalgElementwiseOpFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgElementwiseOpFusionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGELEMENTWISEOPFUSION
#endif // GEN_PASS_DEF_LINALGELEMENTWISEOPFUSION

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDims
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMS
struct LinalgFoldUnitExtentDimsOptions {
  bool foldOneTripLoopsOnly = false;
  bool useRankReducingSlices = false;
};
#undef GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMS
#endif // GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMS
#ifdef GEN_PASS_DEF_LINALGFOLDUNITEXTENTDIMS
namespace impl {

template <typename DerivedT>
class LinalgFoldUnitExtentDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldUnitExtentDimsBase;

  LinalgFoldUnitExtentDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldUnitExtentDimsBase(const LinalgFoldUnitExtentDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-unit-extent-dims");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-unit-extent-dims"; }

  ::llvm::StringRef getDescription() const override { return "Remove unit-extent dimension in Linalg ops on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldUnitExtentDims");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldUnitExtentDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgFoldUnitExtentDimsBase<DerivedT>)

  LinalgFoldUnitExtentDimsBase(const LinalgFoldUnitExtentDimsOptions &options) : LinalgFoldUnitExtentDimsBase() {
    foldOneTripLoopsOnly = options.foldOneTripLoopsOnly;
    useRankReducingSlices = options.useRankReducingSlices;
  }
protected:
  ::mlir::Pass::Option<bool> foldOneTripLoopsOnly{*this, "fold-one-trip-loops-only", ::llvm::cl::desc("Only folds the one-trip loops from Linalg ops on tensors (for testing purposes only)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useRankReducingSlices{*this, "use-rank-reducing-slices", ::llvm::cl::desc("Generate rank-reducing slices instead of reassociative reshapes"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGFOLDUNITEXTENTDIMS
#endif // GEN_PASS_DEF_LINALGFOLDUNITEXTENTDIMS

//===----------------------------------------------------------------------===//
// LinalgGeneralization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGGENERALIZATION
#undef GEN_PASS_DECL_LINALGGENERALIZATION
#endif // GEN_PASS_DECL_LINALGGENERALIZATION
#ifdef GEN_PASS_DEF_LINALGGENERALIZATION
namespace impl {

template <typename DerivedT>
class LinalgGeneralizationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgGeneralizationBase;

  LinalgGeneralizationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgGeneralizationBase(const LinalgGeneralizationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-generalize-named-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-generalize-named-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert named ops into generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgGeneralization");
  }
  ::llvm::StringRef getName() const override { return "LinalgGeneralization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgGeneralizationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGGENERALIZATION
#endif // GEN_PASS_DEF_LINALGGENERALIZATION

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperands
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGINLINESCALAROPERANDS
#undef GEN_PASS_DECL_LINALGINLINESCALAROPERANDS
#endif // GEN_PASS_DECL_LINALGINLINESCALAROPERANDS
#ifdef GEN_PASS_DEF_LINALGINLINESCALAROPERANDS
namespace impl {

template <typename DerivedT>
class LinalgInlineScalarOperandsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgInlineScalarOperandsBase;

  LinalgInlineScalarOperandsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgInlineScalarOperandsBase(const LinalgInlineScalarOperandsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-inline-scalar-operands");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-inline-scalar-operands"; }

  ::llvm::StringRef getDescription() const override { return "Inline scalar operands into linalg generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgInlineScalarOperands");
  }
  ::llvm::StringRef getName() const override { return "LinalgInlineScalarOperands"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgInlineScalarOperandsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGINLINESCALAROPERANDS
#endif // GEN_PASS_DEF_LINALGINLINESCALAROPERANDS

//===----------------------------------------------------------------------===//
// LinalgLowerToAffineLoops
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGLOWERTOAFFINELOOPS
#undef GEN_PASS_DECL_LINALGLOWERTOAFFINELOOPS
#endif // GEN_PASS_DECL_LINALGLOWERTOAFFINELOOPS
#ifdef GEN_PASS_DEF_LINALGLOWERTOAFFINELOOPS
namespace impl {

template <typename DerivedT>
class LinalgLowerToAffineLoopsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgLowerToAffineLoopsBase;

  LinalgLowerToAffineLoopsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToAffineLoopsBase(const LinalgLowerToAffineLoopsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-affine-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-affine-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToAffineLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToAffineLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgLowerToAffineLoopsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGLOWERTOAFFINELOOPS
#endif // GEN_PASS_DEF_LINALGLOWERTOAFFINELOOPS

//===----------------------------------------------------------------------===//
// LinalgLowerToLoops
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGLOWERTOLOOPS
#undef GEN_PASS_DECL_LINALGLOWERTOLOOPS
#endif // GEN_PASS_DECL_LINALGLOWERTOLOOPS
#ifdef GEN_PASS_DEF_LINALGLOWERTOLOOPS
namespace impl {

template <typename DerivedT>
class LinalgLowerToLoopsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgLowerToLoopsBase;

  LinalgLowerToLoopsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToLoopsBase(const LinalgLowerToLoopsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgLowerToLoopsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGLOWERTOLOOPS
#endif // GEN_PASS_DEF_LINALGLOWERTOLOOPS

//===----------------------------------------------------------------------===//
// LinalgLowerToParallelLoops
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGLOWERTOPARALLELLOOPS
#undef GEN_PASS_DECL_LINALGLOWERTOPARALLELLOOPS
#endif // GEN_PASS_DECL_LINALGLOWERTOPARALLELLOOPS
#ifdef GEN_PASS_DEF_LINALGLOWERTOPARALLELLOOPS
namespace impl {

template <typename DerivedT>
class LinalgLowerToParallelLoopsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgLowerToParallelLoopsBase;

  LinalgLowerToParallelLoopsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToParallelLoopsBase(const LinalgLowerToParallelLoopsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToParallelLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToParallelLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgLowerToParallelLoopsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGLOWERTOPARALLELLOOPS
#endif // GEN_PASS_DEF_LINALGLOWERTOPARALLELLOOPS

//===----------------------------------------------------------------------===//
// LinalgNamedOpConversion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGNAMEDOPCONVERSION
#undef GEN_PASS_DECL_LINALGNAMEDOPCONVERSION
#endif // GEN_PASS_DECL_LINALGNAMEDOPCONVERSION
#ifdef GEN_PASS_DEF_LINALGNAMEDOPCONVERSION
namespace impl {

template <typename DerivedT>
class LinalgNamedOpConversionBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgNamedOpConversionBase;

  LinalgNamedOpConversionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgNamedOpConversionBase(const LinalgNamedOpConversionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-named-op-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-named-op-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert from one named linalg op to another."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgNamedOpConversion");
  }
  ::llvm::StringRef getName() const override { return "LinalgNamedOpConversion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgNamedOpConversionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LINALGNAMEDOPCONVERSION
#endif // GEN_PASS_DEF_LINALGNAMEDOPCONVERSION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerConvertElementwiseToLinalg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertElementwiseToLinalgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertElementwiseToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertElementwiseToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgDetensorize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgDetensorize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgDetensorizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgDetensorizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgDetensorizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgElementwiseOpFusion Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgElementwiseOpFusion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgElementwiseOpFusionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgElementwiseOpFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgElementwiseOpFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDims Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFoldUnitExtentDims() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgFoldUnitExtentDimsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgFoldUnitExtentDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgFoldUnitExtentDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgGeneralization Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgGeneralization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgGeneralizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgGeneralizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgGeneralizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperands Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgInlineScalarOperands() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgInlineScalarOperandsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgInlineScalarOperandsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgInlineScalarOperandsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToAffineLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToAffineLoops() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToAffineLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgLowerToAffineLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToAffineLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToLoops() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgLowerToLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToParallelLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToParallelLoops() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToParallelLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgLowerToParallelLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgNamedOpConversion Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgNamedOpConversion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgNamedOpConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgNamedOpConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgNamedOpConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// Linalg Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgPasses() {
  registerConvertElementwiseToLinalg();
  registerLinalgBufferize();
  registerLinalgDetensorize();
  registerLinalgElementwiseOpFusion();
  registerLinalgFoldUnitExtentDims();
  registerLinalgGeneralization();
  registerLinalgInlineScalarOperands();
  registerLinalgLowerToAffineLoops();
  registerLinalgLowerToLoops();
  registerLinalgLowerToParallelLoops();
  registerLinalgNamedOpConversion();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ConvertElementwiseToLinalgBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertElementwiseToLinalgBase;

  ConvertElementwiseToLinalgBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertElementwiseToLinalgBase(const ConvertElementwiseToLinalgBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-elementwise-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-elementwise-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert ElementwiseMappable ops to linalg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertElementwiseToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertElementwiseToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertElementwiseToLinalgBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgBufferizeBase;

  LinalgBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgBufferizeBase(const LinalgBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgBufferize");
  }
  ::llvm::StringRef getName() const override { return "LinalgBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgBufferizeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgDetensorizeBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = LinalgDetensorizeBase;

  LinalgDetensorizeBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgDetensorizeBase(const LinalgDetensorizeBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-detensorize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-detensorize"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize linalg ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgDetensorize");
  }
  ::llvm::StringRef getName() const override { return "LinalgDetensorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgDetensorizeBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> aggressiveMode{*this, "aggressive-mode", ::llvm::cl::desc("Detensorize all ops that qualify for detensoring along with branch operands and basic-block arguments."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LinalgElementwiseOpFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgElementwiseOpFusionBase;

  LinalgElementwiseOpFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgElementwiseOpFusionBase(const LinalgElementwiseOpFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fuse-elementwise-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fuse-elementwise-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fuse elementwise operations on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgElementwiseOpFusion");
  }
  ::llvm::StringRef getName() const override { return "LinalgElementwiseOpFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgElementwiseOpFusionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgFoldUnitExtentDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldUnitExtentDimsBase;

  LinalgFoldUnitExtentDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldUnitExtentDimsBase(const LinalgFoldUnitExtentDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-unit-extent-dims");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-unit-extent-dims"; }

  ::llvm::StringRef getDescription() const override { return "Remove unit-extent dimension in Linalg ops on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldUnitExtentDims");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldUnitExtentDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgFoldUnitExtentDimsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> foldOneTripLoopsOnly{*this, "fold-one-trip-loops-only", ::llvm::cl::desc("Only folds the one-trip loops from Linalg ops on tensors (for testing purposes only)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useRankReducingSlices{*this, "use-rank-reducing-slices", ::llvm::cl::desc("Generate rank-reducing slices instead of reassociative reshapes"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LinalgGeneralizationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgGeneralizationBase;

  LinalgGeneralizationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgGeneralizationBase(const LinalgGeneralizationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-generalize-named-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-generalize-named-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert named ops into generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgGeneralization");
  }
  ::llvm::StringRef getName() const override { return "LinalgGeneralization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgGeneralizationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgInlineScalarOperandsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgInlineScalarOperandsBase;

  LinalgInlineScalarOperandsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgInlineScalarOperandsBase(const LinalgInlineScalarOperandsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-inline-scalar-operands");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-inline-scalar-operands"; }

  ::llvm::StringRef getDescription() const override { return "Inline scalar operands into linalg generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgInlineScalarOperands");
  }
  ::llvm::StringRef getName() const override { return "LinalgInlineScalarOperands"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgInlineScalarOperandsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgLowerToAffineLoopsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgLowerToAffineLoopsBase;

  LinalgLowerToAffineLoopsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToAffineLoopsBase(const LinalgLowerToAffineLoopsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-affine-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-affine-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToAffineLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToAffineLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgLowerToAffineLoopsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgLowerToLoopsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgLowerToLoopsBase;

  LinalgLowerToLoopsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToLoopsBase(const LinalgLowerToLoopsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgLowerToLoopsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgLowerToParallelLoopsBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LinalgLowerToParallelLoopsBase;

  LinalgLowerToParallelLoopsBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToParallelLoopsBase(const LinalgLowerToParallelLoopsBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToParallelLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToParallelLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgLowerToParallelLoopsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgNamedOpConversionBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgNamedOpConversionBase;

  LinalgNamedOpConversionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgNamedOpConversionBase(const LinalgNamedOpConversionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-named-op-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-named-op-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert from one named linalg op to another."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgNamedOpConversion");
  }
  ::llvm::StringRef getName() const override { return "LinalgNamedOpConversion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgNamedOpConversionBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
