test: (use augment code - use new thread)

choose a chess engine

frameworks / packages to use
(python-chess ; )

- create working chess bot 
- for the AI, use sophisticated algorithms, NOT an LLM


- get AI to be able to play against itself or an AI opponent
[create menu before playing a game to select either User vs AI or AI vs AI - one requiring the user to participate (human player against AI) and the other where the user observes the game between 2 AI's.]
[option to play again]
[keep track of all changing variables]

------------

do research online for examples of creating chess bots