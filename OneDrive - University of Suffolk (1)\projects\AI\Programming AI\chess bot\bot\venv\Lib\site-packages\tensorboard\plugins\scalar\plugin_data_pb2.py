# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/scalar/plugin_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,tensorboard/plugins/scalar/plugin_data.proto\x12\x0btensorboard\"#\n\x10ScalarPluginData\x12\x0f\n\x07version\x18\x01 \x01(\x05\x62\x06proto3')



_SCALARPLUGINDATA = DESCRIPTOR.message_types_by_name['ScalarPluginData']
ScalarPluginData = _reflection.GeneratedProtocolMessageType('ScalarPluginData', (_message.Message,), {
  'DESCRIPTOR' : _SCALARPLUGINDATA,
  '__module__' : 'tensorboard.plugins.scalar.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.ScalarPluginData)
  })
_sym_db.RegisterMessage(ScalarPluginData)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SCALARPLUGINDATA._serialized_start=61
  _SCALARPLUGINDATA._serialized_end=96
# @@protoc_insertion_point(module_scope)
