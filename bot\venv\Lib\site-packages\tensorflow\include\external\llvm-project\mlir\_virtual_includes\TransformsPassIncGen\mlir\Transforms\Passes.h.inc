/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CSE
#define GEN_PASS_DECL_CANONICALIZER
#define GEN_PASS_DECL_CONTROLFLOWSINK
#define GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#define GEN_PASS_DECL_INLINER
#define GEN_PASS_DECL_LOCATIONSNAPSHOT
#define GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#define GEN_PASS_DECL_MEM2REG
#define GEN_PASS_DECL_PRINTIRPASS
#define GEN_PASS_DECL_PRINTOPSTATS
#define GEN_PASS_DECL_SCCP
#define GEN_PASS_DECL_STRIPDEBUGINFO
#define GEN_PASS_DECL_SYMBOLDCE
#define GEN_PASS_DECL_SYMBOLPRIVATIZE
#define GEN_PASS_DECL_TOPOLOGICALSORT
#define GEN_PASS_DECL_VIEWOPGRAPH
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// CSE
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CSE
#undef GEN_PASS_DECL_CSE
#endif // GEN_PASS_DECL_CSE
#ifdef GEN_PASS_DEF_CSE
namespace impl {

template <typename DerivedT>
class CSEBase : public ::mlir::OperationPass<> {
public:
  using Base = CSEBase;

  CSEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CSEBase(const CSEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cse");
  }
  ::llvm::StringRef getArgument() const override { return "cse"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate common sub-expressions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSE");
  }
  ::llvm::StringRef getName() const override { return "CSE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CSEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numCSE{this, "num-cse'd", "Number of operations CSE'd"};
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of operations DCE'd"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CSE
#endif // GEN_PASS_DEF_CSE

//===----------------------------------------------------------------------===//
// Canonicalizer
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CANONICALIZER
struct CanonicalizerOptions {
  bool topDownProcessingEnabled = true;
  bool enableRegionSimplification = true;
  int64_t maxIterations = 10;
  int64_t maxNumRewrites = -1;
  bool testConvergence = false;
  ::llvm::ArrayRef<std::string> disabledPatterns;
  ::llvm::ArrayRef<std::string> enabledPatterns;
};
#undef GEN_PASS_DECL_CANONICALIZER
#endif // GEN_PASS_DECL_CANONICALIZER
#ifdef GEN_PASS_DEF_CANONICALIZER
namespace impl {

template <typename DerivedT>
class CanonicalizerBase : public ::mlir::OperationPass<> {
public:
  using Base = CanonicalizerBase;

  CanonicalizerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CanonicalizerBase(const CanonicalizerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("canonicalize");
  }
  ::llvm::StringRef getArgument() const override { return "canonicalize"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Canonicalizer");
  }
  ::llvm::StringRef getName() const override { return "Canonicalizer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CanonicalizerBase<DerivedT>)

  CanonicalizerBase(const CanonicalizerOptions &options) : CanonicalizerBase() {
    topDownProcessingEnabled = options.topDownProcessingEnabled;
    enableRegionSimplification = options.enableRegionSimplification;
    maxIterations = options.maxIterations;
    maxNumRewrites = options.maxNumRewrites;
    testConvergence = options.testConvergence;
    disabledPatterns = options.disabledPatterns;
    enabledPatterns = options.enabledPatterns;
  }
protected:
  ::mlir::Pass::Option<bool> topDownProcessingEnabled{*this, "top-down", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Perform control flow optimizations to the region tree"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int64_t> maxIterations{*this, "max-iterations", ::llvm::cl::desc("Max. iterations between applying patterns / simplifying regions"), ::llvm::cl::init(10)};
  ::mlir::Pass::Option<int64_t> maxNumRewrites{*this, "max-num-rewrites", ::llvm::cl::desc("Max. number of pattern rewrites within an iteration"), ::llvm::cl::init(-1)};
  ::mlir::Pass::Option<bool> testConvergence{*this, "test-convergence", ::llvm::cl::desc("Test only: Fail pass on non-convergence to detect cyclic pattern"), ::llvm::cl::init(false)};
  ::mlir::Pass::ListOption<std::string> disabledPatterns{*this, "disable-patterns", ::llvm::cl::desc("Labels of patterns that should be filtered out during application")};
  ::mlir::Pass::ListOption<std::string> enabledPatterns{*this, "enable-patterns", ::llvm::cl::desc("Labels of patterns that should be used during application, all other patterns are filtered out")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CANONICALIZER
#endif // GEN_PASS_DEF_CANONICALIZER

//===----------------------------------------------------------------------===//
// ControlFlowSink
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONTROLFLOWSINK
#undef GEN_PASS_DECL_CONTROLFLOWSINK
#endif // GEN_PASS_DECL_CONTROLFLOWSINK
#ifdef GEN_PASS_DEF_CONTROLFLOWSINK
namespace impl {

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("control-flow-sink");
  }
  ::llvm::StringRef getArgument() const override { return "control-flow-sink"; }

  ::llvm::StringRef getDescription() const override { return "Sink operations into conditional blocks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ControlFlowSinkBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numSunk{this, "num-sunk", "Number of operations sunk"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONTROLFLOWSINK
#endif // GEN_PASS_DEF_CONTROLFLOWSINK

//===----------------------------------------------------------------------===//
// GenerateRuntimeVerification
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#undef GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#endif // GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#ifdef GEN_PASS_DEF_GENERATERUNTIMEVERIFICATION
namespace impl {

template <typename DerivedT>
class GenerateRuntimeVerificationBase : public ::mlir::OperationPass<> {
public:
  using Base = GenerateRuntimeVerificationBase;

  GenerateRuntimeVerificationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  GenerateRuntimeVerificationBase(const GenerateRuntimeVerificationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("generate-runtime-verification");
  }
  ::llvm::StringRef getArgument() const override { return "generate-runtime-verification"; }

  ::llvm::StringRef getDescription() const override { return "Generate additional runtime op verification checks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GenerateRuntimeVerification");
  }
  ::llvm::StringRef getName() const override { return "GenerateRuntimeVerification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GenerateRuntimeVerificationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GENERATERUNTIMEVERIFICATION
#endif // GEN_PASS_DEF_GENERATERUNTIMEVERIFICATION

//===----------------------------------------------------------------------===//
// Inliner
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INLINER
struct InlinerOptions {
  std::string defaultPipelineStr = "canonicalize";
  ::llvm::ArrayRef<OpPassManager> opPipelineList;
  unsigned maxInliningIterations = 4;
};
#undef GEN_PASS_DECL_INLINER
#endif // GEN_PASS_DECL_INLINER
#ifdef GEN_PASS_DEF_INLINER
namespace impl {

template <typename DerivedT>
class InlinerBase : public ::mlir::OperationPass<> {
public:
  using Base = InlinerBase;

  InlinerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InlinerBase(const InlinerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("inline");
  }
  ::llvm::StringRef getArgument() const override { return "inline"; }

  ::llvm::StringRef getDescription() const override { return "Inline function calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Inliner");
  }
  ::llvm::StringRef getName() const override { return "Inliner"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InlinerBase<DerivedT>)

  InlinerBase(const InlinerOptions &options) : InlinerBase() {
    defaultPipelineStr = options.defaultPipelineStr;
    opPipelineList = options.opPipelineList;
    maxInliningIterations = options.maxInliningIterations;
  }
protected:
  ::mlir::Pass::Option<std::string> defaultPipelineStr{*this, "default-pipeline", ::llvm::cl::desc("The default optimizer pipeline used for callables"), ::llvm::cl::init("canonicalize")};
  ::mlir::Pass::ListOption<OpPassManager> opPipelineList{*this, "op-pipelines", ::llvm::cl::desc("Callable operation specific optimizer pipelines (in the form of `dialect.op(pipeline)`)")};
  ::mlir::Pass::Option<unsigned> maxInliningIterations{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations when inlining within an SCC"), ::llvm::cl::init(4)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_INLINER
#endif // GEN_PASS_DEF_INLINER

//===----------------------------------------------------------------------===//
// LocationSnapshot
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOCATIONSNAPSHOT
struct LocationSnapshotOptions {
  std::string fileName;
  std::string tag;
};
#undef GEN_PASS_DECL_LOCATIONSNAPSHOT
#endif // GEN_PASS_DECL_LOCATIONSNAPSHOT
#ifdef GEN_PASS_DEF_LOCATIONSNAPSHOT
namespace impl {

template <typename DerivedT>
class LocationSnapshotBase : public ::mlir::OperationPass<> {
public:
  using Base = LocationSnapshotBase;

  LocationSnapshotBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LocationSnapshotBase(const LocationSnapshotBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("snapshot-op-locations");
  }
  ::llvm::StringRef getArgument() const override { return "snapshot-op-locations"; }

  ::llvm::StringRef getDescription() const override { return "Generate new locations from the current IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LocationSnapshot");
  }
  ::llvm::StringRef getName() const override { return "LocationSnapshot"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LocationSnapshotBase<DerivedT>)

  LocationSnapshotBase(const LocationSnapshotOptions &options) : LocationSnapshotBase() {
    fileName = options.fileName;
    tag = options.tag;
  }
protected:
  ::mlir::Pass::Option<std::string> fileName{*this, "filename", ::llvm::cl::desc("The filename to print the generated IR")};
  ::mlir::Pass::Option<std::string> tag{*this, "tag", ::llvm::cl::desc("A tag to use when fusing the new locations with the original. If unset, the locations are replaced.")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOCATIONSNAPSHOT
#endif // GEN_PASS_DEF_LOCATIONSNAPSHOT

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#undef GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#endif // GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#ifdef GEN_PASS_DEF_LOOPINVARIANTCODEMOTION
namespace impl {

template <typename DerivedT>
class LoopInvariantCodeMotionBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantCodeMotionBase;

  LoopInvariantCodeMotionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantCodeMotionBase(const LoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopInvariantCodeMotionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOOPINVARIANTCODEMOTION
#endif // GEN_PASS_DEF_LOOPINVARIANTCODEMOTION

//===----------------------------------------------------------------------===//
// Mem2Reg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MEM2REG
std::unique_ptr<::mlir::Pass> createMem2Reg();
#undef GEN_PASS_DECL_MEM2REG
#endif // GEN_PASS_DECL_MEM2REG
#ifdef GEN_PASS_DEF_MEM2REG

namespace impl {
  std::unique_ptr<::mlir::Pass> createMem2Reg();
} // namespace impl
namespace impl {

template <typename DerivedT>
class Mem2RegBase : public ::mlir::OperationPass<> {
public:
  using Base = Mem2RegBase;

  Mem2RegBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  Mem2RegBase(const Mem2RegBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mem2reg");
  }
  ::llvm::StringRef getArgument() const override { return "mem2reg"; }

  ::llvm::StringRef getDescription() const override { return "Promotes memory slots into values."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Mem2Reg");
  }
  ::llvm::StringRef getName() const override { return "Mem2Reg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(Mem2RegBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createMem2Reg() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createMem2Reg() {
  return impl::createMem2Reg();
}
#undef GEN_PASS_DEF_MEM2REG
#endif // GEN_PASS_DEF_MEM2REG

//===----------------------------------------------------------------------===//
// PrintIRPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PRINTIRPASS
struct PrintIRPassOptions {
  std::string label;
};
#undef GEN_PASS_DECL_PRINTIRPASS
#endif // GEN_PASS_DECL_PRINTIRPASS
#ifdef GEN_PASS_DEF_PRINTIRPASS
namespace impl {

template <typename DerivedT>
class PrintIRPassBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintIRPassBase;

  PrintIRPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintIRPassBase(const PrintIRPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-ir");
  }
  ::llvm::StringRef getArgument() const override { return "print-ir"; }

  ::llvm::StringRef getDescription() const override { return "Print IR on the debug stream"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintIRPass");
  }
  ::llvm::StringRef getName() const override { return "PrintIRPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintIRPassBase<DerivedT>)

  PrintIRPassBase(const PrintIRPassOptions &options) : PrintIRPassBase() {
    label = options.label;
  }
protected:
  ::mlir::Pass::Option<std::string> label{*this, "label", ::llvm::cl::desc("Label")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PRINTIRPASS
#endif // GEN_PASS_DEF_PRINTIRPASS

//===----------------------------------------------------------------------===//
// PrintOpStats
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PRINTOPSTATS
struct PrintOpStatsOptions {
  bool printAsJSON = false;
};
#undef GEN_PASS_DECL_PRINTOPSTATS
#endif // GEN_PASS_DECL_PRINTOPSTATS
#ifdef GEN_PASS_DEF_PRINTOPSTATS
namespace impl {

template <typename DerivedT>
class PrintOpStatsBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintOpStatsBase;

  PrintOpStatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintOpStatsBase(const PrintOpStatsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-op-stats");
  }
  ::llvm::StringRef getArgument() const override { return "print-op-stats"; }

  ::llvm::StringRef getDescription() const override { return "Print statistics of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintOpStats");
  }
  ::llvm::StringRef getName() const override { return "PrintOpStats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintOpStatsBase<DerivedT>)

  PrintOpStatsBase(const PrintOpStatsOptions &options) : PrintOpStatsBase() {
    printAsJSON = options.printAsJSON;
  }
protected:
  ::mlir::Pass::Option<bool> printAsJSON{*this, "json", ::llvm::cl::desc("print the stats as JSON"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PRINTOPSTATS
#endif // GEN_PASS_DEF_PRINTOPSTATS

//===----------------------------------------------------------------------===//
// SCCP
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCCP
#undef GEN_PASS_DECL_SCCP
#endif // GEN_PASS_DECL_SCCP
#ifdef GEN_PASS_DEF_SCCP
namespace impl {

template <typename DerivedT>
class SCCPBase : public ::mlir::OperationPass<> {
public:
  using Base = SCCPBase;

  SCCPBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCCPBase(const SCCPBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sccp");
  }
  ::llvm::StringRef getArgument() const override { return "sccp"; }

  ::llvm::StringRef getDescription() const override { return "Sparse Conditional Constant Propagation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCCP");
  }
  ::llvm::StringRef getName() const override { return "SCCP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCCPBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCCP
#endif // GEN_PASS_DEF_SCCP

//===----------------------------------------------------------------------===//
// StripDebugInfo
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STRIPDEBUGINFO
#undef GEN_PASS_DECL_STRIPDEBUGINFO
#endif // GEN_PASS_DECL_STRIPDEBUGINFO
#ifdef GEN_PASS_DEF_STRIPDEBUGINFO
namespace impl {

template <typename DerivedT>
class StripDebugInfoBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDebugInfoBase;

  StripDebugInfoBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDebugInfoBase(const StripDebugInfoBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("strip-debuginfo");
  }
  ::llvm::StringRef getArgument() const override { return "strip-debuginfo"; }

  ::llvm::StringRef getDescription() const override { return "Strip debug info from all operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDebugInfo");
  }
  ::llvm::StringRef getName() const override { return "StripDebugInfo"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripDebugInfoBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STRIPDEBUGINFO
#endif // GEN_PASS_DEF_STRIPDEBUGINFO

//===----------------------------------------------------------------------===//
// SymbolDCE
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SYMBOLDCE
#undef GEN_PASS_DECL_SYMBOLDCE
#endif // GEN_PASS_DECL_SYMBOLDCE
#ifdef GEN_PASS_DEF_SYMBOLDCE
namespace impl {

template <typename DerivedT>
class SymbolDCEBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolDCEBase;

  SymbolDCEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolDCEBase(const SymbolDCEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-dce");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-dce"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate dead symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolDCE");
  }
  ::llvm::StringRef getName() const override { return "SymbolDCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolDCEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of symbols DCE'd"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SYMBOLDCE
#endif // GEN_PASS_DEF_SYMBOLDCE

//===----------------------------------------------------------------------===//
// SymbolPrivatize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SYMBOLPRIVATIZE
struct SymbolPrivatizeOptions {
  ::llvm::ArrayRef<std::string> exclude;
};
#undef GEN_PASS_DECL_SYMBOLPRIVATIZE
#endif // GEN_PASS_DECL_SYMBOLPRIVATIZE
#ifdef GEN_PASS_DEF_SYMBOLPRIVATIZE
namespace impl {

template <typename DerivedT>
class SymbolPrivatizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolPrivatizeBase;

  SymbolPrivatizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolPrivatizeBase(const SymbolPrivatizeBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-privatize");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-privatize"; }

  ::llvm::StringRef getDescription() const override { return "Mark symbols private"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolPrivatize");
  }
  ::llvm::StringRef getName() const override { return "SymbolPrivatize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolPrivatizeBase<DerivedT>)

  SymbolPrivatizeBase(const SymbolPrivatizeOptions &options) : SymbolPrivatizeBase() {
    exclude = options.exclude;
  }
protected:
  ::mlir::Pass::ListOption<std::string> exclude{*this, "exclude", ::llvm::cl::desc("Comma separated list of symbols that should not be marked private")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SYMBOLPRIVATIZE
#endif // GEN_PASS_DEF_SYMBOLPRIVATIZE

//===----------------------------------------------------------------------===//
// TopologicalSort
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOPOLOGICALSORT
#undef GEN_PASS_DECL_TOPOLOGICALSORT
#endif // GEN_PASS_DECL_TOPOLOGICALSORT
#ifdef GEN_PASS_DEF_TOPOLOGICALSORT
namespace impl {

template <typename DerivedT>
class TopologicalSortBase : public ::mlir::OperationPass<> {
public:
  using Base = TopologicalSortBase;

  TopologicalSortBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TopologicalSortBase(const TopologicalSortBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("topological-sort");
  }
  ::llvm::StringRef getArgument() const override { return "topological-sort"; }

  ::llvm::StringRef getDescription() const override { return "Sort regions without SSA dominance in topological order"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TopologicalSort");
  }
  ::llvm::StringRef getName() const override { return "TopologicalSort"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TopologicalSortBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOPOLOGICALSORT
#endif // GEN_PASS_DEF_TOPOLOGICALSORT

//===----------------------------------------------------------------------===//
// ViewOpGraph
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VIEWOPGRAPH
struct ViewOpGraphOptions {
  unsigned maxLabelLen = 20;
  bool printAttrs = true;
  bool printControlFlowEdges = false;
  bool printDataFlowEdges = true;
  bool printResultTypes = true;
};
#undef GEN_PASS_DECL_VIEWOPGRAPH
#endif // GEN_PASS_DECL_VIEWOPGRAPH
#ifdef GEN_PASS_DEF_VIEWOPGRAPH
namespace impl {

template <typename DerivedT>
class ViewOpGraphBase : public ::mlir::OperationPass<> {
public:
  using Base = ViewOpGraphBase;

  ViewOpGraphBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ViewOpGraphBase(const ViewOpGraphBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("view-op-graph");
  }
  ::llvm::StringRef getArgument() const override { return "view-op-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print Graphviz visualization of an operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ViewOpGraph");
  }
  ::llvm::StringRef getName() const override { return "ViewOpGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ViewOpGraphBase<DerivedT>)

  ViewOpGraphBase(const ViewOpGraphOptions &options) : ViewOpGraphBase() {
    maxLabelLen = options.maxLabelLen;
    printAttrs = options.printAttrs;
    printControlFlowEdges = options.printControlFlowEdges;
    printDataFlowEdges = options.printDataFlowEdges;
    printResultTypes = options.printResultTypes;
  }
protected:
  ::mlir::Pass::Option<unsigned> maxLabelLen{*this, "max-label-len", ::llvm::cl::desc("Limit attribute/type length to number of chars"), ::llvm::cl::init(20)};
  ::mlir::Pass::Option<bool> printAttrs{*this, "print-attrs", ::llvm::cl::desc("Print attributes of operations"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printControlFlowEdges{*this, "print-control-flow-edges", ::llvm::cl::desc("Print control flow edges"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printDataFlowEdges{*this, "print-data-flow-edges", ::llvm::cl::desc("Print data flow edges"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printResultTypes{*this, "print-result-types", ::llvm::cl::desc("Print result types of operations"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VIEWOPGRAPH
#endif // GEN_PASS_DEF_VIEWOPGRAPH
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// CSE Registration
//===----------------------------------------------------------------------===//

inline void registerCSE() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCSEPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCSEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCSEPass();
  });
}

//===----------------------------------------------------------------------===//
// Canonicalizer Registration
//===----------------------------------------------------------------------===//

inline void registerCanonicalizer() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCanonicalizerPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCanonicalizerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCanonicalizerPass();
  });
}

//===----------------------------------------------------------------------===//
// ControlFlowSink Registration
//===----------------------------------------------------------------------===//

inline void registerControlFlowSink() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::createControlFlowSinkPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerControlFlowSinkPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::createControlFlowSinkPass();
  });
}

//===----------------------------------------------------------------------===//
// GenerateRuntimeVerification Registration
//===----------------------------------------------------------------------===//

inline void registerGenerateRuntimeVerification() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGenerateRuntimeVerificationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGenerateRuntimeVerificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGenerateRuntimeVerificationPass();
  });
}

//===----------------------------------------------------------------------===//
// Inliner Registration
//===----------------------------------------------------------------------===//

inline void registerInliner() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createInlinerPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInlinerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createInlinerPass();
  });
}

//===----------------------------------------------------------------------===//
// LocationSnapshot Registration
//===----------------------------------------------------------------------===//

inline void registerLocationSnapshot() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLocationSnapshotPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLocationSnapshotPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLocationSnapshotPass();
  });
}

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion Registration
//===----------------------------------------------------------------------===//

inline void registerLoopInvariantCodeMotion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantCodeMotionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLoopInvariantCodeMotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantCodeMotionPass();
  });
}

//===----------------------------------------------------------------------===//
// Mem2Reg Registration
//===----------------------------------------------------------------------===//

inline void registerMem2Reg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMem2Reg();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMem2RegPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMem2Reg();
  });
}

//===----------------------------------------------------------------------===//
// PrintIRPass Registration
//===----------------------------------------------------------------------===//

inline void registerPrintIRPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintIRPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPrintIRPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintIRPass();
  });
}

//===----------------------------------------------------------------------===//
// PrintOpStats Registration
//===----------------------------------------------------------------------===//

inline void registerPrintOpStats() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpStatsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPrintOpStatsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpStatsPass();
  });
}

//===----------------------------------------------------------------------===//
// SCCP Registration
//===----------------------------------------------------------------------===//

inline void registerSCCP() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCCPPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCCPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCCPPass();
  });
}

//===----------------------------------------------------------------------===//
// StripDebugInfo Registration
//===----------------------------------------------------------------------===//

inline void registerStripDebugInfo() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStripDebugInfoPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStripDebugInfoPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStripDebugInfoPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolDCE Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolDCE() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolDCEPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSymbolDCEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolDCEPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolPrivatize Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolPrivatize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolPrivatizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSymbolPrivatizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolPrivatizePass();
  });
}

//===----------------------------------------------------------------------===//
// TopologicalSort Registration
//===----------------------------------------------------------------------===//

inline void registerTopologicalSort() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTopologicalSortPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTopologicalSortPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTopologicalSortPass();
  });
}

//===----------------------------------------------------------------------===//
// ViewOpGraph Registration
//===----------------------------------------------------------------------===//

inline void registerViewOpGraph() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpGraphPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerViewOpGraphPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// Transforms Registration
//===----------------------------------------------------------------------===//

inline void registerTransformsPasses() {
  registerCSE();
  registerCanonicalizer();
  registerControlFlowSink();
  registerGenerateRuntimeVerification();
  registerInliner();
  registerLocationSnapshot();
  registerLoopInvariantCodeMotion();
  registerMem2Reg();
  registerPrintIRPass();
  registerPrintOpStats();
  registerSCCP();
  registerStripDebugInfo();
  registerSymbolDCE();
  registerSymbolPrivatize();
  registerTopologicalSort();
  registerViewOpGraph();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class CSEBase : public ::mlir::OperationPass<> {
public:
  using Base = CSEBase;

  CSEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CSEBase(const CSEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cse");
  }
  ::llvm::StringRef getArgument() const override { return "cse"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate common sub-expressions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSE");
  }
  ::llvm::StringRef getName() const override { return "CSE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CSEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numCSE{this, "num-cse'd", "Number of operations CSE'd"};
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of operations DCE'd"};
};

template <typename DerivedT>
class CanonicalizerBase : public ::mlir::OperationPass<> {
public:
  using Base = CanonicalizerBase;

  CanonicalizerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CanonicalizerBase(const CanonicalizerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("canonicalize");
  }
  ::llvm::StringRef getArgument() const override { return "canonicalize"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Canonicalizer");
  }
  ::llvm::StringRef getName() const override { return "Canonicalizer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CanonicalizerBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> topDownProcessingEnabled{*this, "top-down", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Perform control flow optimizations to the region tree"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int64_t> maxIterations{*this, "max-iterations", ::llvm::cl::desc("Max. iterations between applying patterns / simplifying regions"), ::llvm::cl::init(10)};
  ::mlir::Pass::Option<int64_t> maxNumRewrites{*this, "max-num-rewrites", ::llvm::cl::desc("Max. number of pattern rewrites within an iteration"), ::llvm::cl::init(-1)};
  ::mlir::Pass::Option<bool> testConvergence{*this, "test-convergence", ::llvm::cl::desc("Test only: Fail pass on non-convergence to detect cyclic pattern"), ::llvm::cl::init(false)};
  ::mlir::Pass::ListOption<std::string> disabledPatterns{*this, "disable-patterns", ::llvm::cl::desc("Labels of patterns that should be filtered out during application")};
  ::mlir::Pass::ListOption<std::string> enabledPatterns{*this, "enable-patterns", ::llvm::cl::desc("Labels of patterns that should be used during application, all other patterns are filtered out")};
};

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("control-flow-sink");
  }
  ::llvm::StringRef getArgument() const override { return "control-flow-sink"; }

  ::llvm::StringRef getDescription() const override { return "Sink operations into conditional blocks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ControlFlowSinkBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numSunk{this, "num-sunk", "Number of operations sunk"};
};

template <typename DerivedT>
class GenerateRuntimeVerificationBase : public ::mlir::OperationPass<> {
public:
  using Base = GenerateRuntimeVerificationBase;

  GenerateRuntimeVerificationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  GenerateRuntimeVerificationBase(const GenerateRuntimeVerificationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("generate-runtime-verification");
  }
  ::llvm::StringRef getArgument() const override { return "generate-runtime-verification"; }

  ::llvm::StringRef getDescription() const override { return "Generate additional runtime op verification checks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GenerateRuntimeVerification");
  }
  ::llvm::StringRef getName() const override { return "GenerateRuntimeVerification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GenerateRuntimeVerificationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class InlinerBase : public ::mlir::OperationPass<> {
public:
  using Base = InlinerBase;

  InlinerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InlinerBase(const InlinerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("inline");
  }
  ::llvm::StringRef getArgument() const override { return "inline"; }

  ::llvm::StringRef getDescription() const override { return "Inline function calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Inliner");
  }
  ::llvm::StringRef getName() const override { return "Inliner"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InlinerBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> defaultPipelineStr{*this, "default-pipeline", ::llvm::cl::desc("The default optimizer pipeline used for callables"), ::llvm::cl::init("canonicalize")};
  ::mlir::Pass::ListOption<OpPassManager> opPipelineList{*this, "op-pipelines", ::llvm::cl::desc("Callable operation specific optimizer pipelines (in the form of `dialect.op(pipeline)`)")};
  ::mlir::Pass::Option<unsigned> maxInliningIterations{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations when inlining within an SCC"), ::llvm::cl::init(4)};
};

template <typename DerivedT>
class LocationSnapshotBase : public ::mlir::OperationPass<> {
public:
  using Base = LocationSnapshotBase;

  LocationSnapshotBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LocationSnapshotBase(const LocationSnapshotBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("snapshot-op-locations");
  }
  ::llvm::StringRef getArgument() const override { return "snapshot-op-locations"; }

  ::llvm::StringRef getDescription() const override { return "Generate new locations from the current IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LocationSnapshot");
  }
  ::llvm::StringRef getName() const override { return "LocationSnapshot"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LocationSnapshotBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> fileName{*this, "filename", ::llvm::cl::desc("The filename to print the generated IR")};
  ::mlir::Pass::Option<std::string> tag{*this, "tag", ::llvm::cl::desc("A tag to use when fusing the new locations with the original. If unset, the locations are replaced.")};
};

template <typename DerivedT>
class LoopInvariantCodeMotionBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantCodeMotionBase;

  LoopInvariantCodeMotionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantCodeMotionBase(const LoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopInvariantCodeMotionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class Mem2RegBase : public ::mlir::OperationPass<> {
public:
  using Base = Mem2RegBase;

  Mem2RegBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  Mem2RegBase(const Mem2RegBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mem2reg");
  }
  ::llvm::StringRef getArgument() const override { return "mem2reg"; }

  ::llvm::StringRef getDescription() const override { return "Promotes memory slots into values."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Mem2Reg");
  }
  ::llvm::StringRef getName() const override { return "Mem2Reg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(Mem2RegBase<DerivedT>)

protected:
};

template <typename DerivedT>
class PrintIRPassBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintIRPassBase;

  PrintIRPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintIRPassBase(const PrintIRPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-ir");
  }
  ::llvm::StringRef getArgument() const override { return "print-ir"; }

  ::llvm::StringRef getDescription() const override { return "Print IR on the debug stream"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintIRPass");
  }
  ::llvm::StringRef getName() const override { return "PrintIRPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintIRPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> label{*this, "label", ::llvm::cl::desc("Label")};
};

template <typename DerivedT>
class PrintOpStatsBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintOpStatsBase;

  PrintOpStatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintOpStatsBase(const PrintOpStatsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-op-stats");
  }
  ::llvm::StringRef getArgument() const override { return "print-op-stats"; }

  ::llvm::StringRef getDescription() const override { return "Print statistics of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintOpStats");
  }
  ::llvm::StringRef getName() const override { return "PrintOpStats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintOpStatsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> printAsJSON{*this, "json", ::llvm::cl::desc("print the stats as JSON"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class SCCPBase : public ::mlir::OperationPass<> {
public:
  using Base = SCCPBase;

  SCCPBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCCPBase(const SCCPBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sccp");
  }
  ::llvm::StringRef getArgument() const override { return "sccp"; }

  ::llvm::StringRef getDescription() const override { return "Sparse Conditional Constant Propagation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCCP");
  }
  ::llvm::StringRef getName() const override { return "SCCP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCCPBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StripDebugInfoBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDebugInfoBase;

  StripDebugInfoBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDebugInfoBase(const StripDebugInfoBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("strip-debuginfo");
  }
  ::llvm::StringRef getArgument() const override { return "strip-debuginfo"; }

  ::llvm::StringRef getDescription() const override { return "Strip debug info from all operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDebugInfo");
  }
  ::llvm::StringRef getName() const override { return "StripDebugInfo"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripDebugInfoBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SymbolDCEBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolDCEBase;

  SymbolDCEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolDCEBase(const SymbolDCEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-dce");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-dce"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate dead symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolDCE");
  }
  ::llvm::StringRef getName() const override { return "SymbolDCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolDCEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of symbols DCE'd"};
};

template <typename DerivedT>
class SymbolPrivatizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolPrivatizeBase;

  SymbolPrivatizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolPrivatizeBase(const SymbolPrivatizeBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-privatize");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-privatize"; }

  ::llvm::StringRef getDescription() const override { return "Mark symbols private"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolPrivatize");
  }
  ::llvm::StringRef getName() const override { return "SymbolPrivatize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolPrivatizeBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> exclude{*this, "exclude", ::llvm::cl::desc("Comma separated list of symbols that should not be marked private")};
};

template <typename DerivedT>
class TopologicalSortBase : public ::mlir::OperationPass<> {
public:
  using Base = TopologicalSortBase;

  TopologicalSortBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TopologicalSortBase(const TopologicalSortBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("topological-sort");
  }
  ::llvm::StringRef getArgument() const override { return "topological-sort"; }

  ::llvm::StringRef getDescription() const override { return "Sort regions without SSA dominance in topological order"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TopologicalSort");
  }
  ::llvm::StringRef getName() const override { return "TopologicalSort"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TopologicalSortBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ViewOpGraphBase : public ::mlir::OperationPass<> {
public:
  using Base = ViewOpGraphBase;

  ViewOpGraphBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ViewOpGraphBase(const ViewOpGraphBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("view-op-graph");
  }
  ::llvm::StringRef getArgument() const override { return "view-op-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print Graphviz visualization of an operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ViewOpGraph");
  }
  ::llvm::StringRef getName() const override { return "ViewOpGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ViewOpGraphBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> maxLabelLen{*this, "max-label-len", ::llvm::cl::desc("Limit attribute/type length to number of chars"), ::llvm::cl::init(20)};
  ::mlir::Pass::Option<bool> printAttrs{*this, "print-attrs", ::llvm::cl::desc("Print attributes of operations"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printControlFlowEdges{*this, "print-control-flow-edges", ::llvm::cl::desc("Print control flow edges"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printDataFlowEdges{*this, "print-data-flow-edges", ::llvm::cl::desc("Print data flow edges"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printResultTypes{*this, "print-result-types", ::llvm::cl::desc("Print result types of operations"), ::llvm::cl::init(true)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
