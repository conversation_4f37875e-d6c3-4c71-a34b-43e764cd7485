/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Interface Definitions                                                      *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/// Returns mapping as an integer from the attribute.
int64_t mlir::DeviceMappingAttrInterface::getMappingId() const {
      return getImpl()->getMappingId(getImpl(), *this);
  }
