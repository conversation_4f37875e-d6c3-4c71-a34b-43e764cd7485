/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_EXPANDOPS
#define GEN_PASS_DECL_EXPANDSTRIDEDMETADATA
#define GEN_PASS_DECL_FOLDMEMREFALIASOPS
#define GEN_PASS_DECL_MEMREFEMULATEWIDEINT
#define GEN_PASS_DECL_NORMALIZEMEMREFS
#define GEN_PASS_DECL_RESOLVERANKEDSHAPETYPERESULTDIMS
#define GEN_PASS_DECL_RESOLVESHAPEDTYPERESULTDIMS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ExpandOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EXPANDOPS
#undef GEN_PASS_DECL_EXPANDOPS
#endif // GEN_PASS_DECL_EXPANDOPS
#ifdef GEN_PASS_DEF_EXPANDOPS
namespace impl {

template <typename DerivedT>
class ExpandOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = ExpandOpsBase;

  ExpandOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ExpandOpsBase(const ExpandOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("memref-expand");
  }
  ::llvm::StringRef getArgument() const override { return "memref-expand"; }

  ::llvm::StringRef getDescription() const override { return "Legalize memref operations to be convertible to LLVM."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExpandOps");
  }
  ::llvm::StringRef getName() const override { return "ExpandOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExpandOpsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EXPANDOPS
#endif // GEN_PASS_DEF_EXPANDOPS

//===----------------------------------------------------------------------===//
// ExpandStridedMetadata
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EXPANDSTRIDEDMETADATA
#undef GEN_PASS_DECL_EXPANDSTRIDEDMETADATA
#endif // GEN_PASS_DECL_EXPANDSTRIDEDMETADATA
#ifdef GEN_PASS_DEF_EXPANDSTRIDEDMETADATA
namespace impl {

template <typename DerivedT>
class ExpandStridedMetadataBase : public ::mlir::OperationPass<> {
public:
  using Base = ExpandStridedMetadataBase;

  ExpandStridedMetadataBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ExpandStridedMetadataBase(const ExpandStridedMetadataBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("expand-strided-metadata");
  }
  ::llvm::StringRef getArgument() const override { return "expand-strided-metadata"; }

  ::llvm::StringRef getDescription() const override { return "Expand memref operations into easier to analyze constructs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExpandStridedMetadata");
  }
  ::llvm::StringRef getName() const override { return "ExpandStridedMetadata"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExpandStridedMetadataBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EXPANDSTRIDEDMETADATA
#endif // GEN_PASS_DEF_EXPANDSTRIDEDMETADATA

//===----------------------------------------------------------------------===//
// FoldMemRefAliasOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FOLDMEMREFALIASOPS
#undef GEN_PASS_DECL_FOLDMEMREFALIASOPS
#endif // GEN_PASS_DECL_FOLDMEMREFALIASOPS
#ifdef GEN_PASS_DEF_FOLDMEMREFALIASOPS
namespace impl {

template <typename DerivedT>
class FoldMemRefAliasOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = FoldMemRefAliasOpsBase;

  FoldMemRefAliasOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  FoldMemRefAliasOpsBase(const FoldMemRefAliasOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("fold-memref-alias-ops");
  }
  ::llvm::StringRef getArgument() const override { return "fold-memref-alias-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fold memref alias ops into consumer load/store ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FoldMemRefAliasOps");
  }
  ::llvm::StringRef getName() const override { return "FoldMemRefAliasOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FoldMemRefAliasOpsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FOLDMEMREFALIASOPS
#endif // GEN_PASS_DEF_FOLDMEMREFALIASOPS

//===----------------------------------------------------------------------===//
// MemRefEmulateWideInt
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MEMREFEMULATEWIDEINT
struct MemRefEmulateWideIntOptions {
  unsigned widestIntSupported = 32;
};
std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt();
std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt(const MemRefEmulateWideIntOptions &options);
#undef GEN_PASS_DECL_MEMREFEMULATEWIDEINT
#endif // GEN_PASS_DECL_MEMREFEMULATEWIDEINT
#ifdef GEN_PASS_DEF_MEMREFEMULATEWIDEINT

namespace impl {
  std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt(const MemRefEmulateWideIntOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class MemRefEmulateWideIntBase : public ::mlir::OperationPass<> {
public:
  using Base = MemRefEmulateWideIntBase;

  MemRefEmulateWideIntBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  MemRefEmulateWideIntBase(const MemRefEmulateWideIntBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("memref-emulate-wide-int");
  }
  ::llvm::StringRef getArgument() const override { return "memref-emulate-wide-int"; }

  ::llvm::StringRef getDescription() const override { return "Emulate 2*N-bit integer operations using N-bit operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MemRefEmulateWideInt");
  }
  ::llvm::StringRef getName() const override { return "MemRefEmulateWideInt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MemRefEmulateWideIntBase<DerivedT>)

  MemRefEmulateWideIntBase(const MemRefEmulateWideIntOptions &options) : MemRefEmulateWideIntBase() {
    widestIntSupported = options.widestIntSupported;
  }
protected:
  ::mlir::Pass::Option<unsigned> widestIntSupported{*this, "widest-int-supported", ::llvm::cl::desc("Widest integer type supported by the target"), ::llvm::cl::init(32)};
private:

  friend std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt(const MemRefEmulateWideIntOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt() {
  return impl::createMemRefEmulateWideInt();
}

std::unique_ptr<::mlir::Pass> createMemRefEmulateWideInt(const MemRefEmulateWideIntOptions &options) {
  return impl::createMemRefEmulateWideInt(options);
}
#undef GEN_PASS_DEF_MEMREFEMULATEWIDEINT
#endif // GEN_PASS_DEF_MEMREFEMULATEWIDEINT

//===----------------------------------------------------------------------===//
// NormalizeMemRefs
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_NORMALIZEMEMREFS
#undef GEN_PASS_DECL_NORMALIZEMEMREFS
#endif // GEN_PASS_DECL_NORMALIZEMEMREFS
#ifdef GEN_PASS_DEF_NORMALIZEMEMREFS
namespace impl {

template <typename DerivedT>
class NormalizeMemRefsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = NormalizeMemRefsBase;

  NormalizeMemRefsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  NormalizeMemRefsBase(const NormalizeMemRefsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("normalize-memrefs");
  }
  ::llvm::StringRef getArgument() const override { return "normalize-memrefs"; }

  ::llvm::StringRef getDescription() const override { return "Normalize memrefs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NormalizeMemRefs");
  }
  ::llvm::StringRef getName() const override { return "NormalizeMemRefs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NormalizeMemRefsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_NORMALIZEMEMREFS
#endif // GEN_PASS_DEF_NORMALIZEMEMREFS

//===----------------------------------------------------------------------===//
// ResolveRankedShapeTypeResultDims
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RESOLVERANKEDSHAPETYPERESULTDIMS
#undef GEN_PASS_DECL_RESOLVERANKEDSHAPETYPERESULTDIMS
#endif // GEN_PASS_DECL_RESOLVERANKEDSHAPETYPERESULTDIMS
#ifdef GEN_PASS_DEF_RESOLVERANKEDSHAPETYPERESULTDIMS
namespace impl {

template <typename DerivedT>
class ResolveRankedShapeTypeResultDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = ResolveRankedShapeTypeResultDimsBase;

  ResolveRankedShapeTypeResultDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ResolveRankedShapeTypeResultDimsBase(const ResolveRankedShapeTypeResultDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("resolve-ranked-shaped-type-result-dims");
  }
  ::llvm::StringRef getArgument() const override { return "resolve-ranked-shaped-type-result-dims"; }

  ::llvm::StringRef getDescription() const override { return "Resolve memref.dim of result values of ranked shape type"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResolveRankedShapeTypeResultDims");
  }
  ::llvm::StringRef getName() const override { return "ResolveRankedShapeTypeResultDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResolveRankedShapeTypeResultDimsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_RESOLVERANKEDSHAPETYPERESULTDIMS
#endif // GEN_PASS_DEF_RESOLVERANKEDSHAPETYPERESULTDIMS

//===----------------------------------------------------------------------===//
// ResolveShapedTypeResultDims
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RESOLVESHAPEDTYPERESULTDIMS
#undef GEN_PASS_DECL_RESOLVESHAPEDTYPERESULTDIMS
#endif // GEN_PASS_DECL_RESOLVESHAPEDTYPERESULTDIMS
#ifdef GEN_PASS_DEF_RESOLVESHAPEDTYPERESULTDIMS
namespace impl {

template <typename DerivedT>
class ResolveShapedTypeResultDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = ResolveShapedTypeResultDimsBase;

  ResolveShapedTypeResultDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ResolveShapedTypeResultDimsBase(const ResolveShapedTypeResultDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("resolve-shaped-type-result-dims");
  }
  ::llvm::StringRef getArgument() const override { return "resolve-shaped-type-result-dims"; }

  ::llvm::StringRef getDescription() const override { return "Resolve memref.dim of result values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResolveShapedTypeResultDims");
  }
  ::llvm::StringRef getName() const override { return "ResolveShapedTypeResultDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResolveShapedTypeResultDimsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_RESOLVESHAPEDTYPERESULTDIMS
#endif // GEN_PASS_DEF_RESOLVESHAPEDTYPERESULTDIMS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ExpandOps Registration
//===----------------------------------------------------------------------===//

inline void registerExpandOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createExpandOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerExpandOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createExpandOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ExpandStridedMetadata Registration
//===----------------------------------------------------------------------===//

inline void registerExpandStridedMetadata() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createExpandStridedMetadataPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerExpandStridedMetadataPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createExpandStridedMetadataPass();
  });
}

//===----------------------------------------------------------------------===//
// FoldMemRefAliasOps Registration
//===----------------------------------------------------------------------===//

inline void registerFoldMemRefAliasOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createFoldMemRefAliasOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFoldMemRefAliasOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createFoldMemRefAliasOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// MemRefEmulateWideInt Registration
//===----------------------------------------------------------------------===//

inline void registerMemRefEmulateWideInt() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMemRefEmulateWideInt();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMemRefEmulateWideIntPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMemRefEmulateWideInt();
  });
}

//===----------------------------------------------------------------------===//
// NormalizeMemRefs Registration
//===----------------------------------------------------------------------===//

inline void registerNormalizeMemRefs() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createNormalizeMemRefsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerNormalizeMemRefsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createNormalizeMemRefsPass();
  });
}

//===----------------------------------------------------------------------===//
// ResolveRankedShapeTypeResultDims Registration
//===----------------------------------------------------------------------===//

inline void registerResolveRankedShapeTypeResultDims() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createResolveRankedShapeTypeResultDimsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerResolveRankedShapeTypeResultDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createResolveRankedShapeTypeResultDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// ResolveShapedTypeResultDims Registration
//===----------------------------------------------------------------------===//

inline void registerResolveShapedTypeResultDims() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createResolveShapedTypeResultDimsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerResolveShapedTypeResultDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createResolveShapedTypeResultDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// MemRef Registration
//===----------------------------------------------------------------------===//

inline void registerMemRefPasses() {
  registerExpandOps();
  registerExpandStridedMetadata();
  registerFoldMemRefAliasOps();
  registerMemRefEmulateWideInt();
  registerNormalizeMemRefs();
  registerResolveRankedShapeTypeResultDims();
  registerResolveShapedTypeResultDims();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ExpandOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = ExpandOpsBase;

  ExpandOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ExpandOpsBase(const ExpandOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("memref-expand");
  }
  ::llvm::StringRef getArgument() const override { return "memref-expand"; }

  ::llvm::StringRef getDescription() const override { return "Legalize memref operations to be convertible to LLVM."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExpandOps");
  }
  ::llvm::StringRef getName() const override { return "ExpandOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExpandOpsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ExpandStridedMetadataBase : public ::mlir::OperationPass<> {
public:
  using Base = ExpandStridedMetadataBase;

  ExpandStridedMetadataBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ExpandStridedMetadataBase(const ExpandStridedMetadataBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("expand-strided-metadata");
  }
  ::llvm::StringRef getArgument() const override { return "expand-strided-metadata"; }

  ::llvm::StringRef getDescription() const override { return "Expand memref operations into easier to analyze constructs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExpandStridedMetadata");
  }
  ::llvm::StringRef getName() const override { return "ExpandStridedMetadata"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExpandStridedMetadataBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FoldMemRefAliasOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = FoldMemRefAliasOpsBase;

  FoldMemRefAliasOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  FoldMemRefAliasOpsBase(const FoldMemRefAliasOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("fold-memref-alias-ops");
  }
  ::llvm::StringRef getArgument() const override { return "fold-memref-alias-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fold memref alias ops into consumer load/store ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FoldMemRefAliasOps");
  }
  ::llvm::StringRef getName() const override { return "FoldMemRefAliasOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FoldMemRefAliasOpsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class MemRefEmulateWideIntBase : public ::mlir::OperationPass<> {
public:
  using Base = MemRefEmulateWideIntBase;

  MemRefEmulateWideIntBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  MemRefEmulateWideIntBase(const MemRefEmulateWideIntBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("memref-emulate-wide-int");
  }
  ::llvm::StringRef getArgument() const override { return "memref-emulate-wide-int"; }

  ::llvm::StringRef getDescription() const override { return "Emulate 2*N-bit integer operations using N-bit operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MemRefEmulateWideInt");
  }
  ::llvm::StringRef getName() const override { return "MemRefEmulateWideInt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MemRefEmulateWideIntBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> widestIntSupported{*this, "widest-int-supported", ::llvm::cl::desc("Widest integer type supported by the target"), ::llvm::cl::init(32)};
};

template <typename DerivedT>
class NormalizeMemRefsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = NormalizeMemRefsBase;

  NormalizeMemRefsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  NormalizeMemRefsBase(const NormalizeMemRefsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("normalize-memrefs");
  }
  ::llvm::StringRef getArgument() const override { return "normalize-memrefs"; }

  ::llvm::StringRef getDescription() const override { return "Normalize memrefs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NormalizeMemRefs");
  }
  ::llvm::StringRef getName() const override { return "NormalizeMemRefs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NormalizeMemRefsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ResolveRankedShapeTypeResultDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = ResolveRankedShapeTypeResultDimsBase;

  ResolveRankedShapeTypeResultDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ResolveRankedShapeTypeResultDimsBase(const ResolveRankedShapeTypeResultDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("resolve-ranked-shaped-type-result-dims");
  }
  ::llvm::StringRef getArgument() const override { return "resolve-ranked-shaped-type-result-dims"; }

  ::llvm::StringRef getDescription() const override { return "Resolve memref.dim of result values of ranked shape type"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResolveRankedShapeTypeResultDims");
  }
  ::llvm::StringRef getName() const override { return "ResolveRankedShapeTypeResultDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResolveRankedShapeTypeResultDimsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ResolveShapedTypeResultDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = ResolveShapedTypeResultDimsBase;

  ResolveShapedTypeResultDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ResolveShapedTypeResultDimsBase(const ResolveShapedTypeResultDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("resolve-shaped-type-result-dims");
  }
  ::llvm::StringRef getArgument() const override { return "resolve-shaped-type-result-dims"; }

  ::llvm::StringRef getDescription() const override { return "Resolve memref.dim of result values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResolveShapedTypeResultDims");
  }
  ::llvm::StringRef getName() const override { return "ResolveShapedTypeResultDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResolveShapedTypeResultDimsBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
