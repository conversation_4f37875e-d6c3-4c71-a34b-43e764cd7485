import chess
import random

def print_board(board):
    print(board)

def get_random_move(board):
    legal_moves = list(board.legal_moves)
    if legal_moves:
        return random.choice(legal_moves)
    return None

def main():
    board = chess.Board()
    
    print("Welcome to Simple Chess Bot! You are playing as <PERSON>.")
    while not board.is_game_over():
        print_board(board)

        # Player's move (input)
        if board.turn == chess.WHITE:
            move = input("Your move (e.g., e2 e4): ")
            try:
                move = chess.Move.from_uci(move.replace(" ", ""))
                if move in board.legal_moves:
                    board.push(move)
                else:
                    print("Illegal move. Try again.")
                    continue
            except ValueError:
                print("Invalid input. Please use the format e2 e4.")
                continue
        else:
            # <PERSON><PERSON>'s move
            print("<PERSON><PERSON> is thinking...")
            move = get_random_move(board)
            if move:
                board.push(move)
                print(f"<PERSON><PERSON> plays: {move}")
            else:
                print("No legal moves! <PERSON><PERSON> resigns.")
                break

    print_board(board)
    print("Game Over!")
    print("Result: ", board.result())

if __name__ == "__main__":
    main()
