# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/tensor_description.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import allocation_description_pb2 as tensorboard_dot_compat_dot_proto_dot_allocation__description__pb2
from tensorboard.compat.proto import tensor_shape_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__shape__pb2
from tensorboard.compat.proto import types_pb2 as tensorboard_dot_compat_dot_proto_dot_types__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1tensorboard/compat/proto/tensor_description.proto\x12\x0btensorboard\x1a\x35tensorboard/compat/proto/allocation_description.proto\x1a+tensorboard/compat/proto/tensor_shape.proto\x1a$tensorboard/compat/proto/types.proto\"\xab\x01\n\x11TensorDescription\x12$\n\x05\x64type\x18\x01 \x01(\x0e\x32\x15.tensorboard.DataType\x12,\n\x05shape\x18\x02 \x01(\x0b\x32\x1d.tensorboard.TensorShapeProto\x12\x42\n\x16\x61llocation_description\x18\x04 \x01(\x0b\x32\".tensorboard.AllocationDescriptionB\x93\x01\n\x18org.tensorflow.frameworkB\x17TensorDescriptionProtosP\x01ZYgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto\xf8\x01\x01\x62\x06proto3')



_TENSORDESCRIPTION = DESCRIPTOR.message_types_by_name['TensorDescription']
TensorDescription = _reflection.GeneratedProtocolMessageType('TensorDescription', (_message.Message,), {
  'DESCRIPTOR' : _TENSORDESCRIPTION,
  '__module__' : 'tensorboard.compat.proto.tensor_description_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.TensorDescription)
  })
_sym_db.RegisterMessage(TensorDescription)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\027TensorDescriptionProtosP\001ZYgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto\370\001\001'
  _TENSORDESCRIPTION._serialized_start=205
  _TENSORDESCRIPTION._serialized_end=376
# @@protoc_insertion_point(module_scope)
