/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Enum Utility Declarations                                                  *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace spirv {
// valid SPIR-V version
enum class Version : uint32_t {
  V_1_0 = 0,
  V_1_1 = 1,
  V_1_2 = 2,
  V_1_3 = 3,
  V_1_4 = 4,
  V_1_5 = 5,
  V_1_6 = 6,
};

::std::optional<Version> symbolizeVersion(uint32_t);
::llvm::StringRef stringifyVersion(Version);
::std::optional<Version> symbolizeVersion(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForVersion() {
  return 6;
}


inline ::llvm::StringRef stringifyEnum(Version enumValue) {
  return stringifyVersion(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Version> symbolizeEnum<Version>(::llvm::StringRef str) {
  return symbolizeVersion(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Version, ::mlir::spirv::Version> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Version> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V version");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Version> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Version>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V version specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Version value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Version> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Version getEmptyKey() {
    return static_cast<::mlir::spirv::Version>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Version getTombstoneKey() {
    return static_cast<::mlir::spirv::Version>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Version &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Version &lhs, const ::mlir::spirv::Version &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V device types
enum class DeviceType : uint32_t {
  Other = 3,
  IntegratedGPU = 2,
  DiscreteGPU = 1,
  CPU = 0,
  Unknown = 4294967295,
};

::std::optional<DeviceType> symbolizeDeviceType(uint32_t);
::llvm::StringRef stringifyDeviceType(DeviceType);
::std::optional<DeviceType> symbolizeDeviceType(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForDeviceType() {
  return 4294967295;
}


inline ::llvm::StringRef stringifyEnum(DeviceType enumValue) {
  return stringifyDeviceType(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<DeviceType> symbolizeEnum<DeviceType>(::llvm::StringRef str) {
  return symbolizeDeviceType(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::DeviceType, ::mlir::spirv::DeviceType> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::DeviceType> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V device types");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::DeviceType> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::DeviceType>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V device types specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::DeviceType value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::DeviceType> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::DeviceType getEmptyKey() {
    return static_cast<::mlir::spirv::DeviceType>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::DeviceType getTombstoneKey() {
    return static_cast<::mlir::spirv::DeviceType>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::DeviceType &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::DeviceType &lhs, const ::mlir::spirv::DeviceType &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// recognized SPIR-V vendor strings
enum class Vendor : uint32_t {
  AMD = 0,
  Apple = 1,
  ARM = 2,
  Imagination = 3,
  Intel = 4,
  NVIDIA = 5,
  Qualcomm = 6,
  SwiftShader = 7,
  Unknown = 4294967295,
};

::std::optional<Vendor> symbolizeVendor(uint32_t);
::llvm::StringRef stringifyVendor(Vendor);
::std::optional<Vendor> symbolizeVendor(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForVendor() {
  return 4294967295;
}


inline ::llvm::StringRef stringifyEnum(Vendor enumValue) {
  return stringifyVendor(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Vendor> symbolizeEnum<Vendor>(::llvm::StringRef str) {
  return symbolizeVendor(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Vendor, ::mlir::spirv::Vendor> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Vendor> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for recognized SPIR-V vendor strings");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Vendor> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Vendor>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid recognized SPIR-V vendor strings specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Vendor value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Vendor> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Vendor getEmptyKey() {
    return static_cast<::mlir::spirv::Vendor>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Vendor getTombstoneKey() {
    return static_cast<::mlir::spirv::Vendor>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Vendor &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Vendor &lhs, const ::mlir::spirv::Vendor &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// recognized SPIR-V client APIs
enum class ClientAPI : uint32_t {
  Metal = 0,
  OpenCL = 1,
  Vulkan = 2,
  WebGPU = 3,
  Unknown = 4294967295,
};

::std::optional<ClientAPI> symbolizeClientAPI(uint32_t);
::llvm::StringRef stringifyClientAPI(ClientAPI);
::std::optional<ClientAPI> symbolizeClientAPI(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForClientAPI() {
  return 4294967295;
}


inline ::llvm::StringRef stringifyEnum(ClientAPI enumValue) {
  return stringifyClientAPI(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ClientAPI> symbolizeEnum<ClientAPI>(::llvm::StringRef str) {
  return symbolizeClientAPI(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ClientAPI, ::mlir::spirv::ClientAPI> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ClientAPI> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for recognized SPIR-V client APIs");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ClientAPI> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ClientAPI>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid recognized SPIR-V client APIs specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ClientAPI value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ClientAPI> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ClientAPI getEmptyKey() {
    return static_cast<::mlir::spirv::ClientAPI>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ClientAPI getTombstoneKey() {
    return static_cast<::mlir::spirv::ClientAPI>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ClientAPI &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ClientAPI &lhs, const ::mlir::spirv::ClientAPI &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// supported SPIR-V extensions
enum class Extension : uint32_t {
  SPV_KHR_16bit_storage = 0,
  SPV_KHR_8bit_storage = 1,
  SPV_KHR_device_group = 2,
  SPV_KHR_float_controls = 3,
  SPV_KHR_physical_storage_buffer = 4,
  SPV_KHR_multiview = 5,
  SPV_KHR_no_integer_wrap_decoration = 6,
  SPV_KHR_post_depth_coverage = 7,
  SPV_KHR_shader_atomic_counter_ops = 8,
  SPV_KHR_shader_ballot = 9,
  SPV_KHR_shader_clock = 10,
  SPV_KHR_shader_draw_parameters = 11,
  SPV_KHR_storage_buffer_storage_class = 12,
  SPV_KHR_subgroup_vote = 13,
  SPV_KHR_variable_pointers = 14,
  SPV_KHR_vulkan_memory_model = 15,
  SPV_KHR_expect_assume = 16,
  SPV_KHR_integer_dot_product = 17,
  SPV_KHR_bit_instructions = 18,
  SPV_KHR_fragment_shading_rate = 19,
  SPV_KHR_workgroup_memory_explicit_layout = 20,
  SPV_KHR_ray_query = 21,
  SPV_KHR_ray_tracing = 22,
  SPV_KHR_subgroup_uniform_control_flow = 23,
  SPV_KHR_linkonce_odr = 24,
  SPV_KHR_fragment_shader_barycentric = 25,
  SPV_KHR_ray_cull_mask = 26,
  SPV_KHR_uniform_group_instructions = 27,
  SPV_KHR_subgroup_rotate = 28,
  SPV_KHR_non_semantic_info = 29,
  SPV_KHR_terminate_invocation = 30,
  SPV_EXT_demote_to_helper_invocation = 1000,
  SPV_EXT_descriptor_indexing = 1001,
  SPV_EXT_fragment_fully_covered = 1002,
  SPV_EXT_fragment_invocation_density = 1003,
  SPV_EXT_fragment_shader_interlock = 1004,
  SPV_EXT_physical_storage_buffer = 1005,
  SPV_EXT_shader_stencil_export = 1006,
  SPV_EXT_shader_viewport_index_layer = 1007,
  SPV_EXT_shader_atomic_float_add = 1008,
  SPV_EXT_shader_atomic_float_min_max = 1009,
  SPV_EXT_shader_image_int64 = 1010,
  SPV_EXT_shader_atomic_float16_add = 1011,
  SPV_AMD_gpu_shader_half_float_fetch = 2000,
  SPV_AMD_shader_ballot = 2001,
  SPV_AMD_shader_explicit_vertex_parameter = 2002,
  SPV_AMD_shader_fragment_mask = 2003,
  SPV_AMD_shader_image_load_store_lod = 2004,
  SPV_AMD_texture_gather_bias_lod = 2005,
  SPV_AMD_shader_early_and_late_fragment_tests = 2006,
  SPV_GOOGLE_decorate_string = 3000,
  SPV_GOOGLE_hlsl_functionality1 = 3001,
  SPV_GOOGLE_user_type = 3002,
  SPV_INTEL_device_side_avc_motion_estimation = 4000,
  SPV_INTEL_media_block_io = 4001,
  SPV_INTEL_shader_integer_functions2 = 4002,
  SPV_INTEL_subgroups = 4003,
  SPV_INTEL_vector_compute = 4007,
  SPV_INTEL_float_controls2 = 4004,
  SPV_INTEL_function_pointers = 4005,
  SPV_INTEL_inline_assembly = 4006,
  SPV_INTEL_variable_length_array = 4008,
  SPV_INTEL_fpga_memory_attributes = 4009,
  SPV_INTEL_unstructured_loop_controls = 4012,
  SPV_INTEL_fpga_loop_controls = 4013,
  SPV_INTEL_arbitrary_precision_integers = 4010,
  SPV_INTEL_arbitrary_precision_floating_point = 4011,
  SPV_INTEL_kernel_attributes = 4014,
  SPV_INTEL_fpga_memory_accesses = 4015,
  SPV_INTEL_fpga_cluster_attributes = 4016,
  SPV_INTEL_loop_fuse = 4017,
  SPV_INTEL_fpga_buffer_location = 4018,
  SPV_INTEL_arbitrary_precision_fixed_point = 4019,
  SPV_INTEL_usm_storage_classes = 4020,
  SPV_INTEL_io_pipes = 4021,
  SPV_INTEL_blocking_pipes = 4022,
  SPV_INTEL_fpga_reg = 4023,
  SPV_INTEL_long_constant_composite = 4024,
  SPV_INTEL_optnone = 4025,
  SPV_INTEL_debug_module = 4026,
  SPV_INTEL_fp_fast_math_mode = 4027,
  SPV_INTEL_memory_access_aliasing = 4028,
  SPV_INTEL_split_barrier = 4029,
  SPV_INTEL_joint_matrix = 4030,
  SPV_INTEL_bfloat16_conversion = 4031,
  SPV_NV_compute_shader_derivatives = 5000,
  SPV_NV_cooperative_matrix = 5001,
  SPV_NV_fragment_shader_barycentric = 5002,
  SPV_NV_geometry_shader_passthrough = 5003,
  SPV_NV_mesh_shader = 5004,
  SPV_NV_ray_tracing = 5005,
  SPV_NV_sample_mask_override_coverage = 5006,
  SPV_NV_shader_image_footprint = 5007,
  SPV_NV_shader_sm_builtins = 5008,
  SPV_NV_shader_subgroup_partitioned = 5009,
  SPV_NV_shading_rate = 5010,
  SPV_NV_stereo_view_rendering = 5011,
  SPV_NV_viewport_array2 = 5012,
  SPV_NV_bindless_texture = 5013,
  SPV_NV_ray_tracing_motion_blur = 5014,
  SPV_NVX_multiview_per_view_attributes = 5015,
};

::std::optional<Extension> symbolizeExtension(uint32_t);
::llvm::StringRef stringifyExtension(Extension);
::std::optional<Extension> symbolizeExtension(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForExtension() {
  return 5015;
}


inline ::llvm::StringRef stringifyEnum(Extension enumValue) {
  return stringifyExtension(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Extension> symbolizeEnum<Extension>(::llvm::StringRef str) {
  return symbolizeExtension(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Extension, ::mlir::spirv::Extension> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Extension> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for supported SPIR-V extensions");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Extension> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Extension>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid supported SPIR-V extensions specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Extension value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Extension> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Extension getEmptyKey() {
    return static_cast<::mlir::spirv::Extension>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Extension getTombstoneKey() {
    return static_cast<::mlir::spirv::Extension>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Extension &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Extension &lhs, const ::mlir::spirv::Extension &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Capability
enum class Capability : uint32_t {
  Matrix = 0,
  Addresses = 4,
  Linkage = 5,
  Kernel = 6,
  Float16 = 9,
  Float64 = 10,
  Int64 = 11,
  Groups = 18,
  Int16 = 22,
  Int8 = 39,
  Sampled1D = 43,
  SampledBuffer = 46,
  GroupNonUniform = 61,
  ShaderLayer = 69,
  ShaderViewportIndex = 70,
  UniformDecoration = 71,
  SubgroupBallotKHR = 4423,
  SubgroupVoteKHR = 4431,
  StorageBuffer16BitAccess = 4433,
  StoragePushConstant16 = 4435,
  StorageInputOutput16 = 4436,
  DeviceGroup = 4437,
  AtomicStorageOps = 4445,
  SampleMaskPostDepthCoverage = 4447,
  StorageBuffer8BitAccess = 4448,
  StoragePushConstant8 = 4450,
  DenormPreserve = 4464,
  DenormFlushToZero = 4465,
  SignedZeroInfNanPreserve = 4466,
  RoundingModeRTE = 4467,
  RoundingModeRTZ = 4468,
  ImageFootprintNV = 5282,
  FragmentBarycentricKHR = 5284,
  ComputeDerivativeGroupQuadsNV = 5288,
  GroupNonUniformPartitionedNV = 5297,
  VulkanMemoryModel = 5345,
  VulkanMemoryModelDeviceScope = 5346,
  ComputeDerivativeGroupLinearNV = 5350,
  BindlessTextureNV = 5390,
  SubgroupShuffleINTEL = 5568,
  SubgroupBufferBlockIOINTEL = 5569,
  SubgroupImageBlockIOINTEL = 5570,
  SubgroupImageMediaBlockIOINTEL = 5579,
  RoundToInfinityINTEL = 5582,
  FloatingPointModeINTEL = 5583,
  FunctionPointersINTEL = 5603,
  IndirectReferencesINTEL = 5604,
  AsmINTEL = 5606,
  AtomicFloat32MinMaxEXT = 5612,
  AtomicFloat64MinMaxEXT = 5613,
  AtomicFloat16MinMaxEXT = 5616,
  VectorAnyINTEL = 5619,
  ExpectAssumeKHR = 5629,
  SubgroupAvcMotionEstimationINTEL = 5696,
  SubgroupAvcMotionEstimationIntraINTEL = 5697,
  SubgroupAvcMotionEstimationChromaINTEL = 5698,
  VariableLengthArrayINTEL = 5817,
  FunctionFloatControlINTEL = 5821,
  FPGAMemoryAttributesINTEL = 5824,
  ArbitraryPrecisionIntegersINTEL = 5844,
  ArbitraryPrecisionFloatingPointINTEL = 5845,
  UnstructuredLoopControlsINTEL = 5886,
  FPGALoopControlsINTEL = 5888,
  KernelAttributesINTEL = 5892,
  FPGAKernelAttributesINTEL = 5897,
  FPGAMemoryAccessesINTEL = 5898,
  FPGAClusterAttributesINTEL = 5904,
  LoopFuseINTEL = 5906,
  MemoryAccessAliasingINTEL = 5910,
  FPGABufferLocationINTEL = 5920,
  ArbitraryPrecisionFixedPointINTEL = 5922,
  USMStorageClassesINTEL = 5935,
  IOPipesINTEL = 5943,
  BlockingPipesINTEL = 5945,
  FPGARegINTEL = 5948,
  DotProductInputAll = 6016,
  DotProductInput4x8BitPacked = 6018,
  DotProduct = 6019,
  RayCullMaskKHR = 6020,
  BitInstructions = 6025,
  AtomicFloat32AddEXT = 6033,
  AtomicFloat64AddEXT = 6034,
  LongConstantCompositeINTEL = 6089,
  OptNoneINTEL = 6094,
  AtomicFloat16AddEXT = 6095,
  DebugInfoModuleINTEL = 6114,
  SplitBarrierINTEL = 6141,
  GroupUniformArithmeticKHR = 6400,
  Shader = 1,
  Vector16 = 7,
  Float16Buffer = 8,
  Int64Atomics = 12,
  ImageBasic = 13,
  Pipes = 17,
  DeviceEnqueue = 19,
  LiteralSampler = 20,
  GenericPointer = 38,
  Image1D = 44,
  ImageBuffer = 47,
  NamedBarrier = 59,
  GroupNonUniformVote = 62,
  GroupNonUniformArithmetic = 63,
  GroupNonUniformBallot = 64,
  GroupNonUniformShuffle = 65,
  GroupNonUniformShuffleRelative = 66,
  GroupNonUniformClustered = 67,
  GroupNonUniformQuad = 68,
  StorageUniform16 = 4434,
  UniformAndStorageBuffer8BitAccess = 4449,
  UniformTexelBufferArrayDynamicIndexing = 5304,
  VectorComputeINTEL = 5617,
  FPFastMathModeINTEL = 5837,
  DotProductInput4x8Bit = 6017,
  GroupNonUniformRotateKHR = 6026,
  Geometry = 2,
  Tessellation = 3,
  ImageReadWrite = 14,
  ImageMipmap = 15,
  AtomicStorage = 21,
  ImageGatherExtended = 25,
  StorageImageMultisample = 27,
  UniformBufferArrayDynamicIndexing = 28,
  SampledImageArrayDynamicIndexing = 29,
  StorageBufferArrayDynamicIndexing = 30,
  StorageImageArrayDynamicIndexing = 31,
  ClipDistance = 32,
  CullDistance = 33,
  SampleRateShading = 35,
  SampledRect = 37,
  InputAttachment = 40,
  SparseResidency = 41,
  MinLod = 42,
  SampledCubeArray = 45,
  ImageMSArray = 48,
  StorageImageExtendedFormats = 49,
  ImageQuery = 50,
  DerivativeControl = 51,
  InterpolationFunction = 52,
  TransformFeedback = 53,
  StorageImageReadWithoutFormat = 55,
  StorageImageWriteWithoutFormat = 56,
  SubgroupDispatch = 58,
  PipeStorage = 60,
  FragmentShadingRateKHR = 4422,
  DrawParameters = 4427,
  WorkgroupMemoryExplicitLayoutKHR = 4428,
  WorkgroupMemoryExplicitLayout16BitAccessKHR = 4430,
  MultiView = 4439,
  VariablePointersStorageBuffer = 4441,
  RayQueryProvisionalKHR = 4471,
  RayQueryKHR = 4472,
  RayTracingKHR = 4479,
  Float16ImageAMD = 5008,
  ImageGatherBiasLodAMD = 5009,
  FragmentMaskAMD = 5010,
  StencilExportEXT = 5013,
  ImageReadWriteLodAMD = 5015,
  Int64ImageEXT = 5016,
  ShaderClockKHR = 5055,
  FragmentFullyCoveredEXT = 5265,
  MeshShadingNV = 5266,
  FragmentDensityEXT = 5291,
  ShaderNonUniform = 5301,
  RuntimeDescriptorArray = 5302,
  StorageTexelBufferArrayDynamicIndexing = 5305,
  RayTracingNV = 5340,
  RayTracingMotionBlurNV = 5341,
  PhysicalStorageBufferAddresses = 5347,
  RayTracingProvisionalKHR = 5353,
  CooperativeMatrixNV = 5357,
  FragmentShaderSampleInterlockEXT = 5363,
  FragmentShaderShadingRateInterlockEXT = 5372,
  ShaderSMBuiltinsNV = 5373,
  FragmentShaderPixelInterlockEXT = 5378,
  DemoteToHelperInvocation = 5379,
  IntegerFunctions2INTEL = 5584,
  TessellationPointSize = 23,
  GeometryPointSize = 24,
  ImageCubeArray = 34,
  ImageRect = 36,
  GeometryStreams = 54,
  MultiViewport = 57,
  WorkgroupMemoryExplicitLayout8BitAccessKHR = 4429,
  VariablePointers = 4442,
  RayTraversalPrimitiveCullingKHR = 4478,
  SampleMaskOverrideCoverageNV = 5249,
  GeometryShaderPassthroughNV = 5251,
  PerViewAttributesNV = 5260,
  InputAttachmentArrayDynamicIndexing = 5303,
  UniformBufferArrayNonUniformIndexing = 5306,
  SampledImageArrayNonUniformIndexing = 5307,
  StorageBufferArrayNonUniformIndexing = 5308,
  StorageImageArrayNonUniformIndexing = 5309,
  InputAttachmentArrayNonUniformIndexing = 5310,
  UniformTexelBufferArrayNonUniformIndexing = 5311,
  StorageTexelBufferArrayNonUniformIndexing = 5312,
  ShaderViewportIndexLayerEXT = 5254,
  ShaderViewportMaskNV = 5255,
  ShaderStereoViewNV = 5259,
  JointMatrixINTEL = 6118,
  Bfloat16ConversionINTEL = 6115,
};

::std::optional<Capability> symbolizeCapability(uint32_t);
::llvm::StringRef stringifyCapability(Capability);
::std::optional<Capability> symbolizeCapability(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForCapability() {
  return 6400;
}


inline ::llvm::StringRef stringifyEnum(Capability enumValue) {
  return stringifyCapability(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Capability> symbolizeEnum<Capability>(::llvm::StringRef str) {
  return symbolizeCapability(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Capability, ::mlir::spirv::Capability> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Capability> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Capability");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Capability> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Capability>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Capability specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Capability value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Capability> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Capability getEmptyKey() {
    return static_cast<::mlir::spirv::Capability>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Capability getTombstoneKey() {
    return static_cast<::mlir::spirv::Capability>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Capability &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Capability &lhs, const ::mlir::spirv::Capability &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V AddressingModel
enum class AddressingModel : uint32_t {
  Logical = 0,
  Physical32 = 1,
  Physical64 = 2,
  PhysicalStorageBuffer64 = 5348,
};

::std::optional<AddressingModel> symbolizeAddressingModel(uint32_t);
::llvm::StringRef stringifyAddressingModel(AddressingModel);
::std::optional<AddressingModel> symbolizeAddressingModel(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForAddressingModel() {
  return 5348;
}


inline ::llvm::StringRef stringifyEnum(AddressingModel enumValue) {
  return stringifyAddressingModel(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<AddressingModel> symbolizeEnum<AddressingModel>(::llvm::StringRef str) {
  return symbolizeAddressingModel(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::AddressingModel, ::mlir::spirv::AddressingModel> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::AddressingModel> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V AddressingModel");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::AddressingModel> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::AddressingModel>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V AddressingModel specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::AddressingModel value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::AddressingModel> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::AddressingModel getEmptyKey() {
    return static_cast<::mlir::spirv::AddressingModel>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::AddressingModel getTombstoneKey() {
    return static_cast<::mlir::spirv::AddressingModel>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::AddressingModel &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::AddressingModel &lhs, const ::mlir::spirv::AddressingModel &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V BuiltIn
enum class BuiltIn : uint32_t {
  Position = 0,
  PointSize = 1,
  ClipDistance = 3,
  CullDistance = 4,
  VertexId = 5,
  InstanceId = 6,
  PrimitiveId = 7,
  InvocationId = 8,
  Layer = 9,
  ViewportIndex = 10,
  TessLevelOuter = 11,
  TessLevelInner = 12,
  TessCoord = 13,
  PatchVertices = 14,
  FragCoord = 15,
  PointCoord = 16,
  FrontFacing = 17,
  SampleId = 18,
  SamplePosition = 19,
  SampleMask = 20,
  FragDepth = 22,
  HelperInvocation = 23,
  NumWorkgroups = 24,
  WorkgroupSize = 25,
  WorkgroupId = 26,
  LocalInvocationId = 27,
  GlobalInvocationId = 28,
  LocalInvocationIndex = 29,
  WorkDim = 30,
  GlobalSize = 31,
  EnqueuedWorkgroupSize = 32,
  GlobalOffset = 33,
  GlobalLinearId = 34,
  SubgroupSize = 36,
  SubgroupMaxSize = 37,
  NumSubgroups = 38,
  NumEnqueuedSubgroups = 39,
  SubgroupId = 40,
  SubgroupLocalInvocationId = 41,
  VertexIndex = 42,
  InstanceIndex = 43,
  SubgroupEqMask = 4416,
  SubgroupGeMask = 4417,
  SubgroupGtMask = 4418,
  SubgroupLeMask = 4419,
  SubgroupLtMask = 4420,
  BaseVertex = 4424,
  BaseInstance = 4425,
  DrawIndex = 4426,
  PrimitiveShadingRateKHR = 4432,
  DeviceIndex = 4438,
  ViewIndex = 4440,
  ShadingRateKHR = 4444,
  BaryCoordNoPerspAMD = 4992,
  BaryCoordNoPerspCentroidAMD = 4993,
  BaryCoordNoPerspSampleAMD = 4994,
  BaryCoordSmoothAMD = 4995,
  BaryCoordSmoothCentroidAMD = 4996,
  BaryCoordSmoothSampleAMD = 4997,
  BaryCoordPullModelAMD = 4998,
  FragStencilRefEXT = 5014,
  ViewportMaskNV = 5253,
  SecondaryPositionNV = 5257,
  SecondaryViewportMaskNV = 5258,
  PositionPerViewNV = 5261,
  ViewportMaskPerViewNV = 5262,
  FullyCoveredEXT = 5264,
  TaskCountNV = 5274,
  PrimitiveCountNV = 5275,
  PrimitiveIndicesNV = 5276,
  ClipDistancePerViewNV = 5277,
  CullDistancePerViewNV = 5278,
  LayerPerViewNV = 5279,
  MeshViewCountNV = 5280,
  MeshViewIndicesNV = 5281,
  BaryCoordKHR = 5286,
  BaryCoordNoPerspKHR = 5287,
  FragSizeEXT = 5292,
  FragInvocationCountEXT = 5293,
  LaunchIdKHR = 5319,
  LaunchSizeKHR = 5320,
  WorldRayOriginKHR = 5321,
  WorldRayDirectionKHR = 5322,
  ObjectRayOriginKHR = 5323,
  ObjectRayDirectionKHR = 5324,
  RayTminKHR = 5325,
  RayTmaxKHR = 5326,
  InstanceCustomIndexKHR = 5327,
  ObjectToWorldKHR = 5330,
  WorldToObjectKHR = 5331,
  HitTNV = 5332,
  HitKindKHR = 5333,
  CurrentRayTimeNV = 5334,
  IncomingRayFlagsKHR = 5351,
  RayGeometryIndexKHR = 5352,
  WarpsPerSMNV = 5374,
  SMCountNV = 5375,
  WarpIDNV = 5376,
  SMIDNV = 5377,
  CullMaskKHR = 6021,
};

::std::optional<BuiltIn> symbolizeBuiltIn(uint32_t);
::llvm::StringRef stringifyBuiltIn(BuiltIn);
::std::optional<BuiltIn> symbolizeBuiltIn(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForBuiltIn() {
  return 6021;
}


inline ::llvm::StringRef stringifyEnum(BuiltIn enumValue) {
  return stringifyBuiltIn(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<BuiltIn> symbolizeEnum<BuiltIn>(::llvm::StringRef str) {
  return symbolizeBuiltIn(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::BuiltIn, ::mlir::spirv::BuiltIn> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::BuiltIn> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V BuiltIn");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::BuiltIn> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::BuiltIn>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V BuiltIn specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::BuiltIn value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::BuiltIn> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::BuiltIn getEmptyKey() {
    return static_cast<::mlir::spirv::BuiltIn>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::BuiltIn getTombstoneKey() {
    return static_cast<::mlir::spirv::BuiltIn>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::BuiltIn &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::BuiltIn &lhs, const ::mlir::spirv::BuiltIn &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Decoration
enum class Decoration : uint32_t {
  RelaxedPrecision = 0,
  SpecId = 1,
  Block = 2,
  BufferBlock = 3,
  RowMajor = 4,
  ColMajor = 5,
  ArrayStride = 6,
  MatrixStride = 7,
  GLSLShared = 8,
  GLSLPacked = 9,
  CPacked = 10,
  BuiltIn = 11,
  NoPerspective = 13,
  Flat = 14,
  Patch = 15,
  Centroid = 16,
  Sample = 17,
  Invariant = 18,
  Restrict = 19,
  Aliased = 20,
  Volatile = 21,
  Constant = 22,
  Coherent = 23,
  NonWritable = 24,
  NonReadable = 25,
  Uniform = 26,
  UniformId = 27,
  SaturatedConversion = 28,
  Stream = 29,
  Location = 30,
  Component = 31,
  Index = 32,
  Binding = 33,
  DescriptorSet = 34,
  Offset = 35,
  XfbBuffer = 36,
  XfbStride = 37,
  FuncParamAttr = 38,
  FPRoundingMode = 39,
  FPFastMathMode = 40,
  LinkageAttributes = 41,
  NoContraction = 42,
  InputAttachmentIndex = 43,
  Alignment = 44,
  MaxByteOffset = 45,
  AlignmentId = 46,
  MaxByteOffsetId = 47,
  NoSignedWrap = 4469,
  NoUnsignedWrap = 4470,
  ExplicitInterpAMD = 4999,
  OverrideCoverageNV = 5248,
  PassthroughNV = 5250,
  ViewportRelativeNV = 5252,
  SecondaryViewportRelativeNV = 5256,
  PerPrimitiveNV = 5271,
  PerViewNV = 5272,
  PerTaskNV = 5273,
  PerVertexKHR = 5285,
  NonUniform = 5300,
  RestrictPointer = 5355,
  AliasedPointer = 5356,
  BindlessSamplerNV = 5398,
  BindlessImageNV = 5399,
  BoundSamplerNV = 5400,
  BoundImageNV = 5401,
  SIMTCallINTEL = 5599,
  ReferencedIndirectlyINTEL = 5602,
  ClobberINTEL = 5607,
  SideEffectsINTEL = 5608,
  VectorComputeVariableINTEL = 5624,
  FuncParamIOKindINTEL = 5625,
  VectorComputeFunctionINTEL = 5626,
  StackCallINTEL = 5627,
  GlobalVariableOffsetINTEL = 5628,
  CounterBuffer = 5634,
  UserSemantic = 5635,
  UserTypeGOOGLE = 5636,
  FunctionRoundingModeINTEL = 5822,
  FunctionDenormModeINTEL = 5823,
  RegisterINTEL = 5825,
  MemoryINTEL = 5826,
  NumbanksINTEL = 5827,
  BankwidthINTEL = 5828,
  MaxPrivateCopiesINTEL = 5829,
  SinglepumpINTEL = 5830,
  DoublepumpINTEL = 5831,
  MaxReplicatesINTEL = 5832,
  SimpleDualPortINTEL = 5833,
  MergeINTEL = 5834,
  BankBitsINTEL = 5835,
  ForcePow2DepthINTEL = 5836,
  BurstCoalesceINTEL = 5899,
  CacheSizeINTEL = 5900,
  DontStaticallyCoalesceINTEL = 5901,
  PrefetchINTEL = 5902,
  StallEnableINTEL = 5905,
  FuseLoopsInFunctionINTEL = 5907,
  AliasScopeINTEL = 5914,
  NoAliasINTEL = 5915,
  BufferLocationINTEL = 5921,
  IOPipeStorageINTEL = 5944,
  FunctionFloatingPointModeINTEL = 6080,
  SingleElementVectorINTEL = 6085,
  VectorComputeCallableFunctionINTEL = 6087,
  MediaBlockIOINTEL = 6140,
};

::std::optional<Decoration> symbolizeDecoration(uint32_t);
::llvm::StringRef stringifyDecoration(Decoration);
::std::optional<Decoration> symbolizeDecoration(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForDecoration() {
  return 6140;
}


inline ::llvm::StringRef stringifyEnum(Decoration enumValue) {
  return stringifyDecoration(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Decoration> symbolizeEnum<Decoration>(::llvm::StringRef str) {
  return symbolizeDecoration(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Decoration, ::mlir::spirv::Decoration> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Decoration> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Decoration");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Decoration> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Decoration>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Decoration specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Decoration value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Decoration> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Decoration getEmptyKey() {
    return static_cast<::mlir::spirv::Decoration>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Decoration getTombstoneKey() {
    return static_cast<::mlir::spirv::Decoration>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Decoration &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Decoration &lhs, const ::mlir::spirv::Decoration &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Dim
enum class Dim : uint32_t {
  Dim1D = 0,
  Dim2D = 1,
  Dim3D = 2,
  Cube = 3,
  Rect = 4,
  Buffer = 5,
  SubpassData = 6,
};

::std::optional<Dim> symbolizeDim(uint32_t);
::llvm::StringRef stringifyDim(Dim);
::std::optional<Dim> symbolizeDim(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForDim() {
  return 6;
}


inline ::llvm::StringRef stringifyEnum(Dim enumValue) {
  return stringifyDim(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Dim> symbolizeEnum<Dim>(::llvm::StringRef str) {
  return symbolizeDim(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Dim, ::mlir::spirv::Dim> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Dim> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Dim");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Dim> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Dim>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Dim specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Dim value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Dim> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Dim getEmptyKey() {
    return static_cast<::mlir::spirv::Dim>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Dim getTombstoneKey() {
    return static_cast<::mlir::spirv::Dim>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Dim &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Dim &lhs, const ::mlir::spirv::Dim &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V ExecutionMode
enum class ExecutionMode : uint32_t {
  Invocations = 0,
  SpacingEqual = 1,
  SpacingFractionalEven = 2,
  SpacingFractionalOdd = 3,
  VertexOrderCw = 4,
  VertexOrderCcw = 5,
  PixelCenterInteger = 6,
  OriginUpperLeft = 7,
  OriginLowerLeft = 8,
  EarlyFragmentTests = 9,
  PointMode = 10,
  Xfb = 11,
  DepthReplacing = 12,
  DepthGreater = 14,
  DepthLess = 15,
  DepthUnchanged = 16,
  LocalSize = 17,
  LocalSizeHint = 18,
  InputPoints = 19,
  InputLines = 20,
  InputLinesAdjacency = 21,
  Triangles = 22,
  InputTrianglesAdjacency = 23,
  Quads = 24,
  Isolines = 25,
  OutputVertices = 26,
  OutputPoints = 27,
  OutputLineStrip = 28,
  OutputTriangleStrip = 29,
  VecTypeHint = 30,
  ContractionOff = 31,
  Initializer = 33,
  Finalizer = 34,
  SubgroupSize = 35,
  SubgroupsPerWorkgroup = 36,
  SubgroupsPerWorkgroupId = 37,
  LocalSizeId = 38,
  LocalSizeHintId = 39,
  SubgroupUniformControlFlowKHR = 4421,
  PostDepthCoverage = 4446,
  DenormPreserve = 4459,
  DenormFlushToZero = 4460,
  SignedZeroInfNanPreserve = 4461,
  RoundingModeRTE = 4462,
  RoundingModeRTZ = 4463,
  EarlyAndLateFragmentTestsAMD = 5017,
  StencilRefReplacingEXT = 5027,
  StencilRefUnchangedFrontAMD = 5079,
  StencilRefGreaterFrontAMD = 5080,
  StencilRefLessFrontAMD = 5081,
  StencilRefUnchangedBackAMD = 5082,
  StencilRefGreaterBackAMD = 5083,
  StencilRefLessBackAMD = 5084,
  OutputLinesNV = 5269,
  OutputPrimitivesNV = 5270,
  DerivativeGroupQuadsNV = 5289,
  DerivativeGroupLinearNV = 5290,
  OutputTrianglesNV = 5298,
  PixelInterlockOrderedEXT = 5366,
  PixelInterlockUnorderedEXT = 5367,
  SampleInterlockOrderedEXT = 5368,
  SampleInterlockUnorderedEXT = 5369,
  ShadingRateInterlockOrderedEXT = 5370,
  ShadingRateInterlockUnorderedEXT = 5371,
  SharedLocalMemorySizeINTEL = 5618,
  RoundingModeRTPINTEL = 5620,
  RoundingModeRTNINTEL = 5621,
  FloatingPointModeALTINTEL = 5622,
  FloatingPointModeIEEEINTEL = 5623,
  MaxWorkgroupSizeINTEL = 5893,
  MaxWorkDimINTEL = 5894,
  NoGlobalOffsetINTEL = 5895,
  NumSIMDWorkitemsINTEL = 5896,
  SchedulerTargetFmaxMhzINTEL = 5903,
  StreamingInterfaceINTEL = 6154,
  NamedBarrierCountINTEL = 6417,
};

::std::optional<ExecutionMode> symbolizeExecutionMode(uint32_t);
::llvm::StringRef stringifyExecutionMode(ExecutionMode);
::std::optional<ExecutionMode> symbolizeExecutionMode(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForExecutionMode() {
  return 6417;
}


inline ::llvm::StringRef stringifyEnum(ExecutionMode enumValue) {
  return stringifyExecutionMode(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ExecutionMode> symbolizeEnum<ExecutionMode>(::llvm::StringRef str) {
  return symbolizeExecutionMode(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ExecutionMode, ::mlir::spirv::ExecutionMode> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ExecutionMode> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V ExecutionMode");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ExecutionMode> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ExecutionMode>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V ExecutionMode specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ExecutionMode value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ExecutionMode> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ExecutionMode getEmptyKey() {
    return static_cast<::mlir::spirv::ExecutionMode>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ExecutionMode getTombstoneKey() {
    return static_cast<::mlir::spirv::ExecutionMode>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ExecutionMode &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ExecutionMode &lhs, const ::mlir::spirv::ExecutionMode &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V ExecutionModel
enum class ExecutionModel : uint32_t {
  Vertex = 0,
  TessellationControl = 1,
  TessellationEvaluation = 2,
  Geometry = 3,
  Fragment = 4,
  GLCompute = 5,
  Kernel = 6,
  TaskNV = 5267,
  MeshNV = 5268,
  RayGenerationKHR = 5313,
  IntersectionKHR = 5314,
  AnyHitKHR = 5315,
  ClosestHitKHR = 5316,
  MissKHR = 5317,
  CallableKHR = 5318,
};

::std::optional<ExecutionModel> symbolizeExecutionModel(uint32_t);
::llvm::StringRef stringifyExecutionModel(ExecutionModel);
::std::optional<ExecutionModel> symbolizeExecutionModel(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForExecutionModel() {
  return 5318;
}


inline ::llvm::StringRef stringifyEnum(ExecutionModel enumValue) {
  return stringifyExecutionModel(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ExecutionModel> symbolizeEnum<ExecutionModel>(::llvm::StringRef str) {
  return symbolizeExecutionModel(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ExecutionModel, ::mlir::spirv::ExecutionModel> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ExecutionModel> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V ExecutionModel");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ExecutionModel> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ExecutionModel>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V ExecutionModel specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ExecutionModel value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ExecutionModel> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ExecutionModel getEmptyKey() {
    return static_cast<::mlir::spirv::ExecutionModel>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ExecutionModel getTombstoneKey() {
    return static_cast<::mlir::spirv::ExecutionModel>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ExecutionModel &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ExecutionModel &lhs, const ::mlir::spirv::ExecutionModel &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V FunctionControl
enum class FunctionControl : uint32_t {
  None = 0,
  Inline = 1,
  DontInline = 2,
  Pure = 4,
  Const = 8,
  OptNoneINTEL = 65536,
};

::std::optional<FunctionControl> symbolizeFunctionControl(uint32_t);
std::string stringifyFunctionControl(FunctionControl);
::std::optional<FunctionControl> symbolizeFunctionControl(::llvm::StringRef);

inline constexpr FunctionControl operator|(FunctionControl a, FunctionControl b) {
  return static_cast<FunctionControl>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}
inline constexpr FunctionControl operator&(FunctionControl a, FunctionControl b) {
  return static_cast<FunctionControl>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}
inline constexpr FunctionControl operator^(FunctionControl a, FunctionControl b) {
  return static_cast<FunctionControl>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}
inline constexpr FunctionControl operator~(FunctionControl bits) {
  // Ensure only bits that can be present in the enum are set
  return static_cast<FunctionControl>(~static_cast<uint32_t>(bits) & static_cast<uint32_t>(65551u));
}
inline constexpr bool bitEnumContainsAll(FunctionControl bits, FunctionControl bit) {
  return (bits & bit) == bit;
}
inline constexpr bool bitEnumContainsAny(FunctionControl bits, FunctionControl bit) {
  return (static_cast<uint32_t>(bits) & static_cast<uint32_t>(bit)) != 0;
}
inline constexpr FunctionControl bitEnumClear(FunctionControl bits, FunctionControl bit) {
  return bits & ~bit;
}
inline constexpr FunctionControl bitEnumSet(FunctionControl bits, FunctionControl bit, /*optional*/bool value=true) {
  return value ? (bits | bit) : bitEnumClear(bits, bit);
}
  
inline std::string stringifyEnum(FunctionControl enumValue) {
  return stringifyFunctionControl(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<FunctionControl> symbolizeEnum<FunctionControl>(::llvm::StringRef str) {
  return symbolizeFunctionControl(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::FunctionControl, ::mlir::spirv::FunctionControl> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::FunctionControl> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V FunctionControl");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::FunctionControl> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::FunctionControl>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V FunctionControl specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::FunctionControl value) {
  auto valueStr = stringifyEnum(value);
  auto underlyingValue = static_cast<std::make_unsigned_t<::mlir::spirv::FunctionControl>>(value);
  if (underlyingValue && !llvm::has_single_bit(underlyingValue))
    return p << '"' << valueStr << '"';
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::FunctionControl> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::FunctionControl getEmptyKey() {
    return static_cast<::mlir::spirv::FunctionControl>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::FunctionControl getTombstoneKey() {
    return static_cast<::mlir::spirv::FunctionControl>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::FunctionControl &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::FunctionControl &lhs, const ::mlir::spirv::FunctionControl &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V GroupOperation
enum class GroupOperation : uint32_t {
  Reduce = 0,
  InclusiveScan = 1,
  ExclusiveScan = 2,
  ClusteredReduce = 3,
  PartitionedReduceNV = 6,
  PartitionedInclusiveScanNV = 7,
  PartitionedExclusiveScanNV = 8,
};

::std::optional<GroupOperation> symbolizeGroupOperation(uint32_t);
::llvm::StringRef stringifyGroupOperation(GroupOperation);
::std::optional<GroupOperation> symbolizeGroupOperation(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForGroupOperation() {
  return 8;
}


inline ::llvm::StringRef stringifyEnum(GroupOperation enumValue) {
  return stringifyGroupOperation(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<GroupOperation> symbolizeEnum<GroupOperation>(::llvm::StringRef str) {
  return symbolizeGroupOperation(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::GroupOperation, ::mlir::spirv::GroupOperation> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::GroupOperation> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V GroupOperation");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::GroupOperation> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::GroupOperation>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V GroupOperation specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::GroupOperation value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::GroupOperation> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::GroupOperation getEmptyKey() {
    return static_cast<::mlir::spirv::GroupOperation>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::GroupOperation getTombstoneKey() {
    return static_cast<::mlir::spirv::GroupOperation>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::GroupOperation &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::GroupOperation &lhs, const ::mlir::spirv::GroupOperation &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V ImageFormat
enum class ImageFormat : uint32_t {
  Unknown = 0,
  Rgba32f = 1,
  Rgba16f = 2,
  R32f = 3,
  Rgba8 = 4,
  Rgba8Snorm = 5,
  Rg32f = 6,
  Rg16f = 7,
  R11fG11fB10f = 8,
  R16f = 9,
  Rgba16 = 10,
  Rgb10A2 = 11,
  Rg16 = 12,
  Rg8 = 13,
  R16 = 14,
  R8 = 15,
  Rgba16Snorm = 16,
  Rg16Snorm = 17,
  Rg8Snorm = 18,
  R16Snorm = 19,
  R8Snorm = 20,
  Rgba32i = 21,
  Rgba16i = 22,
  Rgba8i = 23,
  R32i = 24,
  Rg32i = 25,
  Rg16i = 26,
  Rg8i = 27,
  R16i = 28,
  R8i = 29,
  Rgba32ui = 30,
  Rgba16ui = 31,
  Rgba8ui = 32,
  R32ui = 33,
  Rgb10a2ui = 34,
  Rg32ui = 35,
  Rg16ui = 36,
  Rg8ui = 37,
  R16ui = 38,
  R8ui = 39,
  R64ui = 40,
  R64i = 41,
};

::std::optional<ImageFormat> symbolizeImageFormat(uint32_t);
::llvm::StringRef stringifyImageFormat(ImageFormat);
::std::optional<ImageFormat> symbolizeImageFormat(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForImageFormat() {
  return 41;
}


inline ::llvm::StringRef stringifyEnum(ImageFormat enumValue) {
  return stringifyImageFormat(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ImageFormat> symbolizeEnum<ImageFormat>(::llvm::StringRef str) {
  return symbolizeImageFormat(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ImageFormat, ::mlir::spirv::ImageFormat> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ImageFormat> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V ImageFormat");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ImageFormat> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ImageFormat>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V ImageFormat specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ImageFormat value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ImageFormat> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ImageFormat getEmptyKey() {
    return static_cast<::mlir::spirv::ImageFormat>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ImageFormat getTombstoneKey() {
    return static_cast<::mlir::spirv::ImageFormat>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ImageFormat &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ImageFormat &lhs, const ::mlir::spirv::ImageFormat &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V ImageOperands
enum class ImageOperands : uint32_t {
  None = 0,
  Bias = 1,
  Lod = 2,
  Grad = 4,
  ConstOffset = 8,
  Offset = 16,
  ConstOffsets = 32,
  Sample = 64,
  MinLod = 128,
  MakeTexelAvailable = 256,
  MakeTexelVisible = 512,
  NonPrivateTexel = 1024,
  VolatileTexel = 2048,
  SignExtend = 4096,
  Offsets = 65536,
  ZeroExtend = 8192,
  Nontemporal = 16384,
};

::std::optional<ImageOperands> symbolizeImageOperands(uint32_t);
std::string stringifyImageOperands(ImageOperands);
::std::optional<ImageOperands> symbolizeImageOperands(::llvm::StringRef);

inline constexpr ImageOperands operator|(ImageOperands a, ImageOperands b) {
  return static_cast<ImageOperands>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}
inline constexpr ImageOperands operator&(ImageOperands a, ImageOperands b) {
  return static_cast<ImageOperands>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}
inline constexpr ImageOperands operator^(ImageOperands a, ImageOperands b) {
  return static_cast<ImageOperands>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}
inline constexpr ImageOperands operator~(ImageOperands bits) {
  // Ensure only bits that can be present in the enum are set
  return static_cast<ImageOperands>(~static_cast<uint32_t>(bits) & static_cast<uint32_t>(98303u));
}
inline constexpr bool bitEnumContainsAll(ImageOperands bits, ImageOperands bit) {
  return (bits & bit) == bit;
}
inline constexpr bool bitEnumContainsAny(ImageOperands bits, ImageOperands bit) {
  return (static_cast<uint32_t>(bits) & static_cast<uint32_t>(bit)) != 0;
}
inline constexpr ImageOperands bitEnumClear(ImageOperands bits, ImageOperands bit) {
  return bits & ~bit;
}
inline constexpr ImageOperands bitEnumSet(ImageOperands bits, ImageOperands bit, /*optional*/bool value=true) {
  return value ? (bits | bit) : bitEnumClear(bits, bit);
}
  
inline std::string stringifyEnum(ImageOperands enumValue) {
  return stringifyImageOperands(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ImageOperands> symbolizeEnum<ImageOperands>(::llvm::StringRef str) {
  return symbolizeImageOperands(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ImageOperands, ::mlir::spirv::ImageOperands> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ImageOperands> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V ImageOperands");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ImageOperands> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ImageOperands>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V ImageOperands specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ImageOperands value) {
  auto valueStr = stringifyEnum(value);
  auto underlyingValue = static_cast<std::make_unsigned_t<::mlir::spirv::ImageOperands>>(value);
  if (underlyingValue && !llvm::has_single_bit(underlyingValue))
    return p << '"' << valueStr << '"';
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ImageOperands> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ImageOperands getEmptyKey() {
    return static_cast<::mlir::spirv::ImageOperands>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ImageOperands getTombstoneKey() {
    return static_cast<::mlir::spirv::ImageOperands>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ImageOperands &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ImageOperands &lhs, const ::mlir::spirv::ImageOperands &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V LinkageType
enum class LinkageType : uint32_t {
  Export = 0,
  Import = 1,
  LinkOnceODR = 2,
};

::std::optional<LinkageType> symbolizeLinkageType(uint32_t);
::llvm::StringRef stringifyLinkageType(LinkageType);
::std::optional<LinkageType> symbolizeLinkageType(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForLinkageType() {
  return 2;
}


inline ::llvm::StringRef stringifyEnum(LinkageType enumValue) {
  return stringifyLinkageType(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<LinkageType> symbolizeEnum<LinkageType>(::llvm::StringRef str) {
  return symbolizeLinkageType(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::LinkageType, ::mlir::spirv::LinkageType> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::LinkageType> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V LinkageType");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::LinkageType> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::LinkageType>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V LinkageType specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::LinkageType value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::LinkageType> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::LinkageType getEmptyKey() {
    return static_cast<::mlir::spirv::LinkageType>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::LinkageType getTombstoneKey() {
    return static_cast<::mlir::spirv::LinkageType>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::LinkageType &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::LinkageType &lhs, const ::mlir::spirv::LinkageType &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V LoopControl
enum class LoopControl : uint32_t {
  None = 0,
  Unroll = 1,
  DontUnroll = 2,
  DependencyInfinite = 4,
  DependencyLength = 8,
  MinIterations = 16,
  MaxIterations = 32,
  IterationMultiple = 64,
  PeelCount = 128,
  PartialCount = 256,
  InitiationIntervalINTEL = 65536,
  LoopCoalesceINTEL = 1048576,
  MaxConcurrencyINTEL = 131072,
  MaxInterleavingINTEL = 2097152,
  DependencyArrayINTEL = 262144,
  SpeculatedIterationsINTEL = 4194304,
  PipelineEnableINTEL = 524288,
  NoFusionINTEL = 8388608,
};

::std::optional<LoopControl> symbolizeLoopControl(uint32_t);
std::string stringifyLoopControl(LoopControl);
::std::optional<LoopControl> symbolizeLoopControl(::llvm::StringRef);

inline constexpr LoopControl operator|(LoopControl a, LoopControl b) {
  return static_cast<LoopControl>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}
inline constexpr LoopControl operator&(LoopControl a, LoopControl b) {
  return static_cast<LoopControl>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}
inline constexpr LoopControl operator^(LoopControl a, LoopControl b) {
  return static_cast<LoopControl>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}
inline constexpr LoopControl operator~(LoopControl bits) {
  // Ensure only bits that can be present in the enum are set
  return static_cast<LoopControl>(~static_cast<uint32_t>(bits) & static_cast<uint32_t>(16712191u));
}
inline constexpr bool bitEnumContainsAll(LoopControl bits, LoopControl bit) {
  return (bits & bit) == bit;
}
inline constexpr bool bitEnumContainsAny(LoopControl bits, LoopControl bit) {
  return (static_cast<uint32_t>(bits) & static_cast<uint32_t>(bit)) != 0;
}
inline constexpr LoopControl bitEnumClear(LoopControl bits, LoopControl bit) {
  return bits & ~bit;
}
inline constexpr LoopControl bitEnumSet(LoopControl bits, LoopControl bit, /*optional*/bool value=true) {
  return value ? (bits | bit) : bitEnumClear(bits, bit);
}
  
inline std::string stringifyEnum(LoopControl enumValue) {
  return stringifyLoopControl(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<LoopControl> symbolizeEnum<LoopControl>(::llvm::StringRef str) {
  return symbolizeLoopControl(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::LoopControl, ::mlir::spirv::LoopControl> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::LoopControl> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V LoopControl");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::LoopControl> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::LoopControl>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V LoopControl specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::LoopControl value) {
  auto valueStr = stringifyEnum(value);
  auto underlyingValue = static_cast<std::make_unsigned_t<::mlir::spirv::LoopControl>>(value);
  if (underlyingValue && !llvm::has_single_bit(underlyingValue))
    return p << '"' << valueStr << '"';
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::LoopControl> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::LoopControl getEmptyKey() {
    return static_cast<::mlir::spirv::LoopControl>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::LoopControl getTombstoneKey() {
    return static_cast<::mlir::spirv::LoopControl>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::LoopControl &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::LoopControl &lhs, const ::mlir::spirv::LoopControl &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V MemoryAccess
enum class MemoryAccess : uint32_t {
  None = 0,
  Volatile = 1,
  Aligned = 2,
  Nontemporal = 4,
  MakePointerAvailable = 8,
  MakePointerVisible = 16,
  NonPrivatePointer = 32,
  AliasScopeINTELMask = 65536,
  NoAliasINTELMask = 131072,
};

::std::optional<MemoryAccess> symbolizeMemoryAccess(uint32_t);
std::string stringifyMemoryAccess(MemoryAccess);
::std::optional<MemoryAccess> symbolizeMemoryAccess(::llvm::StringRef);

inline constexpr MemoryAccess operator|(MemoryAccess a, MemoryAccess b) {
  return static_cast<MemoryAccess>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}
inline constexpr MemoryAccess operator&(MemoryAccess a, MemoryAccess b) {
  return static_cast<MemoryAccess>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}
inline constexpr MemoryAccess operator^(MemoryAccess a, MemoryAccess b) {
  return static_cast<MemoryAccess>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}
inline constexpr MemoryAccess operator~(MemoryAccess bits) {
  // Ensure only bits that can be present in the enum are set
  return static_cast<MemoryAccess>(~static_cast<uint32_t>(bits) & static_cast<uint32_t>(196671u));
}
inline constexpr bool bitEnumContainsAll(MemoryAccess bits, MemoryAccess bit) {
  return (bits & bit) == bit;
}
inline constexpr bool bitEnumContainsAny(MemoryAccess bits, MemoryAccess bit) {
  return (static_cast<uint32_t>(bits) & static_cast<uint32_t>(bit)) != 0;
}
inline constexpr MemoryAccess bitEnumClear(MemoryAccess bits, MemoryAccess bit) {
  return bits & ~bit;
}
inline constexpr MemoryAccess bitEnumSet(MemoryAccess bits, MemoryAccess bit, /*optional*/bool value=true) {
  return value ? (bits | bit) : bitEnumClear(bits, bit);
}
  
inline std::string stringifyEnum(MemoryAccess enumValue) {
  return stringifyMemoryAccess(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<MemoryAccess> symbolizeEnum<MemoryAccess>(::llvm::StringRef str) {
  return symbolizeMemoryAccess(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::MemoryAccess, ::mlir::spirv::MemoryAccess> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::MemoryAccess> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V MemoryAccess");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::MemoryAccess> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::MemoryAccess>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V MemoryAccess specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::MemoryAccess value) {
  auto valueStr = stringifyEnum(value);
  auto underlyingValue = static_cast<std::make_unsigned_t<::mlir::spirv::MemoryAccess>>(value);
  if (underlyingValue && !llvm::has_single_bit(underlyingValue))
    return p << '"' << valueStr << '"';
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::MemoryAccess> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::MemoryAccess getEmptyKey() {
    return static_cast<::mlir::spirv::MemoryAccess>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::MemoryAccess getTombstoneKey() {
    return static_cast<::mlir::spirv::MemoryAccess>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::MemoryAccess &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::MemoryAccess &lhs, const ::mlir::spirv::MemoryAccess &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V MemoryModel
enum class MemoryModel : uint32_t {
  Simple = 0,
  GLSL450 = 1,
  OpenCL = 2,
  Vulkan = 3,
};

::std::optional<MemoryModel> symbolizeMemoryModel(uint32_t);
::llvm::StringRef stringifyMemoryModel(MemoryModel);
::std::optional<MemoryModel> symbolizeMemoryModel(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForMemoryModel() {
  return 3;
}


inline ::llvm::StringRef stringifyEnum(MemoryModel enumValue) {
  return stringifyMemoryModel(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<MemoryModel> symbolizeEnum<MemoryModel>(::llvm::StringRef str) {
  return symbolizeMemoryModel(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::MemoryModel, ::mlir::spirv::MemoryModel> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::MemoryModel> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V MemoryModel");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::MemoryModel> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::MemoryModel>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V MemoryModel specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::MemoryModel value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::MemoryModel> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::MemoryModel getEmptyKey() {
    return static_cast<::mlir::spirv::MemoryModel>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::MemoryModel getTombstoneKey() {
    return static_cast<::mlir::spirv::MemoryModel>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::MemoryModel &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::MemoryModel &lhs, const ::mlir::spirv::MemoryModel &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V MemorySemantics
enum class MemorySemantics : uint32_t {
  None = 0,
  Acquire = 2,
  Release = 4,
  AcquireRelease = 8,
  SequentiallyConsistent = 16,
  UniformMemory = 64,
  SubgroupMemory = 128,
  WorkgroupMemory = 256,
  CrossWorkgroupMemory = 512,
  AtomicCounterMemory = 1024,
  ImageMemory = 2048,
  OutputMemory = 4096,
  MakeAvailable = 8192,
  MakeVisible = 16384,
  Volatile = 32768,
};

::std::optional<MemorySemantics> symbolizeMemorySemantics(uint32_t);
std::string stringifyMemorySemantics(MemorySemantics);
::std::optional<MemorySemantics> symbolizeMemorySemantics(::llvm::StringRef);

inline constexpr MemorySemantics operator|(MemorySemantics a, MemorySemantics b) {
  return static_cast<MemorySemantics>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}
inline constexpr MemorySemantics operator&(MemorySemantics a, MemorySemantics b) {
  return static_cast<MemorySemantics>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}
inline constexpr MemorySemantics operator^(MemorySemantics a, MemorySemantics b) {
  return static_cast<MemorySemantics>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}
inline constexpr MemorySemantics operator~(MemorySemantics bits) {
  // Ensure only bits that can be present in the enum are set
  return static_cast<MemorySemantics>(~static_cast<uint32_t>(bits) & static_cast<uint32_t>(65502u));
}
inline constexpr bool bitEnumContainsAll(MemorySemantics bits, MemorySemantics bit) {
  return (bits & bit) == bit;
}
inline constexpr bool bitEnumContainsAny(MemorySemantics bits, MemorySemantics bit) {
  return (static_cast<uint32_t>(bits) & static_cast<uint32_t>(bit)) != 0;
}
inline constexpr MemorySemantics bitEnumClear(MemorySemantics bits, MemorySemantics bit) {
  return bits & ~bit;
}
inline constexpr MemorySemantics bitEnumSet(MemorySemantics bits, MemorySemantics bit, /*optional*/bool value=true) {
  return value ? (bits | bit) : bitEnumClear(bits, bit);
}
  
inline std::string stringifyEnum(MemorySemantics enumValue) {
  return stringifyMemorySemantics(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<MemorySemantics> symbolizeEnum<MemorySemantics>(::llvm::StringRef str) {
  return symbolizeMemorySemantics(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::MemorySemantics, ::mlir::spirv::MemorySemantics> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::MemorySemantics> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V MemorySemantics");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::MemorySemantics> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::MemorySemantics>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V MemorySemantics specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::MemorySemantics value) {
  auto valueStr = stringifyEnum(value);
  auto underlyingValue = static_cast<std::make_unsigned_t<::mlir::spirv::MemorySemantics>>(value);
  if (underlyingValue && !llvm::has_single_bit(underlyingValue))
    return p << '"' << valueStr << '"';
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::MemorySemantics> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::MemorySemantics getEmptyKey() {
    return static_cast<::mlir::spirv::MemorySemantics>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::MemorySemantics getTombstoneKey() {
    return static_cast<::mlir::spirv::MemorySemantics>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::MemorySemantics &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::MemorySemantics &lhs, const ::mlir::spirv::MemorySemantics &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Scope
enum class Scope : uint32_t {
  CrossDevice = 0,
  Device = 1,
  Workgroup = 2,
  Subgroup = 3,
  Invocation = 4,
  QueueFamily = 5,
  ShaderCallKHR = 6,
};

::std::optional<Scope> symbolizeScope(uint32_t);
::llvm::StringRef stringifyScope(Scope);
::std::optional<Scope> symbolizeScope(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForScope() {
  return 6;
}


inline ::llvm::StringRef stringifyEnum(Scope enumValue) {
  return stringifyScope(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Scope> symbolizeEnum<Scope>(::llvm::StringRef str) {
  return symbolizeScope(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Scope, ::mlir::spirv::Scope> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Scope> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Scope");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Scope> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Scope>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Scope specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Scope value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Scope> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Scope getEmptyKey() {
    return static_cast<::mlir::spirv::Scope>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Scope getTombstoneKey() {
    return static_cast<::mlir::spirv::Scope>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Scope &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Scope &lhs, const ::mlir::spirv::Scope &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V SelectionControl
enum class SelectionControl : uint32_t {
  None = 0,
  Flatten = 1,
  DontFlatten = 2,
};

::std::optional<SelectionControl> symbolizeSelectionControl(uint32_t);
std::string stringifySelectionControl(SelectionControl);
::std::optional<SelectionControl> symbolizeSelectionControl(::llvm::StringRef);

inline constexpr SelectionControl operator|(SelectionControl a, SelectionControl b) {
  return static_cast<SelectionControl>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}
inline constexpr SelectionControl operator&(SelectionControl a, SelectionControl b) {
  return static_cast<SelectionControl>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}
inline constexpr SelectionControl operator^(SelectionControl a, SelectionControl b) {
  return static_cast<SelectionControl>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}
inline constexpr SelectionControl operator~(SelectionControl bits) {
  // Ensure only bits that can be present in the enum are set
  return static_cast<SelectionControl>(~static_cast<uint32_t>(bits) & static_cast<uint32_t>(3u));
}
inline constexpr bool bitEnumContainsAll(SelectionControl bits, SelectionControl bit) {
  return (bits & bit) == bit;
}
inline constexpr bool bitEnumContainsAny(SelectionControl bits, SelectionControl bit) {
  return (static_cast<uint32_t>(bits) & static_cast<uint32_t>(bit)) != 0;
}
inline constexpr SelectionControl bitEnumClear(SelectionControl bits, SelectionControl bit) {
  return bits & ~bit;
}
inline constexpr SelectionControl bitEnumSet(SelectionControl bits, SelectionControl bit, /*optional*/bool value=true) {
  return value ? (bits | bit) : bitEnumClear(bits, bit);
}
  
inline std::string stringifyEnum(SelectionControl enumValue) {
  return stringifySelectionControl(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<SelectionControl> symbolizeEnum<SelectionControl>(::llvm::StringRef str) {
  return symbolizeSelectionControl(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::SelectionControl, ::mlir::spirv::SelectionControl> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::SelectionControl> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V SelectionControl");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::SelectionControl> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::SelectionControl>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V SelectionControl specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::SelectionControl value) {
  auto valueStr = stringifyEnum(value);
  auto underlyingValue = static_cast<std::make_unsigned_t<::mlir::spirv::SelectionControl>>(value);
  if (underlyingValue && !llvm::has_single_bit(underlyingValue))
    return p << '"' << valueStr << '"';
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::SelectionControl> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::SelectionControl getEmptyKey() {
    return static_cast<::mlir::spirv::SelectionControl>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::SelectionControl getTombstoneKey() {
    return static_cast<::mlir::spirv::SelectionControl>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::SelectionControl &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::SelectionControl &lhs, const ::mlir::spirv::SelectionControl &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V StorageClass
enum class StorageClass : uint32_t {
  UniformConstant = 0,
  Input = 1,
  Uniform = 2,
  Output = 3,
  Workgroup = 4,
  CrossWorkgroup = 5,
  Private = 6,
  Function = 7,
  Generic = 8,
  PushConstant = 9,
  AtomicCounter = 10,
  Image = 11,
  StorageBuffer = 12,
  CallableDataKHR = 5328,
  IncomingCallableDataKHR = 5329,
  RayPayloadKHR = 5338,
  HitAttributeKHR = 5339,
  IncomingRayPayloadKHR = 5342,
  ShaderRecordBufferKHR = 5343,
  PhysicalStorageBuffer = 5349,
  CodeSectionINTEL = 5605,
  DeviceOnlyINTEL = 5936,
  HostOnlyINTEL = 5937,
};

::std::optional<StorageClass> symbolizeStorageClass(uint32_t);
::llvm::StringRef stringifyStorageClass(StorageClass);
::std::optional<StorageClass> symbolizeStorageClass(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForStorageClass() {
  return 5937;
}


inline ::llvm::StringRef stringifyEnum(StorageClass enumValue) {
  return stringifyStorageClass(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<StorageClass> symbolizeEnum<StorageClass>(::llvm::StringRef str) {
  return symbolizeStorageClass(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::StorageClass, ::mlir::spirv::StorageClass> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::StorageClass> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V StorageClass");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::StorageClass> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::StorageClass>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V StorageClass specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::StorageClass value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::StorageClass> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::StorageClass getEmptyKey() {
    return static_cast<::mlir::spirv::StorageClass>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::StorageClass getTombstoneKey() {
    return static_cast<::mlir::spirv::StorageClass>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::StorageClass &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::StorageClass &lhs, const ::mlir::spirv::StorageClass &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V PackedVectorFormat
enum class PackedVectorFormat : uint32_t {
  PackedVectorFormat4x8Bit = 0,
};

::std::optional<PackedVectorFormat> symbolizePackedVectorFormat(uint32_t);
::llvm::StringRef stringifyPackedVectorFormat(PackedVectorFormat);
::std::optional<PackedVectorFormat> symbolizePackedVectorFormat(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForPackedVectorFormat() {
  return 0;
}


inline ::llvm::StringRef stringifyEnum(PackedVectorFormat enumValue) {
  return stringifyPackedVectorFormat(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<PackedVectorFormat> symbolizeEnum<PackedVectorFormat>(::llvm::StringRef str) {
  return symbolizePackedVectorFormat(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::PackedVectorFormat, ::mlir::spirv::PackedVectorFormat> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::PackedVectorFormat> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V PackedVectorFormat");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::PackedVectorFormat> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::PackedVectorFormat>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V PackedVectorFormat specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::PackedVectorFormat value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::PackedVectorFormat> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::PackedVectorFormat getEmptyKey() {
    return static_cast<::mlir::spirv::PackedVectorFormat>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::PackedVectorFormat getTombstoneKey() {
    return static_cast<::mlir::spirv::PackedVectorFormat>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::PackedVectorFormat &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::PackedVectorFormat &lhs, const ::mlir::spirv::PackedVectorFormat &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Image Depth specification
enum class ImageDepthInfo : uint32_t {
  NoDepth = 0,
  IsDepth = 1,
  DepthUnknown = 2,
};

::std::optional<ImageDepthInfo> symbolizeImageDepthInfo(uint32_t);
::llvm::StringRef stringifyImageDepthInfo(ImageDepthInfo);
::std::optional<ImageDepthInfo> symbolizeImageDepthInfo(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForImageDepthInfo() {
  return 2;
}


inline ::llvm::StringRef stringifyEnum(ImageDepthInfo enumValue) {
  return stringifyImageDepthInfo(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ImageDepthInfo> symbolizeEnum<ImageDepthInfo>(::llvm::StringRef str) {
  return symbolizeImageDepthInfo(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ImageDepthInfo, ::mlir::spirv::ImageDepthInfo> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ImageDepthInfo> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Image Depth specification");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ImageDepthInfo> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ImageDepthInfo>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Image Depth specification specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ImageDepthInfo value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ImageDepthInfo> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ImageDepthInfo getEmptyKey() {
    return static_cast<::mlir::spirv::ImageDepthInfo>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ImageDepthInfo getTombstoneKey() {
    return static_cast<::mlir::spirv::ImageDepthInfo>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ImageDepthInfo &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ImageDepthInfo &lhs, const ::mlir::spirv::ImageDepthInfo &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Image Arrayed specification
enum class ImageArrayedInfo : uint32_t {
  NonArrayed = 0,
  Arrayed = 1,
};

::std::optional<ImageArrayedInfo> symbolizeImageArrayedInfo(uint32_t);
::llvm::StringRef stringifyImageArrayedInfo(ImageArrayedInfo);
::std::optional<ImageArrayedInfo> symbolizeImageArrayedInfo(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForImageArrayedInfo() {
  return 1;
}


inline ::llvm::StringRef stringifyEnum(ImageArrayedInfo enumValue) {
  return stringifyImageArrayedInfo(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ImageArrayedInfo> symbolizeEnum<ImageArrayedInfo>(::llvm::StringRef str) {
  return symbolizeImageArrayedInfo(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ImageArrayedInfo, ::mlir::spirv::ImageArrayedInfo> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ImageArrayedInfo> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Image Arrayed specification");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ImageArrayedInfo> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ImageArrayedInfo>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Image Arrayed specification specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ImageArrayedInfo value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ImageArrayedInfo> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ImageArrayedInfo getEmptyKey() {
    return static_cast<::mlir::spirv::ImageArrayedInfo>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ImageArrayedInfo getTombstoneKey() {
    return static_cast<::mlir::spirv::ImageArrayedInfo>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ImageArrayedInfo &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ImageArrayedInfo &lhs, const ::mlir::spirv::ImageArrayedInfo &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Image Sampling specification
enum class ImageSamplingInfo : uint32_t {
  SingleSampled = 0,
  MultiSampled = 1,
};

::std::optional<ImageSamplingInfo> symbolizeImageSamplingInfo(uint32_t);
::llvm::StringRef stringifyImageSamplingInfo(ImageSamplingInfo);
::std::optional<ImageSamplingInfo> symbolizeImageSamplingInfo(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForImageSamplingInfo() {
  return 1;
}


inline ::llvm::StringRef stringifyEnum(ImageSamplingInfo enumValue) {
  return stringifyImageSamplingInfo(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ImageSamplingInfo> symbolizeEnum<ImageSamplingInfo>(::llvm::StringRef str) {
  return symbolizeImageSamplingInfo(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ImageSamplingInfo, ::mlir::spirv::ImageSamplingInfo> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ImageSamplingInfo> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Image Sampling specification");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ImageSamplingInfo> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ImageSamplingInfo>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Image Sampling specification specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ImageSamplingInfo value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ImageSamplingInfo> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ImageSamplingInfo getEmptyKey() {
    return static_cast<::mlir::spirv::ImageSamplingInfo>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ImageSamplingInfo getTombstoneKey() {
    return static_cast<::mlir::spirv::ImageSamplingInfo>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ImageSamplingInfo &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ImageSamplingInfo &lhs, const ::mlir::spirv::ImageSamplingInfo &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V Sampler Use specification
enum class ImageSamplerUseInfo : uint32_t {
  SamplerUnknown = 0,
  NeedSampler = 1,
  NoSampler = 2,
};

::std::optional<ImageSamplerUseInfo> symbolizeImageSamplerUseInfo(uint32_t);
::llvm::StringRef stringifyImageSamplerUseInfo(ImageSamplerUseInfo);
::std::optional<ImageSamplerUseInfo> symbolizeImageSamplerUseInfo(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForImageSamplerUseInfo() {
  return 2;
}


inline ::llvm::StringRef stringifyEnum(ImageSamplerUseInfo enumValue) {
  return stringifyImageSamplerUseInfo(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<ImageSamplerUseInfo> symbolizeEnum<ImageSamplerUseInfo>(::llvm::StringRef str) {
  return symbolizeImageSamplerUseInfo(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::ImageSamplerUseInfo, ::mlir::spirv::ImageSamplerUseInfo> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::ImageSamplerUseInfo> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V Sampler Use specification");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::ImageSamplerUseInfo> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::ImageSamplerUseInfo>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V Sampler Use specification specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::ImageSamplerUseInfo value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::ImageSamplerUseInfo> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::ImageSamplerUseInfo getEmptyKey() {
    return static_cast<::mlir::spirv::ImageSamplerUseInfo>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::ImageSamplerUseInfo getTombstoneKey() {
    return static_cast<::mlir::spirv::ImageSamplerUseInfo>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::ImageSamplerUseInfo &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::ImageSamplerUseInfo &lhs, const ::mlir::spirv::ImageSamplerUseInfo &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V MatrixLayout
enum class MatrixLayout : uint32_t {
  ColumnMajor = 0,
  RowMajor = 1,
  PackedA = 2,
  PackedB = 3,
};

::std::optional<MatrixLayout> symbolizeMatrixLayout(uint32_t);
::llvm::StringRef stringifyMatrixLayout(MatrixLayout);
::std::optional<MatrixLayout> symbolizeMatrixLayout(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForMatrixLayout() {
  return 3;
}


inline ::llvm::StringRef stringifyEnum(MatrixLayout enumValue) {
  return stringifyMatrixLayout(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<MatrixLayout> symbolizeEnum<MatrixLayout>(::llvm::StringRef str) {
  return symbolizeMatrixLayout(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::MatrixLayout, ::mlir::spirv::MatrixLayout> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::MatrixLayout> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V MatrixLayout");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::MatrixLayout> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::MatrixLayout>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V MatrixLayout specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::MatrixLayout value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::MatrixLayout> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::MatrixLayout getEmptyKey() {
    return static_cast<::mlir::spirv::MatrixLayout>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::MatrixLayout getTombstoneKey() {
    return static_cast<::mlir::spirv::MatrixLayout>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::MatrixLayout &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::MatrixLayout &lhs, const ::mlir::spirv::MatrixLayout &rhs) {
    return lhs == rhs;
  }
};
}

namespace mlir {
namespace spirv {
// valid SPIR-V instructions
enum class Opcode : uint32_t {
  OpNop = 0,
  OpUndef = 1,
  OpSourceContinued = 2,
  OpSource = 3,
  OpSourceExtension = 4,
  OpName = 5,
  OpMemberName = 6,
  OpString = 7,
  OpLine = 8,
  OpExtension = 10,
  OpExtInstImport = 11,
  OpExtInst = 12,
  OpMemoryModel = 14,
  OpEntryPoint = 15,
  OpExecutionMode = 16,
  OpCapability = 17,
  OpTypeVoid = 19,
  OpTypeBool = 20,
  OpTypeInt = 21,
  OpTypeFloat = 22,
  OpTypeVector = 23,
  OpTypeMatrix = 24,
  OpTypeImage = 25,
  OpTypeSampledImage = 27,
  OpTypeArray = 28,
  OpTypeRuntimeArray = 29,
  OpTypeStruct = 30,
  OpTypePointer = 32,
  OpTypeFunction = 33,
  OpTypeForwardPointer = 39,
  OpConstantTrue = 41,
  OpConstantFalse = 42,
  OpConstant = 43,
  OpConstantComposite = 44,
  OpConstantNull = 46,
  OpSpecConstantTrue = 48,
  OpSpecConstantFalse = 49,
  OpSpecConstant = 50,
  OpSpecConstantComposite = 51,
  OpSpecConstantOp = 52,
  OpFunction = 54,
  OpFunctionParameter = 55,
  OpFunctionEnd = 56,
  OpFunctionCall = 57,
  OpVariable = 59,
  OpLoad = 61,
  OpStore = 62,
  OpCopyMemory = 63,
  OpAccessChain = 65,
  OpPtrAccessChain = 67,
  OpInBoundsPtrAccessChain = 70,
  OpDecorate = 71,
  OpMemberDecorate = 72,
  OpVectorExtractDynamic = 77,
  OpVectorInsertDynamic = 78,
  OpVectorShuffle = 79,
  OpCompositeConstruct = 80,
  OpCompositeExtract = 81,
  OpCompositeInsert = 82,
  OpTranspose = 84,
  OpImageDrefGather = 97,
  OpImage = 100,
  OpImageQuerySize = 104,
  OpConvertFToU = 109,
  OpConvertFToS = 110,
  OpConvertSToF = 111,
  OpConvertUToF = 112,
  OpUConvert = 113,
  OpSConvert = 114,
  OpFConvert = 115,
  OpPtrCastToGeneric = 121,
  OpGenericCastToPtr = 122,
  OpGenericCastToPtrExplicit = 123,
  OpBitcast = 124,
  OpSNegate = 126,
  OpFNegate = 127,
  OpIAdd = 128,
  OpFAdd = 129,
  OpISub = 130,
  OpFSub = 131,
  OpIMul = 132,
  OpFMul = 133,
  OpUDiv = 134,
  OpSDiv = 135,
  OpFDiv = 136,
  OpUMod = 137,
  OpSRem = 138,
  OpSMod = 139,
  OpFRem = 140,
  OpFMod = 141,
  OpVectorTimesScalar = 142,
  OpMatrixTimesScalar = 143,
  OpMatrixTimesMatrix = 146,
  OpIAddCarry = 149,
  OpISubBorrow = 150,
  OpUMulExtended = 151,
  OpSMulExtended = 152,
  OpIsNan = 156,
  OpIsInf = 157,
  OpOrdered = 162,
  OpUnordered = 163,
  OpLogicalEqual = 164,
  OpLogicalNotEqual = 165,
  OpLogicalOr = 166,
  OpLogicalAnd = 167,
  OpLogicalNot = 168,
  OpSelect = 169,
  OpIEqual = 170,
  OpINotEqual = 171,
  OpUGreaterThan = 172,
  OpSGreaterThan = 173,
  OpUGreaterThanEqual = 174,
  OpSGreaterThanEqual = 175,
  OpULessThan = 176,
  OpSLessThan = 177,
  OpULessThanEqual = 178,
  OpSLessThanEqual = 179,
  OpFOrdEqual = 180,
  OpFUnordEqual = 181,
  OpFOrdNotEqual = 182,
  OpFUnordNotEqual = 183,
  OpFOrdLessThan = 184,
  OpFUnordLessThan = 185,
  OpFOrdGreaterThan = 186,
  OpFUnordGreaterThan = 187,
  OpFOrdLessThanEqual = 188,
  OpFUnordLessThanEqual = 189,
  OpFOrdGreaterThanEqual = 190,
  OpFUnordGreaterThanEqual = 191,
  OpShiftRightLogical = 194,
  OpShiftRightArithmetic = 195,
  OpShiftLeftLogical = 196,
  OpBitwiseOr = 197,
  OpBitwiseXor = 198,
  OpBitwiseAnd = 199,
  OpNot = 200,
  OpBitFieldInsert = 201,
  OpBitFieldSExtract = 202,
  OpBitFieldUExtract = 203,
  OpBitReverse = 204,
  OpBitCount = 205,
  OpControlBarrier = 224,
  OpMemoryBarrier = 225,
  OpAtomicExchange = 229,
  OpAtomicCompareExchange = 230,
  OpAtomicCompareExchangeWeak = 231,
  OpAtomicIIncrement = 232,
  OpAtomicIDecrement = 233,
  OpAtomicIAdd = 234,
  OpAtomicISub = 235,
  OpAtomicSMin = 236,
  OpAtomicUMin = 237,
  OpAtomicSMax = 238,
  OpAtomicUMax = 239,
  OpAtomicAnd = 240,
  OpAtomicOr = 241,
  OpAtomicXor = 242,
  OpPhi = 245,
  OpLoopMerge = 246,
  OpSelectionMerge = 247,
  OpLabel = 248,
  OpBranch = 249,
  OpBranchConditional = 250,
  OpReturn = 253,
  OpReturnValue = 254,
  OpUnreachable = 255,
  OpGroupBroadcast = 263,
  OpGroupIAdd = 264,
  OpGroupFAdd = 265,
  OpGroupFMin = 266,
  OpGroupUMin = 267,
  OpGroupSMin = 268,
  OpGroupFMax = 269,
  OpGroupUMax = 270,
  OpGroupSMax = 271,
  OpNoLine = 317,
  OpModuleProcessed = 330,
  OpGroupNonUniformElect = 333,
  OpGroupNonUniformBroadcast = 337,
  OpGroupNonUniformBallot = 339,
  OpGroupNonUniformShuffle = 345,
  OpGroupNonUniformShuffleXor = 346,
  OpGroupNonUniformShuffleUp = 347,
  OpGroupNonUniformShuffleDown = 348,
  OpGroupNonUniformIAdd = 349,
  OpGroupNonUniformFAdd = 350,
  OpGroupNonUniformIMul = 351,
  OpGroupNonUniformFMul = 352,
  OpGroupNonUniformSMin = 353,
  OpGroupNonUniformUMin = 354,
  OpGroupNonUniformFMin = 355,
  OpGroupNonUniformSMax = 356,
  OpGroupNonUniformUMax = 357,
  OpGroupNonUniformFMax = 358,
  OpSubgroupBallotKHR = 4421,
  OpSDot = 4450,
  OpUDot = 4451,
  OpSUDot = 4452,
  OpSDotAccSat = 4453,
  OpUDotAccSat = 4454,
  OpSUDotAccSat = 4455,
  OpTypeCooperativeMatrixNV = 5358,
  OpCooperativeMatrixLoadNV = 5359,
  OpCooperativeMatrixStoreNV = 5360,
  OpCooperativeMatrixMulAddNV = 5361,
  OpCooperativeMatrixLengthNV = 5362,
  OpSubgroupBlockReadINTEL = 5575,
  OpSubgroupBlockWriteINTEL = 5576,
  OpAssumeTrueKHR = 5630,
  OpAtomicFAddEXT = 6035,
  OpGroupIMulKHR = 6401,
  OpGroupFMulKHR = 6402,
  OpTypeJointMatrixINTEL = 6119,
  OpJointMatrixLoadINTEL = 6120,
  OpJointMatrixStoreINTEL = 6121,
  OpJointMatrixMadINTEL = 6122,
  OpJointMatrixWorkItemLengthINTEL = 6410,
  OpConvertFToBF16INTEL = 6116,
  OpConvertBF16ToFINTEL = 6117,
};

::std::optional<Opcode> symbolizeOpcode(uint32_t);
::llvm::StringRef stringifyOpcode(Opcode);
::std::optional<Opcode> symbolizeOpcode(::llvm::StringRef);
inline constexpr unsigned getMaxEnumValForOpcode() {
  return 6410;
}


inline ::llvm::StringRef stringifyEnum(Opcode enumValue) {
  return stringifyOpcode(enumValue);
}

template <typename EnumType>
::std::optional<EnumType> symbolizeEnum(::llvm::StringRef);

template <>
inline ::std::optional<Opcode> symbolizeEnum<Opcode>(::llvm::StringRef str) {
  return symbolizeOpcode(str);
}
} // namespace spirv
} // namespace mlir

namespace mlir {
template <typename T, typename>
struct FieldParser;

template<>
struct FieldParser<::mlir::spirv::Opcode, ::mlir::spirv::Opcode> {
  template <typename ParserT>
  static FailureOr<::mlir::spirv::Opcode> parse(ParserT &parser) {
    // Parse the keyword/string containing the enum.
    std::string enumKeyword;
    auto loc = parser.getCurrentLocation();
    if (failed(parser.parseOptionalKeywordOrString(&enumKeyword)))
      return parser.emitError(loc, "expected keyword for valid SPIR-V instructions");

    // Symbolize the keyword.
    if (::std::optional<::mlir::spirv::Opcode> attr = ::mlir::spirv::symbolizeEnum<::mlir::spirv::Opcode>(enumKeyword))
      return *attr;
    return parser.emitError(loc, "invalid valid SPIR-V instructions specification: ") << enumKeyword;
  }
};
} // namespace mlir

namespace llvm {
inline ::llvm::raw_ostream &operator<<(::llvm::raw_ostream &p, ::mlir::spirv::Opcode value) {
  auto valueStr = stringifyEnum(value);
  return p << valueStr;
}
} // namespace llvm

namespace llvm {
template<> struct DenseMapInfo<::mlir::spirv::Opcode> {
  using StorageInfo = ::llvm::DenseMapInfo<uint32_t>;

  static inline ::mlir::spirv::Opcode getEmptyKey() {
    return static_cast<::mlir::spirv::Opcode>(StorageInfo::getEmptyKey());
  }

  static inline ::mlir::spirv::Opcode getTombstoneKey() {
    return static_cast<::mlir::spirv::Opcode>(StorageInfo::getTombstoneKey());
  }

  static unsigned getHashValue(const ::mlir::spirv::Opcode &val) {
    return StorageInfo::getHashValue(static_cast<uint32_t>(val));
  }

  static bool isEqual(const ::mlir::spirv::Opcode &lhs, const ::mlir::spirv::Opcode &rhs) {
    return lhs == rhs;
  }
};
}

