/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_OPTIMIZESHAREDMEMORY
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// OptimizeSharedMemory
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_OPTIMIZESHAREDMEMORY
#undef GEN_PASS_DECL_OPTIMIZESHAREDMEMORY
#endif // GEN_PASS_DECL_OPTIMIZESHAREDMEMORY
#ifdef GEN_PASS_DEF_OPTIMIZESHAREDMEMORY
namespace impl {

template <typename DerivedT>
class OptimizeSharedMemoryBase : public ::mlir::OperationPass<> {
public:
  using Base = OptimizeSharedMemoryBase;

  OptimizeSharedMemoryBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeSharedMemoryBase(const OptimizeSharedMemoryBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("nvgpu-optimize-shared-memory");
  }
  ::llvm::StringRef getArgument() const override { return "nvgpu-optimize-shared-memory"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes accesses to shard memory memrefs in order to reduce bank conflicts."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeSharedMemory");
  }
  ::llvm::StringRef getName() const override { return "OptimizeSharedMemory"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OptimizeSharedMemoryBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_OPTIMIZESHAREDMEMORY
#endif // GEN_PASS_DEF_OPTIMIZESHAREDMEMORY
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// OptimizeSharedMemory Registration
//===----------------------------------------------------------------------===//

inline void registerOptimizeSharedMemory() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::nvgpu::createOptimizeSharedMemoryPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOptimizeSharedMemoryPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::nvgpu::createOptimizeSharedMemoryPass();
  });
}

//===----------------------------------------------------------------------===//
// NVGPU Registration
//===----------------------------------------------------------------------===//

inline void registerNVGPUPasses() {
  registerOptimizeSharedMemory();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class OptimizeSharedMemoryBase : public ::mlir::OperationPass<> {
public:
  using Base = OptimizeSharedMemoryBase;

  OptimizeSharedMemoryBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeSharedMemoryBase(const OptimizeSharedMemoryBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("nvgpu-optimize-shared-memory");
  }
  ::llvm::StringRef getArgument() const override { return "nvgpu-optimize-shared-memory"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes accesses to shard memory memrefs in order to reduce bank conflicts."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeSharedMemory");
  }
  ::llvm::StringRef getName() const override { return "OptimizeSharedMemory"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OptimizeSharedMemoryBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
