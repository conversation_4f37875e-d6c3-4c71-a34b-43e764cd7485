/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_FOLDTENSORSUBSETOPS
#define GEN_PASS_DECL_TENSORBUFFERIZE
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// FoldTensorSubsetOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FOLDTENSORSUBSETOPS
#undef GEN_PASS_DECL_FOLDTENSORSUBSETOPS
#endif // GEN_PASS_DECL_FOLDTENSORSUBSETOPS
#ifdef GEN_PASS_DEF_FOLDTENSORSUBSETOPS
namespace impl {

template <typename DerivedT>
class FoldTensorSubsetOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = FoldTensorSubsetOpsBase;

  FoldTensorSubsetOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  FoldTensorSubsetOpsBase(const FoldTensorSubsetOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("fold-tensor-subset-ops");
  }
  ::llvm::StringRef getArgument() const override { return "fold-tensor-subset-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fold tensor subset ops into producer/consumer ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FoldTensorSubsetOps");
  }
  ::llvm::StringRef getName() const override { return "FoldTensorSubsetOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FoldTensorSubsetOpsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FOLDTENSORSUBSETOPS
#endif // GEN_PASS_DEF_FOLDTENSORSUBSETOPS

//===----------------------------------------------------------------------===//
// TensorBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TENSORBUFFERIZE
#undef GEN_PASS_DECL_TENSORBUFFERIZE
#endif // GEN_PASS_DECL_TENSORBUFFERIZE
#ifdef GEN_PASS_DEF_TENSORBUFFERIZE
namespace impl {

template <typename DerivedT>
class TensorBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TensorBufferizeBase;

  TensorBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TensorBufferizeBase(const TensorBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tensor-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "tensor-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the `tensor` dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorBufferize");
  }
  ::llvm::StringRef getName() const override { return "TensorBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TensorBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TENSORBUFFERIZE
#endif // GEN_PASS_DEF_TENSORBUFFERIZE
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// FoldTensorSubsetOps Registration
//===----------------------------------------------------------------------===//

inline void registerFoldTensorSubsetOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tensor::createFoldTensorSubsetOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFoldTensorSubsetOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tensor::createFoldTensorSubsetOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerTensorBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tensor::createTensorBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTensorBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tensor::createTensorBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Tensor Registration
//===----------------------------------------------------------------------===//

inline void registerTensorPasses() {
  registerFoldTensorSubsetOps();
  registerTensorBufferize();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class FoldTensorSubsetOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = FoldTensorSubsetOpsBase;

  FoldTensorSubsetOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  FoldTensorSubsetOpsBase(const FoldTensorSubsetOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("fold-tensor-subset-ops");
  }
  ::llvm::StringRef getArgument() const override { return "fold-tensor-subset-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fold tensor subset ops into producer/consumer ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FoldTensorSubsetOps");
  }
  ::llvm::StringRef getName() const override { return "FoldTensorSubsetOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FoldTensorSubsetOpsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TensorBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TensorBufferizeBase;

  TensorBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TensorBufferizeBase(const TensorBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tensor-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "tensor-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the `tensor` dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorBufferize");
  }
  ::llvm::StringRef getName() const override { return "TensorBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TensorBufferizeBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
