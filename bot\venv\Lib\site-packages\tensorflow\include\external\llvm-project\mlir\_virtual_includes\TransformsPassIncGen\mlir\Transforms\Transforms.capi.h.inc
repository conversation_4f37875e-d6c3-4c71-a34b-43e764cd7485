
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPasses(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsCSE(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsCSE(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsCanonicalizer(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsCanonicalizer(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsControlFlowSink(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsControlFlowSink(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsGenerateRuntimeVerification(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsGenerateRuntimeVerification(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsInliner(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsInliner(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLocationSnapshot(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLocationSnapshot(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLoopInvariantCodeMotion(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLoopInvariantCodeMotion(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsMem2Reg(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsMem2Reg(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsPrintIRPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPrintIRPass(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsPrintOpStats(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPrintOpStats(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSCCP(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSCCP(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsStripDebugInfo(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsStripDebugInfo(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSymbolDCE(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSymbolDCE(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSymbolPrivatize(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSymbolPrivatize(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsTopologicalSort(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsTopologicalSort(void);


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsViewOpGraph(void);
MLIR_CAPI_EXPORTED void mlirRegisterTransformsViewOpGraph(void);



#ifdef __cplusplus
}
#endif
