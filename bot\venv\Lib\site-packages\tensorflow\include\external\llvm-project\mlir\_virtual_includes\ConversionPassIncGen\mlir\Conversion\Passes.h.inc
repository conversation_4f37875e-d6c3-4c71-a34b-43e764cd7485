/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
#define GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
#define GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#define GEN_PASS_DECL_CONVERTARITHTOSPIRV
#define GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#define GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
#define GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#define GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
#define GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#define GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
#define GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDAR<PERSON>
#define GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
#define GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
#define GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
#define GEN_PASS_DECL_CONVERTFUNCTOSPIRV
#define GEN_PASS_DECL_CONVERTGPUTOSPIRV
#define GEN_PASS_DECL_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC
#define GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
#define GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
#define GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
#define GEN_PASS_DECL_CONVERTLINALGTOLLVMPASS
#define GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#define GEN_PASS_DECL_CONVERTMATHTOFUNCS
#define GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
#define GEN_PASS_DECL_CONVERTMATHTOLIBM
#define GEN_PASS_DECL_CONVERTMATHTOSPIRV
#define GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
#define GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
#define GEN_PASS_DECL_CONVERTOPENACCTOLLVMPASS
#define GEN_PASS_DECL_CONVERTOPENACCTOSCF
#define GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
#define GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#define GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#define GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
#define GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
#define GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#define GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#define GEN_PASS_DECL_CONVERTTENSORTOLINALG
#define GEN_PASS_DECL_CONVERTTENSORTOSPIRV
#define GEN_PASS_DECL_CONVERTVECTORTOGPU
#define GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
#define GEN_PASS_DECL_CONVERTVECTORTOSCF
#define GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#define GEN_PASS_DECL_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS
#define GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
#define GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
#define GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#define GEN_PASS_DECL_SCFTOCONTROLFLOW
#define GEN_PASS_DECL_SCFTOSPIRV
#define GEN_PASS_DECL_TOSATOARITH
#define GEN_PASS_DECL_TOSATOLINALG
#define GEN_PASS_DECL_TOSATOLINALGNAMED
#define GEN_PASS_DECL_TOSATOSCF
#define GEN_PASS_DECL_TOSATOTENSOR
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ArithToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
struct ArithToLLVMConversionPassOptions {
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(const ArithToLLVMConversionPassOptions &options);
#undef GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_ARITHTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(const ArithToLLVMConversionPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithToLLVMConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToLLVMConversionPassBase;

  ArithToLLVMConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToLLVMConversionPassBase(const ArithToLLVMConversionPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToLLVMConversionPassBase<DerivedT>)

  ArithToLLVMConversionPassBase(const ArithToLLVMConversionPassOptions &options) : ArithToLLVMConversionPassBase() {
    indexBitwidth = options.indexBitwidth;
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(const ArithToLLVMConversionPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass() {
  return impl::createArithToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(const ArithToLLVMConversionPassOptions &options) {
  return impl::createArithToLLVMConversionPass(options);
}
#undef GEN_PASS_DEF_ARITHTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_ARITHTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// ConvertAMDGPUToROCDL
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
struct ConvertAMDGPUToROCDLOptions {
  std::string chipset = "gfx000";
};
#undef GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
#endif // GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
#ifdef GEN_PASS_DEF_CONVERTAMDGPUTOROCDL
namespace impl {

template <typename DerivedT>
class ConvertAMDGPUToROCDLBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAMDGPUToROCDLBase;

  ConvertAMDGPUToROCDLBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAMDGPUToROCDLBase(const ConvertAMDGPUToROCDLBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-amdgpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-amdgpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Convert AMDGPU dialect to ROCDL dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAMDGPUToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertAMDGPUToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  registry.insert<ROCDL::ROCDLDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAMDGPUToROCDLBase<DerivedT>)

  ConvertAMDGPUToROCDLBase(const ConvertAMDGPUToROCDLOptions &options) : ConvertAMDGPUToROCDLBase() {
    chipset = options.chipset;
  }
protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTAMDGPUTOROCDL
#endif // GEN_PASS_DEF_CONVERTAMDGPUTOROCDL

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
struct ConvertAffineForToGPUOptions {
  unsigned numBlockDims = 1u;
  unsigned numThreadDims = 1u;
};
#undef GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
#endif // GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
#ifdef GEN_PASS_DEF_CONVERTAFFINEFORTOGPU
namespace impl {

template <typename DerivedT>
class ConvertAffineForToGPUBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertAffineForToGPUBase;

  ConvertAffineForToGPUBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineForToGPUBase(const ConvertAffineForToGPUBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-affine-for-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-affine-for-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert top-level AffineFor Ops to GPU kernels"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineForToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineForToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineForToGPUBase<DerivedT>)

  ConvertAffineForToGPUBase(const ConvertAffineForToGPUOptions &options) : ConvertAffineForToGPUBase() {
    numBlockDims = options.numBlockDims;
    numThreadDims = options.numThreadDims;
  }
protected:
  ::mlir::Pass::Option<unsigned> numBlockDims{*this, "gpu-block-dims", ::llvm::cl::desc("Number of GPU block dimensions for mapping"), ::llvm::cl::init(1u)};
  ::mlir::Pass::Option<unsigned> numThreadDims{*this, "gpu-thread-dims", ::llvm::cl::desc("Number of GPU thread dimensions for mapping"), ::llvm::cl::init(1u)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTAFFINEFORTOGPU
#endif // GEN_PASS_DEF_CONVERTAFFINEFORTOGPU

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#undef GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#endif // GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTAFFINETOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertAffineToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAffineToStandardBase;

  ConvertAffineToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineToStandardBase(const ConvertAffineToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lower-affine"; }

  ::llvm::StringRef getDescription() const override { return "Lower Affine operations to a combination of Standard and SCF operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTAFFINETOSTANDARD
#endif // GEN_PASS_DEF_CONVERTAFFINETOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertArithToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARITHTOSPIRV
struct ConvertArithToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
  bool enableFastMath = false;
};
#undef GEN_PASS_DECL_CONVERTARITHTOSPIRV
#endif // GEN_PASS_DECL_CONVERTARITHTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTARITHTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertArithToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithToSPIRVBase;

  ConvertArithToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithToSPIRVBase(const ConvertArithToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArithToSPIRVBase<DerivedT>)

  ConvertArithToSPIRVBase(const ConvertArithToSPIRVOptions &options) : ConvertArithToSPIRVBase() {
    emulateLT32BitScalarTypes = options.emulateLT32BitScalarTypes;
    enableFastMath = options.enableFastMath;
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableFastMath{*this, "enable-fast-math", ::llvm::cl::desc("Enable fast math mode (assuming no NaN and infinity for floating point values) when performing conversion"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTARITHTOSPIRV
#endif // GEN_PASS_DEF_CONVERTARITHTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#undef GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#endif // GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#ifdef GEN_PASS_DEF_CONVERTARMNEON2DTOINTR
namespace impl {

template <typename DerivedT>
class ConvertArmNeon2dToIntrBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmNeon2dToIntrBase;

  ConvertArmNeon2dToIntrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmNeon2dToIntrBase(const ConvertArmNeon2dToIntrBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-neon-2d-to-intr");
  }
  ::llvm::StringRef getArgument() const override { return "arm-neon-2d-to-intr"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arm NEON structured ops to intrinsics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmNeon2dToIntr");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmNeon2dToIntr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arm_neon::ArmNeonDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmNeon2dToIntrBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTARMNEON2DTOINTR
#endif // GEN_PASS_DEF_CONVERTARMNEON2DTOINTR

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
struct ConvertAsyncToLLVMPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass(const ConvertAsyncToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTASYNCTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass(const ConvertAsyncToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertAsyncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertAsyncToLLVMPassBase;

  ConvertAsyncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAsyncToLLVMPassBase(const ConvertAsyncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-async-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-async-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the async dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAsyncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertAsyncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<async::AsyncDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAsyncToLLVMPassBase<DerivedT>)

  ConvertAsyncToLLVMPassBase(const ConvertAsyncToLLVMPassOptions &options) : ConvertAsyncToLLVMPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass(const ConvertAsyncToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass() {
  return impl::createConvertAsyncToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass(const ConvertAsyncToLLVMPassOptions &options) {
  return impl::createConvertAsyncToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTASYNCTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTASYNCTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertBufferizationToMemRef
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#undef GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#endif // GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#ifdef GEN_PASS_DEF_CONVERTBUFFERIZATIONTOMEMREF
namespace impl {

template <typename DerivedT>
class ConvertBufferizationToMemRefBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertBufferizationToMemRefBase;

  ConvertBufferizationToMemRefBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertBufferizationToMemRefBase(const ConvertBufferizationToMemRefBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-bufferization-to-memref");
  }
  ::llvm::StringRef getArgument() const override { return "convert-bufferization-to-memref"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the Bufferization dialect to the MemRef dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertBufferizationToMemRef");
  }
  ::llvm::StringRef getName() const override { return "ConvertBufferizationToMemRef"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertBufferizationToMemRefBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTBUFFERIZATIONTOMEMREF
#endif // GEN_PASS_DEF_CONVERTBUFFERIZATIONTOMEMREF

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass();
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertComplexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToLLVMPassBase;

  ConvertComplexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLLVMPassBase(const ConvertComplexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass() {
  return impl::createConvertComplexToLLVMPass();
}
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertComplexToLibm
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOLIBM
namespace impl {

template <typename DerivedT>
class ConvertComplexToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertComplexToLibmBase;

  ConvertComplexToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLibmBase(const ConvertComplexToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLibmBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOLIBM
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOLIBM

//===----------------------------------------------------------------------===//
// ConvertComplexToSPIRVPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass();
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOSPIRVPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertComplexToSPIRVPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToSPIRVPassBase;

  ConvertComplexToSPIRVPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToSPIRVPassBase(const ConvertComplexToSPIRVPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to SPIRV dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToSPIRVPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToSPIRVPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToSPIRVPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass() {
  return impl::createConvertComplexToSPIRVPass();
}
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOSPIRVPASS
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOSPIRVPASS

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertComplexToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToStandardBase;

  ConvertComplexToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToStandardBase(const ConvertComplexToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<math::MathDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOSTANDARD
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertControlFlowToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
struct ConvertControlFlowToLLVMPassOptions {
  unsigned indexBitwidth = 0;
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(const ConvertControlFlowToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTCONTROLFLOWTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(const ConvertControlFlowToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertControlFlowToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertControlFlowToLLVMPassBase;

  ConvertControlFlowToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToLLVMPassBase(const ConvertControlFlowToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow operations to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToLLVMPassBase<DerivedT>)

  ConvertControlFlowToLLVMPassBase(const ConvertControlFlowToLLVMPassOptions &options) : ConvertControlFlowToLLVMPassBase() {
    indexBitwidth = options.indexBitwidth;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(const ConvertControlFlowToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass() {
  return impl::createConvertControlFlowToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(const ConvertControlFlowToLLVMPassOptions &options) {
  return impl::createConvertControlFlowToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTCONTROLFLOWTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTCONTROLFLOWTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertControlFlowToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
struct ConvertControlFlowToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
#endif // GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTCONTROLFLOWTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertControlFlowToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertControlFlowToSPIRVBase;

  ConvertControlFlowToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToSPIRVBase(const ConvertControlFlowToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToSPIRVBase<DerivedT>)

  ConvertControlFlowToSPIRVBase(const ConvertControlFlowToSPIRVOptions &options) : ConvertControlFlowToSPIRVBase() {
    emulateLT32BitScalarTypes = options.emulateLT32BitScalarTypes;
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTCONTROLFLOWTOSPIRV
#endif // GEN_PASS_DEF_CONVERTCONTROLFLOWTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertFuncToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
struct ConvertFuncToLLVMPassOptions {
  bool useBarePtrCallConv = false;
  unsigned indexBitwidth = 0;
  std::string dataLayout = "";
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(const ConvertFuncToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTFUNCTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(const ConvertFuncToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertFuncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToLLVMPassBase;

  ConvertFuncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToLLVMPassBase(const ConvertFuncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert from the Func dialect to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToLLVMPassBase<DerivedT>)

  ConvertFuncToLLVMPassBase(const ConvertFuncToLLVMPassOptions &options) : ConvertFuncToLLVMPassBase() {
    useBarePtrCallConv = options.useBarePtrCallConv;
    indexBitwidth = options.indexBitwidth;
    dataLayout = options.dataLayout;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace FuncOp's MemRef arguments with bare pointers to the MemRef element types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<std::string> dataLayout{*this, "data-layout", ::llvm::cl::desc("String description (LLVM format) of the data layout that is expected on the produced module"), ::llvm::cl::init("")};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(const ConvertFuncToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass() {
  return impl::createConvertFuncToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(const ConvertFuncToLLVMPassOptions &options) {
  return impl::createConvertFuncToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTFUNCTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTFUNCTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertFuncToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTFUNCTOSPIRV
struct ConvertFuncToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTFUNCTOSPIRV
#endif // GEN_PASS_DECL_CONVERTFUNCTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTFUNCTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertFuncToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertFuncToSPIRVBase;

  ConvertFuncToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToSPIRVBase(const ConvertFuncToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToSPIRVBase<DerivedT>)

  ConvertFuncToSPIRVBase(const ConvertFuncToSPIRVOptions &options) : ConvertFuncToSPIRVBase() {
    emulateLT32BitScalarTypes = options.emulateLT32BitScalarTypes;
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTFUNCTOSPIRV
#endif // GEN_PASS_DEF_CONVERTFUNCTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUTOSPIRV
struct ConvertGPUToSPIRVOptions {
  bool use64bitIndex = false;
};
#undef GEN_PASS_DECL_CONVERTGPUTOSPIRV
#endif // GEN_PASS_DECL_CONVERTGPUTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTGPUTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertGPUToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGPUToSPIRVBase;

  ConvertGPUToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGPUToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertGPUToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGPUToSPIRVBase<DerivedT>)

  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVOptions &options) : ConvertGPUToSPIRVBase() {
    use64bitIndex = options.use64bitIndex;
  }
protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTGPUTOSPIRV
#endif // GEN_PASS_DEF_CONVERTGPUTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertGpuLaunchFuncToVulkanLaunchFunc
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC
#undef GEN_PASS_DECL_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC
#endif // GEN_PASS_DECL_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC
#ifdef GEN_PASS_DEF_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC
namespace impl {

template <typename DerivedT>
class ConvertGpuLaunchFuncToVulkanLaunchFuncBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGpuLaunchFuncToVulkanLaunchFuncBase;

  ConvertGpuLaunchFuncToVulkanLaunchFuncBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuLaunchFuncToVulkanLaunchFuncBase(const ConvertGpuLaunchFuncToVulkanLaunchFuncBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-launch-to-vulkan-launch");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-launch-to-vulkan-launch"; }

  ::llvm::StringRef getDescription() const override { return "Convert gpu.launch_func to vulkanLaunch external call"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuLaunchFuncToVulkanLaunchFunc");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuLaunchFuncToVulkanLaunchFunc"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuLaunchFuncToVulkanLaunchFuncBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC
#endif // GEN_PASS_DEF_CONVERTGPULAUNCHFUNCTOVULKANLAUNCHFUNC

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
struct ConvertGpuOpsToNVVMOpsOptions {
  unsigned indexBitwidth = 0;
  bool hasRedux = false;
  bool useOpaquePointers = true;
};
#undef GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
#endif // GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
#ifdef GEN_PASS_DEF_CONVERTGPUOPSTONVVMOPS
namespace impl {

template <typename DerivedT>
class ConvertGpuOpsToNVVMOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToNVVMOpsBase;

  ConvertGpuOpsToNVVMOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Generate NVVM operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToNVVMOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToNVVMOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<NVVM::NVVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToNVVMOpsBase<DerivedT>)

  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsOptions &options) : ConvertGpuOpsToNVVMOpsBase() {
    indexBitwidth = options.indexBitwidth;
    hasRedux = options.hasRedux;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> hasRedux{*this, "has-redux", ::llvm::cl::desc("Target gpu supports redux"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTGPUOPSTONVVMOPS
#endif // GEN_PASS_DEF_CONVERTGPUOPSTONVVMOPS

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
struct ConvertGpuOpsToROCDLOpsOptions {
  std::string chipset = "gfx000";
  unsigned indexBitwidth = 0;
  bool useBarePtrCallConv = false;
  ::mlir::gpu::amd::Runtime runtime = ::mlir::gpu::amd::Runtime::Unknown;
  bool useOpaquePointers = true;
};
#undef GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
#endif // GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
#ifdef GEN_PASS_DEF_CONVERTGPUOPSTOROCDLOPS
namespace impl {

template <typename DerivedT>
class ConvertGpuOpsToROCDLOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToROCDLOpsBase;

  ConvertGpuOpsToROCDLOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Generate ROCDL operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToROCDLOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToROCDLOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<ROCDL::ROCDLDialect>();

  registry.insert<cf::ControlFlowDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToROCDLOpsBase<DerivedT>)

  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsOptions &options) : ConvertGpuOpsToROCDLOpsBase() {
    chipset = options.chipset;
    indexBitwidth = options.indexBitwidth;
    useBarePtrCallConv = options.useBarePtrCallConv;
    runtime = options.runtime;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace memref arguments in GPU functions with bare pointers.All memrefs must have static shape"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<::mlir::gpu::amd::Runtime> runtime{*this, "runtime", ::llvm::cl::desc("Runtime code will be run on (default is Unknown, can also use HIP or OpenCl)"), ::llvm::cl::init(::mlir::gpu::amd::Runtime::Unknown), ::llvm::cl::values(
            clEnumValN(::mlir::gpu::amd::Runtime::Unknown, "unknown", "Unknown (default)"),
            clEnumValN(::mlir::gpu::amd::Runtime::HIP, "HIP", "HIP"),
            clEnumValN(::mlir::gpu::amd::Runtime::OpenCL, "OpenCL", "OpenCL")
          )};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTGPUOPSTOROCDLOPS
#endif // GEN_PASS_DEF_CONVERTGPUOPSTOROCDLOPS

//===----------------------------------------------------------------------===//
// ConvertIndexToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
struct ConvertIndexToLLVMPassOptions {
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(const ConvertIndexToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTINDEXTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(const ConvertIndexToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertIndexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertIndexToLLVMPassBase;

  ConvertIndexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertIndexToLLVMPassBase(const ConvertIndexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-index-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-index-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the `index` dialect to the `llvm` dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertIndexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertIndexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<::mlir::LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertIndexToLLVMPassBase<DerivedT>)

  ConvertIndexToLLVMPassBase(const ConvertIndexToLLVMPassOptions &options) : ConvertIndexToLLVMPassBase() {
    indexBitwidth = options.indexBitwidth;
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(const ConvertIndexToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass() {
  return impl::createConvertIndexToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(const ConvertIndexToLLVMPassOptions &options) {
  return impl::createConvertIndexToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTINDEXTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTINDEXTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertLinalgToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTLINALGTOLLVMPASS
struct ConvertLinalgToLLVMPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass(const ConvertLinalgToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTLINALGTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTLINALGTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTLINALGTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass(const ConvertLinalgToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertLinalgToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToLLVMPassBase;

  ConvertLinalgToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToLLVMPassBase(const ConvertLinalgToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToLLVMPassBase<DerivedT>)

  ConvertLinalgToLLVMPassBase(const ConvertLinalgToLLVMPassOptions &options) : ConvertLinalgToLLVMPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass(const ConvertLinalgToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass() {
  return impl::createConvertLinalgToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertLinalgToLLVMPass(const ConvertLinalgToLLVMPassOptions &options) {
  return impl::createConvertLinalgToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTLINALGTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTLINALGTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#undef GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#endif // GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTLINALGTOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertLinalgToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToStandardBase;

  ConvertLinalgToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToStandardBase(const ConvertLinalgToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTLINALGTOSTANDARD
#endif // GEN_PASS_DEF_CONVERTLINALGTOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertMathToFuncs
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOFUNCS
struct ConvertMathToFuncsOptions {
  unsigned minWidthOfFPowIExponent = 1;
  bool convertCtlz = false;
};
std::unique_ptr<::mlir::Pass> createConvertMathToFuncs();
std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(const ConvertMathToFuncsOptions &options);
#undef GEN_PASS_DECL_CONVERTMATHTOFUNCS
#endif // GEN_PASS_DECL_CONVERTMATHTOFUNCS
#ifdef GEN_PASS_DEF_CONVERTMATHTOFUNCS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToFuncs();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(const ConvertMathToFuncsOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMathToFuncsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToFuncsBase;

  ConvertMathToFuncsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToFuncsBase(const ConvertMathToFuncsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-funcs");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-funcs"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math operations to calls of outlined implementations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToFuncs");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToFuncs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<cf::ControlFlowDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<vector::VectorDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToFuncsBase<DerivedT>)

  ConvertMathToFuncsBase(const ConvertMathToFuncsOptions &options) : ConvertMathToFuncsBase() {
    minWidthOfFPowIExponent = options.minWidthOfFPowIExponent;
    convertCtlz = options.convertCtlz;
  }
protected:
  ::mlir::Pass::Option<unsigned> minWidthOfFPowIExponent{*this, "min-width-of-fpowi-exponent", ::llvm::cl::desc("Convert FPowI only if the width of its exponent's integer type is greater than or equal to this value"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> convertCtlz{*this, "convert-ctlz", ::llvm::cl::desc("Convert math.ctlz to a software implementation. Enable for targets that do not natively support ctlz."), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMathToFuncs() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(const ConvertMathToFuncsOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMathToFuncs() {
  return impl::createConvertMathToFuncs();
}

std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(const ConvertMathToFuncsOptions &options) {
  return impl::createConvertMathToFuncs(options);
}
#undef GEN_PASS_DEF_CONVERTMATHTOFUNCS
#endif // GEN_PASS_DEF_CONVERTMATHTOFUNCS

//===----------------------------------------------------------------------===//
// ConvertMathToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
struct ConvertMathToLLVMPassOptions {
  bool approximateLog1p = true;
};
std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(const ConvertMathToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTMATHTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(const ConvertMathToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMathToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToLLVMPassBase;

  ConvertMathToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLLVMPassBase(const ConvertMathToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLLVMPassBase<DerivedT>)

  ConvertMathToLLVMPassBase(const ConvertMathToLLVMPassOptions &options) : ConvertMathToLLVMPassBase() {
    approximateLog1p = options.approximateLog1p;
  }
protected:
  ::mlir::Pass::Option<bool> approximateLog1p{*this, "approximate-log1p", ::llvm::cl::desc("Enable approximation of Log1p."), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(const ConvertMathToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass() {
  return impl::createConvertMathToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(const ConvertMathToLLVMPassOptions &options) {
  return impl::createConvertMathToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTMATHTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTMATHTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertMathToLibm
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOLIBM
#undef GEN_PASS_DECL_CONVERTMATHTOLIBM
#endif // GEN_PASS_DECL_CONVERTMATHTOLIBM
#ifdef GEN_PASS_DEF_CONVERTMATHTOLIBM
namespace impl {

template <typename DerivedT>
class ConvertMathToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToLibmBase;

  ConvertMathToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLibmBase(const ConvertMathToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLibmBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMATHTOLIBM
#endif // GEN_PASS_DEF_CONVERTMATHTOLIBM

//===----------------------------------------------------------------------===//
// ConvertMathToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOSPIRV
#undef GEN_PASS_DECL_CONVERTMATHTOSPIRV
#endif // GEN_PASS_DECL_CONVERTMATHTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTMATHTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertMathToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToSPIRVBase;

  ConvertMathToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToSPIRVBase(const ConvertMathToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToSPIRVBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMATHTOSPIRV
#endif // GEN_PASS_DEF_CONVERTMATHTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertMemRefToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
struct ConvertMemRefToSPIRVOptions {
  int boolNumBits = 8;
};
#undef GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
#endif // GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTMEMREFTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertMemRefToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMemRefToSPIRVBase;

  ConvertMemRefToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToSPIRVBase(const ConvertMemRefToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMemRefToSPIRVBase<DerivedT>)

  ConvertMemRefToSPIRVBase(const ConvertMemRefToSPIRVOptions &options) : ConvertMemRefToSPIRVBase() {
    boolNumBits = options.boolNumBits;
  }
protected:
  ::mlir::Pass::Option<int> boolNumBits{*this, "bool-num-bits", ::llvm::cl::desc("The number of bits to store a boolean value"), ::llvm::cl::init(8)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMEMREFTOSPIRV
#endif // GEN_PASS_DEF_CONVERTMEMREFTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertNVGPUToNVVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
struct ConvertNVGPUToNVVMPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass();
std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass(const ConvertNVGPUToNVVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
#endif // GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
#ifdef GEN_PASS_DEF_CONVERTNVGPUTONVVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass(const ConvertNVGPUToNVVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertNVGPUToNVVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertNVGPUToNVVMPassBase;

  ConvertNVGPUToNVVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertNVGPUToNVVMPassBase(const ConvertNVGPUToNVVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-nvgpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-nvgpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert NVGPU dialect to NVVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertNVGPUToNVVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertNVGPUToNVVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<NVVM::NVVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertNVGPUToNVVMPassBase<DerivedT>)

  ConvertNVGPUToNVVMPassBase(const ConvertNVGPUToNVVMPassOptions &options) : ConvertNVGPUToNVVMPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass(const ConvertNVGPUToNVVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass() {
  return impl::createConvertNVGPUToNVVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass(const ConvertNVGPUToNVVMPassOptions &options) {
  return impl::createConvertNVGPUToNVVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTNVGPUTONVVMPASS
#endif // GEN_PASS_DEF_CONVERTNVGPUTONVVMPASS

//===----------------------------------------------------------------------===//
// ConvertOpenACCToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTOPENACCTOLLVMPASS
struct ConvertOpenACCToLLVMPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass(const ConvertOpenACCToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTOPENACCTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTOPENACCTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTOPENACCTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass(const ConvertOpenACCToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertOpenACCToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToLLVMPassBase;

  ConvertOpenACCToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToLLVMPassBase(const ConvertOpenACCToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenACCToLLVMPassBase<DerivedT>)

  ConvertOpenACCToLLVMPassBase(const ConvertOpenACCToLLVMPassOptions &options) : ConvertOpenACCToLLVMPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass(const ConvertOpenACCToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass() {
  return impl::createConvertOpenACCToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertOpenACCToLLVMPass(const ConvertOpenACCToLLVMPassOptions &options) {
  return impl::createConvertOpenACCToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTOPENACCTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTOPENACCTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTOPENACCTOSCF
#undef GEN_PASS_DECL_CONVERTOPENACCTOSCF
#endif // GEN_PASS_DECL_CONVERTOPENACCTOSCF
#ifdef GEN_PASS_DEF_CONVERTOPENACCTOSCF
namespace impl {

template <typename DerivedT>
class ConvertOpenACCToSCFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToSCFBase;

  ConvertOpenACCToSCFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToSCFBase(const ConvertOpenACCToSCFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to OpenACC with SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<acc::OpenACCDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenACCToSCFBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTOPENACCTOSCF
#endif // GEN_PASS_DEF_CONVERTOPENACCTOSCF

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass();
#undef GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTOPENMPTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertOpenMPToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenMPToLLVMPassBase;

  ConvertOpenMPToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenMPToLLVMPassBase(const ConvertOpenMPToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openmp-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openmp-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenMP ops to OpenMP ops with LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenMPToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenMPToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenMPToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass() {
  return impl::createConvertOpenMPToLLVMPass();
}
#undef GEN_PASS_DEF_CONVERTOPENMPTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTOPENMPTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#undef GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#endif // GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#ifdef GEN_PASS_DEF_CONVERTPDLTOPDLINTERP
namespace impl {

template <typename DerivedT>
class ConvertPDLToPDLInterpBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertPDLToPDLInterpBase;

  ConvertPDLToPDLInterpBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertPDLToPDLInterpBase(const ConvertPDLToPDLInterpBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-pdl-to-pdl-interp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-pdl-to-pdl-interp"; }

  ::llvm::StringRef getDescription() const override { return "Convert PDL ops to PDL interpreter ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertPDLToPDLInterp");
  }
  ::llvm::StringRef getName() const override { return "ConvertPDLToPDLInterp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<pdl_interp::PDLInterpDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertPDLToPDLInterpBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTPDLTOPDLINTERP
#endif // GEN_PASS_DEF_CONVERTPDLTOPDLINTERP

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#undef GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#endif // GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#ifdef GEN_PASS_DEF_CONVERTPARALLELLOOPTOGPU
namespace impl {

template <typename DerivedT>
class ConvertParallelLoopToGpuBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertParallelLoopToGpuBase;

  ConvertParallelLoopToGpuBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertParallelLoopToGpuBase(const ConvertParallelLoopToGpuBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-parallel-loops-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-parallel-loops-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert mapped scf.parallel ops to gpu launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertParallelLoopToGpu");
  }
  ::llvm::StringRef getName() const override { return "ConvertParallelLoopToGpu"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertParallelLoopToGpuBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTPARALLELLOOPTOGPU
#endif // GEN_PASS_DEF_CONVERTPARALLELLOOPTOGPU

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMPPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
struct ConvertSCFToOpenMPPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass();
std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(const ConvertSCFToOpenMPPassOptions &options);
#undef GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
#endif // GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
#ifdef GEN_PASS_DEF_CONVERTSCFTOOPENMPPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(const ConvertSCFToOpenMPPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertSCFToOpenMPPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSCFToOpenMPPassBase;

  ConvertSCFToOpenMPPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSCFToOpenMPPassBase(const ConvertSCFToOpenMPPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-openmp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-openmp"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF parallel loop to OpenMP parallel + workshare constructs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSCFToOpenMPPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSCFToOpenMPPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<omp::OpenMPDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSCFToOpenMPPassBase<DerivedT>)

  ConvertSCFToOpenMPPassBase(const ConvertSCFToOpenMPPassOptions &options) : ConvertSCFToOpenMPPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(const ConvertSCFToOpenMPPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass() {
  return impl::createConvertSCFToOpenMPPass();
}

std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(const ConvertSCFToOpenMPPassOptions &options) {
  return impl::createConvertSCFToOpenMPPass(options);
}
#undef GEN_PASS_DEF_CONVERTSCFTOOPENMPPASS
#endif // GEN_PASS_DEF_CONVERTSCFTOOPENMPPASS

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
struct ConvertSPIRVToLLVMPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(const ConvertSPIRVToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTSPIRVTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(const ConvertSPIRVToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertSPIRVToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSPIRVToLLVMPassBase;

  ConvertSPIRVToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSPIRVToLLVMPassBase(const ConvertSPIRVToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-spirv-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-spirv-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert SPIR-V dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSPIRVToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSPIRVToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSPIRVToLLVMPassBase<DerivedT>)

  ConvertSPIRVToLLVMPassBase(const ConvertSPIRVToLLVMPassOptions &options) : ConvertSPIRVToLLVMPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(const ConvertSPIRVToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass() {
  return impl::createConvertSPIRVToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(const ConvertSPIRVToLLVMPassOptions &options) {
  return impl::createConvertSPIRVToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTSPIRVTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTSPIRVTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#undef GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#endif // GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#ifdef GEN_PASS_DEF_CONVERTSHAPECONSTRAINTS
namespace impl {

template <typename DerivedT>
class ConvertShapeConstraintsBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertShapeConstraintsBase;

  ConvertShapeConstraintsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeConstraintsBase(const ConvertShapeConstraintsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Convert shape constraint operations to the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeConstraintsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTSHAPECONSTRAINTS
#endif // GEN_PASS_DEF_CONVERTSHAPECONSTRAINTS

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#undef GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#endif // GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTSHAPETOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertShapeToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertShapeToStandardBase;

  ConvertShapeToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeToStandardBase(const ConvertShapeToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the shape dialect into the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTSHAPETOSTANDARD
#endif // GEN_PASS_DEF_CONVERTSHAPETOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertTensorToLinalg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTENSORTOLINALG
#undef GEN_PASS_DECL_CONVERTTENSORTOLINALG
#endif // GEN_PASS_DECL_CONVERTTENSORTOLINALG
#ifdef GEN_PASS_DEF_CONVERTTENSORTOLINALG
namespace impl {

template <typename DerivedT>
class ConvertTensorToLinalgBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertTensorToLinalgBase;

  ConvertTensorToLinalgBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToLinalgBase(const ConvertTensorToLinalgBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert some Tensor dialect ops to Linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<linalg::LinalgDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToLinalgBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTENSORTOLINALG
#endif // GEN_PASS_DEF_CONVERTTENSORTOLINALG

//===----------------------------------------------------------------------===//
// ConvertTensorToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTENSORTOSPIRV
struct ConvertTensorToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTTENSORTOSPIRV
#endif // GEN_PASS_DECL_CONVERTTENSORTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTTENSORTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertTensorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertTensorToSPIRVBase;

  ConvertTensorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToSPIRVBase(const ConvertTensorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Tensor dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToSPIRVBase<DerivedT>)

  ConvertTensorToSPIRVBase(const ConvertTensorToSPIRVOptions &options) : ConvertTensorToSPIRVBase() {
    emulateLT32BitScalarTypes = options.emulateLT32BitScalarTypes;
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTENSORTOSPIRV
#endif // GEN_PASS_DEF_CONVERTTENSORTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOGPU
struct ConvertVectorToGPUOptions {
  bool useNvGpu = false;
};
#undef GEN_PASS_DECL_CONVERTVECTORTOGPU
#endif // GEN_PASS_DECL_CONVERTVECTORTOGPU
#ifdef GEN_PASS_DEF_CONVERTVECTORTOGPU
namespace impl {

template <typename DerivedT>
class ConvertVectorToGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToGPUBase;

  ConvertVectorToGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToGPUBase(const ConvertVectorToGPUBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the GPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<gpu::GPUDialect>();

  registry.insert<affine::AffineDialect>();

  registry.insert<vector::VectorDialect>();

  registry.insert<nvgpu::NVGPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToGPUBase<DerivedT>)

  ConvertVectorToGPUBase(const ConvertVectorToGPUOptions &options) : ConvertVectorToGPUBase() {
    useNvGpu = options.useNvGpu;
  }
protected:
  ::mlir::Pass::Option<bool> useNvGpu{*this, "use-nvgpu", ::llvm::cl::desc("convert to NvGPU ops instead of GPU dialect ops"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOGPU
#endif // GEN_PASS_DEF_CONVERTVECTORTOGPU

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
struct ConvertVectorToLLVMPassOptions {
  bool reassociateFPReductions = false;
  bool force32BitVectorIndices = true;
  bool amx = false;
  bool armNeon = false;
  bool armSVE = false;
  bool x86Vector = false;
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(const ConvertVectorToLLVMPassOptions &options);
#undef GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTVECTORTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(const ConvertVectorToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertVectorToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToLLVMPassBase;

  ConvertVectorToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToLLVMPassBase(const ConvertVectorToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToLLVMPassBase<DerivedT>)

  ConvertVectorToLLVMPassBase(const ConvertVectorToLLVMPassOptions &options) : ConvertVectorToLLVMPassBase() {
    reassociateFPReductions = options.reassociateFPReductions;
    force32BitVectorIndices = options.force32BitVectorIndices;
    amx = options.amx;
    armNeon = options.armNeon;
    armSVE = options.armSVE;
    x86Vector = options.x86Vector;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> reassociateFPReductions{*this, "reassociate-fp-reductions", ::llvm::cl::desc("Allows llvm to reassociate floating-point reductions for speed"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> force32BitVectorIndices{*this, "force-32bit-vector-indices", ::llvm::cl::desc("Allows compiler to assume vector indices fit in 32-bit if that yields faster code"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> amx{*this, "enable-amx", ::llvm::cl::desc("Enables the use of AMX dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armNeon{*this, "enable-arm-neon", ::llvm::cl::desc("Enables the use of ArmNeon dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armSVE{*this, "enable-arm-sve", ::llvm::cl::desc("Enables the use of ArmSVE dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> x86Vector{*this, "enable-x86vector", ::llvm::cl::desc("Enables the use of X86Vector dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(const ConvertVectorToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass() {
  return impl::createConvertVectorToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(const ConvertVectorToLLVMPassOptions &options) {
  return impl::createConvertVectorToLLVMPass(options);
}
#undef GEN_PASS_DEF_CONVERTVECTORTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTVECTORTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOSCF
struct ConvertVectorToSCFOptions {
  bool fullUnroll = false;
  unsigned targetRank = 1;
  bool lowerTensors = false;
};
#undef GEN_PASS_DECL_CONVERTVECTORTOSCF
#endif // GEN_PASS_DECL_CONVERTVECTORTOSCF
#ifdef GEN_PASS_DEF_CONVERTVECTORTOSCF
namespace impl {

template <typename DerivedT>
class ConvertVectorToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSCFBase;

  ConvertVectorToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSCFBase(const ConvertVectorToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSCFBase<DerivedT>)

  ConvertVectorToSCFBase(const ConvertVectorToSCFOptions &options) : ConvertVectorToSCFBase() {
    fullUnroll = options.fullUnroll;
    targetRank = options.targetRank;
    lowerTensors = options.lowerTensors;
  }
protected:
  ::mlir::Pass::Option<bool> fullUnroll{*this, "full-unroll", ::llvm::cl::desc("Perform full unrolling when converting vector transfers to SCF"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> targetRank{*this, "target-rank", ::llvm::cl::desc("Target vector rank to which transfer ops should be lowered"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> lowerTensors{*this, "lower-tensors", ::llvm::cl::desc("Lower transfer ops that operate on tensors"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOSCF
#endif // GEN_PASS_DEF_CONVERTVECTORTOSCF

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#undef GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#endif // GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTVECTORTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertVectorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSPIRVBase;

  ConvertVectorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSPIRVBase(const ConvertVectorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Vector dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSPIRVBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOSPIRV
#endif // GEN_PASS_DEF_CONVERTVECTORTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertVulkanLaunchFuncToVulkanCallsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS
struct ConvertVulkanLaunchFuncToVulkanCallsPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass();
std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass(const ConvertVulkanLaunchFuncToVulkanCallsPassOptions &options);
#undef GEN_PASS_DECL_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS
#endif // GEN_PASS_DECL_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS
#ifdef GEN_PASS_DEF_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass(const ConvertVulkanLaunchFuncToVulkanCallsPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertVulkanLaunchFuncToVulkanCallsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVulkanLaunchFuncToVulkanCallsPassBase;

  ConvertVulkanLaunchFuncToVulkanCallsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVulkanLaunchFuncToVulkanCallsPassBase(const ConvertVulkanLaunchFuncToVulkanCallsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("launch-func-to-vulkan");
  }
  ::llvm::StringRef getArgument() const override { return "launch-func-to-vulkan"; }

  ::llvm::StringRef getDescription() const override { return "Convert vulkanLaunch external call to Vulkan runtime external calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVulkanLaunchFuncToVulkanCallsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertVulkanLaunchFuncToVulkanCallsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVulkanLaunchFuncToVulkanCallsPassBase<DerivedT>)

  ConvertVulkanLaunchFuncToVulkanCallsPassBase(const ConvertVulkanLaunchFuncToVulkanCallsPassOptions &options) : ConvertVulkanLaunchFuncToVulkanCallsPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass(const ConvertVulkanLaunchFuncToVulkanCallsPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass() {
  return impl::createConvertVulkanLaunchFuncToVulkanCallsPass();
}

std::unique_ptr<::mlir::Pass> createConvertVulkanLaunchFuncToVulkanCallsPass(const ConvertVulkanLaunchFuncToVulkanCallsPassOptions &options) {
  return impl::createConvertVulkanLaunchFuncToVulkanCallsPass(options);
}
#undef GEN_PASS_DEF_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS
#endif // GEN_PASS_DEF_CONVERTVULKANLAUNCHFUNCTOVULKANCALLSPASS

//===----------------------------------------------------------------------===//
// FinalizeMemRefToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
struct FinalizeMemRefToLLVMConversionPassOptions {
  bool useAlignedAlloc = false;
  unsigned indexBitwidth = 0;
  bool useGenericFunctions = false;
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(const FinalizeMemRefToLLVMConversionPassOptions &options);
#undef GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_FINALIZEMEMREFTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(const FinalizeMemRefToLLVMConversionPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class FinalizeMemRefToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FinalizeMemRefToLLVMConversionPassBase;

  FinalizeMemRefToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizeMemRefToLLVMConversionPassBase(const FinalizeMemRefToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalize-memref-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "finalize-memref-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Finalize MemRef dialect to LLVM dialect conversion"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizeMemRefToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "FinalizeMemRefToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalizeMemRefToLLVMConversionPassBase<DerivedT>)

  FinalizeMemRefToLLVMConversionPassBase(const FinalizeMemRefToLLVMConversionPassOptions &options) : FinalizeMemRefToLLVMConversionPassBase() {
    useAlignedAlloc = options.useAlignedAlloc;
    indexBitwidth = options.indexBitwidth;
    useGenericFunctions = options.useGenericFunctions;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useAlignedAlloc{*this, "use-aligned-alloc", ::llvm::cl::desc("Use aligned_alloc in place of malloc for heap allocations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useGenericFunctions{*this, "use-generic-functions", ::llvm::cl::desc("Use generic allocation and deallocation functions instead of the classic 'malloc', 'aligned_alloc' and 'free' functions"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(const FinalizeMemRefToLLVMConversionPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass() {
  return impl::createFinalizeMemRefToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(const FinalizeMemRefToLLVMConversionPassOptions &options) {
  return impl::createFinalizeMemRefToLLVMConversionPass(options);
}
#undef GEN_PASS_DEF_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_FINALIZEMEMREFTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
struct GpuToLLVMConversionPassOptions {
  bool kernelBarePtrCallConv = false;
  std::string gpuBinaryAnnotation = gpu::getDefaultGpuBinaryAnnotation();
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(const GpuToLLVMConversionPassOptions &options);
#undef GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_GPUTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(const GpuToLLVMConversionPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class GpuToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuToLLVMConversionPassBase;

  GpuToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to LLVM dialect with GPU runtime calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuToLLVMConversionPassBase<DerivedT>)

  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassOptions &options) : GpuToLLVMConversionPassBase() {
    kernelBarePtrCallConv = options.kernelBarePtrCallConv;
    gpuBinaryAnnotation = options.gpuBinaryAnnotation;
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> kernelBarePtrCallConv{*this, "use-bare-pointers-for-kernels", ::llvm::cl::desc("Use bare pointers to pass memref arguments to kernels. The kernel must use the same setting for this option."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> gpuBinaryAnnotation{*this, "gpu-binary-annotation", ::llvm::cl::desc("Annotation attribute string for GPU binary"), ::llvm::cl::init(gpu::getDefaultGpuBinaryAnnotation())};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(const GpuToLLVMConversionPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass() {
  return impl::createGpuToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(const GpuToLLVMConversionPassOptions &options) {
  return impl::createGpuToLLVMConversionPass(options);
}
#undef GEN_PASS_DEF_GPUTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_GPUTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
struct LowerHostCodeToLLVMPassOptions {
  bool useOpaquePointers = true;
};
std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass();
std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass(const LowerHostCodeToLLVMPassOptions &options);
#undef GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
#endif // GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
#ifdef GEN_PASS_DEF_LOWERHOSTCODETOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass(const LowerHostCodeToLLVMPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class LowerHostCodeToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LowerHostCodeToLLVMPassBase;

  LowerHostCodeToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerHostCodeToLLVMPassBase(const LowerHostCodeToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "lower-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lowers the host module code and `gpu.launch_func` to LLVM"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerHostCodeToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "LowerHostCodeToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerHostCodeToLLVMPassBase<DerivedT>)

  LowerHostCodeToLLVMPassBase(const LowerHostCodeToLLVMPassOptions &options) : LowerHostCodeToLLVMPassBase() {
    useOpaquePointers = options.useOpaquePointers;
  }
protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass(const LowerHostCodeToLLVMPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass() {
  return impl::createLowerHostCodeToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass(const LowerHostCodeToLLVMPassOptions &options) {
  return impl::createLowerHostCodeToLLVMPass(options);
}
#undef GEN_PASS_DEF_LOWERHOSTCODETOLLVMPASS
#endif // GEN_PASS_DEF_LOWERHOSTCODETOLLVMPASS

//===----------------------------------------------------------------------===//
// MapMemRefStorageClass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
struct MapMemRefStorageClassOptions {
  std::string clientAPI = "vulkan";
};
#undef GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
#endif // GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
#ifdef GEN_PASS_DEF_MAPMEMREFSTORAGECLASS
namespace impl {

template <typename DerivedT>
class MapMemRefStorageClassBase : public ::mlir::OperationPass<> {
public:
  using Base = MapMemRefStorageClassBase;

  MapMemRefStorageClassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  MapMemRefStorageClassBase(const MapMemRefStorageClassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("map-memref-spirv-storage-class");
  }
  ::llvm::StringRef getArgument() const override { return "map-memref-spirv-storage-class"; }

  ::llvm::StringRef getDescription() const override { return "Map numeric MemRef memory spaces to SPIR-V storage classes"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MapMemRefStorageClass");
  }
  ::llvm::StringRef getName() const override { return "MapMemRefStorageClass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MapMemRefStorageClassBase<DerivedT>)

  MapMemRefStorageClassBase(const MapMemRefStorageClassOptions &options) : MapMemRefStorageClassBase() {
    clientAPI = options.clientAPI;
  }
protected:
  ::mlir::Pass::Option<std::string> clientAPI{*this, "client-api", ::llvm::cl::desc("The client API to use for populating mappings"), ::llvm::cl::init("vulkan")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_MAPMEMREFSTORAGECLASS
#endif // GEN_PASS_DEF_MAPMEMREFSTORAGECLASS

//===----------------------------------------------------------------------===//
// ReconcileUnrealizedCasts
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#undef GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#endif // GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#ifdef GEN_PASS_DEF_RECONCILEUNREALIZEDCASTS
namespace impl {

template <typename DerivedT>
class ReconcileUnrealizedCastsBase : public ::mlir::OperationPass<> {
public:
  using Base = ReconcileUnrealizedCastsBase;

  ReconcileUnrealizedCastsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ReconcileUnrealizedCastsBase(const ReconcileUnrealizedCastsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("reconcile-unrealized-casts");
  }
  ::llvm::StringRef getArgument() const override { return "reconcile-unrealized-casts"; }

  ::llvm::StringRef getDescription() const override { return "Simplify and eliminate unrealized conversion casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReconcileUnrealizedCasts");
  }
  ::llvm::StringRef getName() const override { return "ReconcileUnrealizedCasts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ReconcileUnrealizedCastsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_RECONCILEUNREALIZEDCASTS
#endif // GEN_PASS_DEF_RECONCILEUNREALIZEDCASTS

//===----------------------------------------------------------------------===//
// SCFToControlFlow
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFTOCONTROLFLOW
#undef GEN_PASS_DECL_SCFTOCONTROLFLOW
#endif // GEN_PASS_DECL_SCFTOCONTROLFLOW
#ifdef GEN_PASS_DEF_SCFTOCONTROLFLOW
namespace impl {

template <typename DerivedT>
class SCFToControlFlowBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToControlFlowBase;

  SCFToControlFlowBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToControlFlowBase(const SCFToControlFlowBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-cf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-cf"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to ControlFlow dialect, replacing structured control flow with a CFG"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToControlFlow");
  }
  ::llvm::StringRef getName() const override { return "SCFToControlFlow"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToControlFlowBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFTOCONTROLFLOW
#endif // GEN_PASS_DEF_SCFTOCONTROLFLOW

//===----------------------------------------------------------------------===//
// SCFToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFTOSPIRV
std::unique_ptr<::mlir::Pass> createSCFToSPIRV();
#undef GEN_PASS_DECL_SCFTOSPIRV
#endif // GEN_PASS_DECL_SCFTOSPIRV
#ifdef GEN_PASS_DEF_SCFTOSPIRV

namespace impl {
  std::unique_ptr<::mlir::Pass> createSCFToSPIRV();
} // namespace impl
namespace impl {

template <typename DerivedT>
class SCFToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToSPIRVBase;

  SCFToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToSPIRVBase(const SCFToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to SPIR-V dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "SCFToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToSPIRVBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createSCFToSPIRV() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createSCFToSPIRV() {
  return impl::createSCFToSPIRV();
}
#undef GEN_PASS_DEF_SCFTOSPIRV
#endif // GEN_PASS_DEF_SCFTOSPIRV

//===----------------------------------------------------------------------===//
// TosaToArith
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOARITH
struct TosaToArithOptions {
  bool includeApplyRescale = false;
  bool use32Bit = false;
};
#undef GEN_PASS_DECL_TOSATOARITH
#endif // GEN_PASS_DECL_TOSATOARITH
#ifdef GEN_PASS_DEF_TOSATOARITH
namespace impl {

template <typename DerivedT>
class TosaToArithBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToArithBase;

  TosaToArithBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToArithBase(const TosaToArithBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-arith");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-arith"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Arith dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToArith");
  }
  ::llvm::StringRef getName() const override { return "TosaToArith"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToArithBase<DerivedT>)

  TosaToArithBase(const TosaToArithOptions &options) : TosaToArithBase() {
    includeApplyRescale = options.includeApplyRescale;
    use32Bit = options.use32Bit;
  }
protected:
  ::mlir::Pass::Option<bool> includeApplyRescale{*this, "include-apply-rescale", ::llvm::cl::desc("Whether to include the lowering for tosa.apply_rescale to arith"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> use32Bit{*this, "use-32-bit", ::llvm::cl::desc("Whether to prioritze lowering to 32-bit operations"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOARITH
#endif // GEN_PASS_DEF_TOSATOARITH

//===----------------------------------------------------------------------===//
// TosaToLinalg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOLINALG
#undef GEN_PASS_DECL_TOSATOLINALG
#endif // GEN_PASS_DECL_TOSATOLINALG
#ifdef GEN_PASS_DEF_TOSATOLINALG
namespace impl {

template <typename DerivedT>
class TosaToLinalgBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgBase;

  TosaToLinalgBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgBase(const TosaToLinalgBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalg");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOLINALG
#endif // GEN_PASS_DEF_TOSATOLINALG

//===----------------------------------------------------------------------===//
// TosaToLinalgNamed
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOLINALGNAMED
#undef GEN_PASS_DECL_TOSATOLINALGNAMED
#endif // GEN_PASS_DECL_TOSATOLINALGNAMED
#ifdef GEN_PASS_DEF_TOSATOLINALGNAMED
namespace impl {

template <typename DerivedT>
class TosaToLinalgNamedBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgNamedBase;

  TosaToLinalgNamedBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgNamedBase(const TosaToLinalgNamedBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg-named");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg-named"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg named operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalgNamed");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalgNamed"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgNamedBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOLINALGNAMED
#endif // GEN_PASS_DEF_TOSATOLINALGNAMED

//===----------------------------------------------------------------------===//
// TosaToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOSCF
#undef GEN_PASS_DECL_TOSATOSCF
#endif // GEN_PASS_DECL_TOSATOSCF
#ifdef GEN_PASS_DEF_TOSATOSCF
namespace impl {

template <typename DerivedT>
class TosaToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToSCFBase;

  TosaToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToSCFBase(const TosaToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToSCF");
  }
  ::llvm::StringRef getName() const override { return "TosaToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect, scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToSCFBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOSCF
#endif // GEN_PASS_DEF_TOSATOSCF

//===----------------------------------------------------------------------===//
// TosaToTensor
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOTENSOR
#undef GEN_PASS_DECL_TOSATOTENSOR
#endif // GEN_PASS_DECL_TOSATOTENSOR
#ifdef GEN_PASS_DEF_TOSATOTENSOR
namespace impl {

template <typename DerivedT>
class TosaToTensorBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToTensorBase;

  TosaToTensorBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToTensorBase(const TosaToTensorBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-tensor");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-tensor"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Tensor dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToTensor");
  }
  ::llvm::StringRef getName() const override { return "TosaToTensor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToTensorBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOTENSOR
#endif // GEN_PASS_DEF_TOSATOTENSOR
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ArithToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerArithToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAMDGPUToROCDL Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAMDGPUToROCDL() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertAMDGPUToROCDLPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAMDGPUToROCDLPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertAMDGPUToROCDLPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineForToGPU() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineForToGPUPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAffineForToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineForToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerAffinePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAffineToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerAffinePass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArithToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArithToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createConvertArithToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArithToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createConvertArithToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArmNeon2dToIntr() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmNeon2dToIntrPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArmNeon2dToIntrPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmNeon2dToIntrPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAsyncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertAsyncToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAsyncToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertAsyncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertBufferizationToMemRef Registration
//===----------------------------------------------------------------------===//

inline void registerConvertBufferizationToMemRef() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferizationToMemRefPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertBufferizationToMemRefPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferizationToMemRefPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToLibm Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToLibm() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToLibmPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToLibmPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToLibmPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToSPIRVPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToSPIRVPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToStandardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertControlFlowToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertControlFlowToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertControlFlowToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertControlFlowToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertControlFlowToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertControlFlowToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertControlFlowToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertControlFlowToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertControlFlowToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertControlFlowToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertFuncToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertFuncToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertFuncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertFuncToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertFuncToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertFuncToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGPUToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGPUToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGPUToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGPUToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuLaunchFuncToVulkanLaunchFunc Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuLaunchFuncToVulkanLaunchFunc() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGpuLaunchFuncToVulkanLaunchFuncPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGpuLaunchFuncToVulkanLaunchFuncPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGpuLaunchFuncToVulkanLaunchFuncPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToNVVMOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToNVVMOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGpuOpsToNVVMOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToNVVMOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToROCDLOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToROCDLOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGpuOpsToROCDLOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToROCDLOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertIndexToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertIndexToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertIndexToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertIndexToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertIndexToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertLinalgToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToStandardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertLinalgToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToFuncs Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToFuncs() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToFuncs();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToFuncsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToFuncs();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLibm Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLibm() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLibmPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToLibmPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLibmPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMemRefToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMemRefToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMemRefToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMemRefToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMemRefToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertNVGPUToNVVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertNVGPUToNVVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertNVGPUToNVVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertNVGPUToNVVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertNVGPUToNVVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertOpenACCToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertOpenACCToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertOpenACCToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToSCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertOpenACCToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenMPToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertOpenMPToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertOpenMPToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertOpenMPToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp Registration
//===----------------------------------------------------------------------===//

inline void registerConvertPDLToPDLInterp() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPDLToPDLInterpPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertPDLToPDLInterpPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPDLToPDLInterpPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu Registration
//===----------------------------------------------------------------------===//

inline void registerConvertParallelLoopToGpu() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopToGpuPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertParallelLoopToGpuPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopToGpuPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMPPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSCFToOpenMPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSCFToOpenMPPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertSCFToOpenMPPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSCFToOpenMPPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSPIRVToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSPIRVToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertSPIRVToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSPIRVToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeConstraints() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeConstraintsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertShapeConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeToStandardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertShapeToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertTensorToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTensorToLinalg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToLinalgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertTensorToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertTensorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTensorToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertTensorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToGPU() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToGPUPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertVectorToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertVectorToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVulkanLaunchFuncToVulkanCallsPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVulkanLaunchFuncToVulkanCallsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertVulkanLaunchFuncToVulkanCallsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVulkanLaunchFuncToVulkanCallsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertVulkanLaunchFuncToVulkanCallsPass();
  });
}

//===----------------------------------------------------------------------===//
// FinalizeMemRefToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerFinalizeMemRefToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFinalizeMemRefToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFinalizeMemRefToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFinalizeMemRefToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createGpuToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGpuToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createGpuToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerHostCodeToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerHostCodeToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerHostCodeToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerHostCodeToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// MapMemRefStorageClass Registration
//===----------------------------------------------------------------------===//

inline void registerMapMemRefStorageClass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createMapMemRefStorageClassPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMapMemRefStorageClassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createMapMemRefStorageClassPass();
  });
}

//===----------------------------------------------------------------------===//
// ReconcileUnrealizedCasts Registration
//===----------------------------------------------------------------------===//

inline void registerReconcileUnrealizedCasts() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createReconcileUnrealizedCastsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerReconcileUnrealizedCastsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createReconcileUnrealizedCastsPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToControlFlow Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToControlFlow() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFToControlFlowPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToCFPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSCFToSPIRV();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSCFToSPIRV();
  });
}

//===----------------------------------------------------------------------===//
// TosaToArith Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToArith() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToArith();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToArithPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToArith();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalg();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalg();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalgNamed Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalgNamed() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalgNamed();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToLinalgNamedPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalgNamed();
  });
}

//===----------------------------------------------------------------------===//
// TosaToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToSCF();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToSCF();
  });
}

//===----------------------------------------------------------------------===//
// TosaToTensor Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToTensor() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToTensor();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToTensorPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToTensor();
  });
}

//===----------------------------------------------------------------------===//
// Conversion Registration
//===----------------------------------------------------------------------===//

inline void registerConversionPasses() {
  registerArithToLLVMConversionPass();
  registerConvertAMDGPUToROCDL();
  registerConvertAffineForToGPU();
  registerConvertAffineToStandard();
  registerConvertArithToSPIRV();
  registerConvertArmNeon2dToIntr();
  registerConvertAsyncToLLVMPass();
  registerConvertBufferizationToMemRef();
  registerConvertComplexToLLVMPass();
  registerConvertComplexToLibm();
  registerConvertComplexToSPIRVPass();
  registerConvertComplexToStandard();
  registerConvertControlFlowToLLVMPass();
  registerConvertControlFlowToSPIRV();
  registerConvertFuncToLLVMPass();
  registerConvertFuncToSPIRV();
  registerConvertGPUToSPIRV();
  registerConvertGpuLaunchFuncToVulkanLaunchFunc();
  registerConvertGpuOpsToNVVMOps();
  registerConvertGpuOpsToROCDLOps();
  registerConvertIndexToLLVMPass();
  registerConvertLinalgToLLVMPass();
  registerConvertLinalgToStandard();
  registerConvertMathToFuncs();
  registerConvertMathToLLVMPass();
  registerConvertMathToLibm();
  registerConvertMathToSPIRV();
  registerConvertMemRefToSPIRV();
  registerConvertNVGPUToNVVMPass();
  registerConvertOpenACCToLLVMPass();
  registerConvertOpenACCToSCF();
  registerConvertOpenMPToLLVMPass();
  registerConvertPDLToPDLInterp();
  registerConvertParallelLoopToGpu();
  registerConvertSCFToOpenMPPass();
  registerConvertSPIRVToLLVMPass();
  registerConvertShapeConstraints();
  registerConvertShapeToStandard();
  registerConvertTensorToLinalg();
  registerConvertTensorToSPIRV();
  registerConvertVectorToGPU();
  registerConvertVectorToLLVMPass();
  registerConvertVectorToSCF();
  registerConvertVectorToSPIRV();
  registerConvertVulkanLaunchFuncToVulkanCallsPass();
  registerFinalizeMemRefToLLVMConversionPass();
  registerGpuToLLVMConversionPass();
  registerLowerHostCodeToLLVMPass();
  registerMapMemRefStorageClass();
  registerReconcileUnrealizedCasts();
  registerSCFToControlFlow();
  registerSCFToSPIRV();
  registerTosaToArith();
  registerTosaToLinalg();
  registerTosaToLinalgNamed();
  registerTosaToSCF();
  registerTosaToTensor();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ArithToLLVMConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToLLVMConversionPassBase;

  ArithToLLVMConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToLLVMConversionPassBase(const ArithToLLVMConversionPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertAMDGPUToROCDLBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAMDGPUToROCDLBase;

  ConvertAMDGPUToROCDLBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAMDGPUToROCDLBase(const ConvertAMDGPUToROCDLBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-amdgpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-amdgpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Convert AMDGPU dialect to ROCDL dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAMDGPUToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertAMDGPUToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  registry.insert<ROCDL::ROCDLDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAMDGPUToROCDLBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
};

template <typename DerivedT>
class ConvertAffineForToGPUBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertAffineForToGPUBase;

  ConvertAffineForToGPUBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineForToGPUBase(const ConvertAffineForToGPUBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-affine-for-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-affine-for-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert top-level AffineFor Ops to GPU kernels"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineForToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineForToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineForToGPUBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> numBlockDims{*this, "gpu-block-dims", ::llvm::cl::desc("Number of GPU block dimensions for mapping"), ::llvm::cl::init(1u)};
  ::mlir::Pass::Option<unsigned> numThreadDims{*this, "gpu-thread-dims", ::llvm::cl::desc("Number of GPU thread dimensions for mapping"), ::llvm::cl::init(1u)};
};

template <typename DerivedT>
class ConvertAffineToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAffineToStandardBase;

  ConvertAffineToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineToStandardBase(const ConvertAffineToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lower-affine"; }

  ::llvm::StringRef getDescription() const override { return "Lower Affine operations to a combination of Standard and SCF operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertArithToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithToSPIRVBase;

  ConvertArithToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithToSPIRVBase(const ConvertArithToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArithToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableFastMath{*this, "enable-fast-math", ::llvm::cl::desc("Enable fast math mode (assuming no NaN and infinity for floating point values) when performing conversion"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertArmNeon2dToIntrBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmNeon2dToIntrBase;

  ConvertArmNeon2dToIntrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmNeon2dToIntrBase(const ConvertArmNeon2dToIntrBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-neon-2d-to-intr");
  }
  ::llvm::StringRef getArgument() const override { return "arm-neon-2d-to-intr"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arm NEON structured ops to intrinsics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmNeon2dToIntr");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmNeon2dToIntr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arm_neon::ArmNeonDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmNeon2dToIntrBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertAsyncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertAsyncToLLVMPassBase;

  ConvertAsyncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAsyncToLLVMPassBase(const ConvertAsyncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-async-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-async-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the async dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAsyncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertAsyncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<async::AsyncDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAsyncToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertBufferizationToMemRefBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertBufferizationToMemRefBase;

  ConvertBufferizationToMemRefBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertBufferizationToMemRefBase(const ConvertBufferizationToMemRefBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-bufferization-to-memref");
  }
  ::llvm::StringRef getArgument() const override { return "convert-bufferization-to-memref"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the Bufferization dialect to the MemRef dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertBufferizationToMemRef");
  }
  ::llvm::StringRef getName() const override { return "ConvertBufferizationToMemRef"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertBufferizationToMemRefBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToLLVMPassBase;

  ConvertComplexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLLVMPassBase(const ConvertComplexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertComplexToLibmBase;

  ConvertComplexToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLibmBase(const ConvertComplexToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLibmBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToSPIRVPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToSPIRVPassBase;

  ConvertComplexToSPIRVPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToSPIRVPassBase(const ConvertComplexToSPIRVPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to SPIRV dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToSPIRVPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToSPIRVPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToSPIRVPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToStandardBase;

  ConvertComplexToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToStandardBase(const ConvertComplexToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<math::MathDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertControlFlowToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertControlFlowToLLVMPassBase;

  ConvertControlFlowToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToLLVMPassBase(const ConvertControlFlowToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow operations to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertControlFlowToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertControlFlowToSPIRVBase;

  ConvertControlFlowToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToSPIRVBase(const ConvertControlFlowToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertFuncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToLLVMPassBase;

  ConvertFuncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToLLVMPassBase(const ConvertFuncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert from the Func dialect to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace FuncOp's MemRef arguments with bare pointers to the MemRef element types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<std::string> dataLayout{*this, "data-layout", ::llvm::cl::desc("String description (LLVM format) of the data layout that is expected on the produced module"), ::llvm::cl::init("")};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertFuncToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertFuncToSPIRVBase;

  ConvertFuncToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToSPIRVBase(const ConvertFuncToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertGPUToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGPUToSPIRVBase;

  ConvertGPUToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGPUToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertGPUToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGPUToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertGpuLaunchFuncToVulkanLaunchFuncBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGpuLaunchFuncToVulkanLaunchFuncBase;

  ConvertGpuLaunchFuncToVulkanLaunchFuncBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuLaunchFuncToVulkanLaunchFuncBase(const ConvertGpuLaunchFuncToVulkanLaunchFuncBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-launch-to-vulkan-launch");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-launch-to-vulkan-launch"; }

  ::llvm::StringRef getDescription() const override { return "Convert gpu.launch_func to vulkanLaunch external call"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuLaunchFuncToVulkanLaunchFunc");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuLaunchFuncToVulkanLaunchFunc"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuLaunchFuncToVulkanLaunchFuncBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertGpuOpsToNVVMOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToNVVMOpsBase;

  ConvertGpuOpsToNVVMOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Generate NVVM operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToNVVMOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToNVVMOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<NVVM::NVVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToNVVMOpsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> hasRedux{*this, "has-redux", ::llvm::cl::desc("Target gpu supports redux"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertGpuOpsToROCDLOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToROCDLOpsBase;

  ConvertGpuOpsToROCDLOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Generate ROCDL operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToROCDLOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToROCDLOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<ROCDL::ROCDLDialect>();

  registry.insert<cf::ControlFlowDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToROCDLOpsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace memref arguments in GPU functions with bare pointers.All memrefs must have static shape"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<::mlir::gpu::amd::Runtime> runtime{*this, "runtime", ::llvm::cl::desc("Runtime code will be run on (default is Unknown, can also use HIP or OpenCl)"), ::llvm::cl::init(::mlir::gpu::amd::Runtime::Unknown), ::llvm::cl::values(
            clEnumValN(::mlir::gpu::amd::Runtime::Unknown, "unknown", "Unknown (default)"),
            clEnumValN(::mlir::gpu::amd::Runtime::HIP, "HIP", "HIP"),
            clEnumValN(::mlir::gpu::amd::Runtime::OpenCL, "OpenCL", "OpenCL")
          )};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertIndexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertIndexToLLVMPassBase;

  ConvertIndexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertIndexToLLVMPassBase(const ConvertIndexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-index-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-index-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the `index` dialect to the `llvm` dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertIndexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertIndexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<::mlir::LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertIndexToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertLinalgToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToLLVMPassBase;

  ConvertLinalgToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToLLVMPassBase(const ConvertLinalgToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertLinalgToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToStandardBase;

  ConvertLinalgToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToStandardBase(const ConvertLinalgToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMathToFuncsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToFuncsBase;

  ConvertMathToFuncsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToFuncsBase(const ConvertMathToFuncsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-funcs");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-funcs"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math operations to calls of outlined implementations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToFuncs");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToFuncs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<cf::ControlFlowDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<vector::VectorDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToFuncsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> minWidthOfFPowIExponent{*this, "min-width-of-fpowi-exponent", ::llvm::cl::desc("Convert FPowI only if the width of its exponent's integer type is greater than or equal to this value"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> convertCtlz{*this, "convert-ctlz", ::llvm::cl::desc("Convert math.ctlz to a software implementation. Enable for targets that do not natively support ctlz."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertMathToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToLLVMPassBase;

  ConvertMathToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLLVMPassBase(const ConvertMathToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> approximateLog1p{*this, "approximate-log1p", ::llvm::cl::desc("Enable approximation of Log1p."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertMathToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToLibmBase;

  ConvertMathToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLibmBase(const ConvertMathToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<vector::VectorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLibmBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMathToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToSPIRVBase;

  ConvertMathToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToSPIRVBase(const ConvertMathToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToSPIRVBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMemRefToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMemRefToSPIRVBase;

  ConvertMemRefToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToSPIRVBase(const ConvertMemRefToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMemRefToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int> boolNumBits{*this, "bool-num-bits", ::llvm::cl::desc("The number of bits to store a boolean value"), ::llvm::cl::init(8)};
};

template <typename DerivedT>
class ConvertNVGPUToNVVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertNVGPUToNVVMPassBase;

  ConvertNVGPUToNVVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertNVGPUToNVVMPassBase(const ConvertNVGPUToNVVMPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-nvgpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-nvgpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert NVGPU dialect to NVVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertNVGPUToNVVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertNVGPUToNVVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<NVVM::NVVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertNVGPUToNVVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertOpenACCToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToLLVMPassBase;

  ConvertOpenACCToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToLLVMPassBase(const ConvertOpenACCToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenACCToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertOpenACCToSCFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToSCFBase;

  ConvertOpenACCToSCFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToSCFBase(const ConvertOpenACCToSCFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to OpenACC with SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<acc::OpenACCDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenACCToSCFBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertOpenMPToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenMPToLLVMPassBase;

  ConvertOpenMPToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenMPToLLVMPassBase(const ConvertOpenMPToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openmp-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openmp-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenMP ops to OpenMP ops with LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenMPToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenMPToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenMPToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertPDLToPDLInterpBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertPDLToPDLInterpBase;

  ConvertPDLToPDLInterpBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertPDLToPDLInterpBase(const ConvertPDLToPDLInterpBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-pdl-to-pdl-interp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-pdl-to-pdl-interp"; }

  ::llvm::StringRef getDescription() const override { return "Convert PDL ops to PDL interpreter ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertPDLToPDLInterp");
  }
  ::llvm::StringRef getName() const override { return "ConvertPDLToPDLInterp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<pdl_interp::PDLInterpDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertPDLToPDLInterpBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertParallelLoopToGpuBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertParallelLoopToGpuBase;

  ConvertParallelLoopToGpuBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertParallelLoopToGpuBase(const ConvertParallelLoopToGpuBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-parallel-loops-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-parallel-loops-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert mapped scf.parallel ops to gpu launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertParallelLoopToGpu");
  }
  ::llvm::StringRef getName() const override { return "ConvertParallelLoopToGpu"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<gpu::GPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertParallelLoopToGpuBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertSCFToOpenMPPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSCFToOpenMPPassBase;

  ConvertSCFToOpenMPPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSCFToOpenMPPassBase(const ConvertSCFToOpenMPPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-openmp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-openmp"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF parallel loop to OpenMP parallel + workshare constructs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSCFToOpenMPPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSCFToOpenMPPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<omp::OpenMPDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSCFToOpenMPPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertSPIRVToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSPIRVToLLVMPassBase;

  ConvertSPIRVToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSPIRVToLLVMPassBase(const ConvertSPIRVToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-spirv-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-spirv-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert SPIR-V dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSPIRVToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSPIRVToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSPIRVToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertShapeConstraintsBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertShapeConstraintsBase;

  ConvertShapeConstraintsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeConstraintsBase(const ConvertShapeConstraintsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Convert shape constraint operations to the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeConstraintsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertShapeToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertShapeToStandardBase;

  ConvertShapeToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeToStandardBase(const ConvertShapeToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the shape dialect into the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertTensorToLinalgBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertTensorToLinalgBase;

  ConvertTensorToLinalgBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToLinalgBase(const ConvertTensorToLinalgBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert some Tensor dialect ops to Linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<linalg::LinalgDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToLinalgBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertTensorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertTensorToSPIRVBase;

  ConvertTensorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToSPIRVBase(const ConvertTensorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Tensor dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertVectorToGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToGPUBase;

  ConvertVectorToGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToGPUBase(const ConvertVectorToGPUBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the GPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<gpu::GPUDialect>();

  registry.insert<affine::AffineDialect>();

  registry.insert<vector::VectorDialect>();

  registry.insert<nvgpu::NVGPUDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToGPUBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useNvGpu{*this, "use-nvgpu", ::llvm::cl::desc("convert to NvGPU ops instead of GPU dialect ops"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertVectorToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToLLVMPassBase;

  ConvertVectorToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToLLVMPassBase(const ConvertVectorToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> reassociateFPReductions{*this, "reassociate-fp-reductions", ::llvm::cl::desc("Allows llvm to reassociate floating-point reductions for speed"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> force32BitVectorIndices{*this, "force-32bit-vector-indices", ::llvm::cl::desc("Allows compiler to assume vector indices fit in 32-bit if that yields faster code"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> amx{*this, "enable-amx", ::llvm::cl::desc("Enables the use of AMX dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armNeon{*this, "enable-arm-neon", ::llvm::cl::desc("Enables the use of ArmNeon dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armSVE{*this, "enable-arm-sve", ::llvm::cl::desc("Enables the use of ArmSVE dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> x86Vector{*this, "enable-x86vector", ::llvm::cl::desc("Enables the use of X86Vector dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertVectorToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSCFBase;

  ConvertVectorToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSCFBase(const ConvertVectorToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<affine::AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSCFBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> fullUnroll{*this, "full-unroll", ::llvm::cl::desc("Perform full unrolling when converting vector transfers to SCF"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> targetRank{*this, "target-rank", ::llvm::cl::desc("Target vector rank to which transfer ops should be lowered"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> lowerTensors{*this, "lower-tensors", ::llvm::cl::desc("Lower transfer ops that operate on tensors"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertVectorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSPIRVBase;

  ConvertVectorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSPIRVBase(const ConvertVectorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Vector dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSPIRVBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertVulkanLaunchFuncToVulkanCallsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVulkanLaunchFuncToVulkanCallsPassBase;

  ConvertVulkanLaunchFuncToVulkanCallsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVulkanLaunchFuncToVulkanCallsPassBase(const ConvertVulkanLaunchFuncToVulkanCallsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("launch-func-to-vulkan");
  }
  ::llvm::StringRef getArgument() const override { return "launch-func-to-vulkan"; }

  ::llvm::StringRef getDescription() const override { return "Convert vulkanLaunch external call to Vulkan runtime external calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVulkanLaunchFuncToVulkanCallsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertVulkanLaunchFuncToVulkanCallsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVulkanLaunchFuncToVulkanCallsPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class FinalizeMemRefToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FinalizeMemRefToLLVMConversionPassBase;

  FinalizeMemRefToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizeMemRefToLLVMConversionPassBase(const FinalizeMemRefToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalize-memref-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "finalize-memref-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Finalize MemRef dialect to LLVM dialect conversion"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizeMemRefToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "FinalizeMemRefToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalizeMemRefToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useAlignedAlloc{*this, "use-aligned-alloc", ::llvm::cl::desc("Use aligned_alloc in place of malloc for heap allocations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useGenericFunctions{*this, "use-generic-functions", ::llvm::cl::desc("Use generic allocation and deallocation functions instead of the classic 'malloc', 'aligned_alloc' and 'free' functions"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class GpuToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuToLLVMConversionPassBase;

  GpuToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to LLVM dialect with GPU runtime calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  registry.insert<memref::MemRefDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> kernelBarePtrCallConv{*this, "use-bare-pointers-for-kernels", ::llvm::cl::desc("Use bare pointers to pass memref arguments to kernels. The kernel must use the same setting for this option."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> gpuBinaryAnnotation{*this, "gpu-binary-annotation", ::llvm::cl::desc("Annotation attribute string for GPU binary"), ::llvm::cl::init(gpu::getDefaultGpuBinaryAnnotation())};
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class LowerHostCodeToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LowerHostCodeToLLVMPassBase;

  LowerHostCodeToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerHostCodeToLLVMPassBase(const LowerHostCodeToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "lower-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lowers the host module code and `gpu.launch_func` to LLVM"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerHostCodeToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "LowerHostCodeToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerHostCodeToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useOpaquePointers{*this, "use-opaque-pointers", ::llvm::cl::desc("Generate LLVM IR using opaque pointers instead of typed pointers"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class MapMemRefStorageClassBase : public ::mlir::OperationPass<> {
public:
  using Base = MapMemRefStorageClassBase;

  MapMemRefStorageClassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  MapMemRefStorageClassBase(const MapMemRefStorageClassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("map-memref-spirv-storage-class");
  }
  ::llvm::StringRef getArgument() const override { return "map-memref-spirv-storage-class"; }

  ::llvm::StringRef getDescription() const override { return "Map numeric MemRef memory spaces to SPIR-V storage classes"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MapMemRefStorageClass");
  }
  ::llvm::StringRef getName() const override { return "MapMemRefStorageClass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MapMemRefStorageClassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> clientAPI{*this, "client-api", ::llvm::cl::desc("The client API to use for populating mappings"), ::llvm::cl::init("vulkan")};
};

template <typename DerivedT>
class ReconcileUnrealizedCastsBase : public ::mlir::OperationPass<> {
public:
  using Base = ReconcileUnrealizedCastsBase;

  ReconcileUnrealizedCastsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ReconcileUnrealizedCastsBase(const ReconcileUnrealizedCastsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("reconcile-unrealized-casts");
  }
  ::llvm::StringRef getArgument() const override { return "reconcile-unrealized-casts"; }

  ::llvm::StringRef getDescription() const override { return "Simplify and eliminate unrealized conversion casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReconcileUnrealizedCasts");
  }
  ::llvm::StringRef getName() const override { return "ReconcileUnrealizedCasts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ReconcileUnrealizedCastsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFToControlFlowBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToControlFlowBase;

  SCFToControlFlowBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToControlFlowBase(const SCFToControlFlowBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-cf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-cf"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to ControlFlow dialect, replacing structured control flow with a CFG"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToControlFlow");
  }
  ::llvm::StringRef getName() const override { return "SCFToControlFlow"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToControlFlowBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToSPIRVBase;

  SCFToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToSPIRVBase(const SCFToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to SPIR-V dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "SCFToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToSPIRVBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaToArithBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToArithBase;

  TosaToArithBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToArithBase(const TosaToArithBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-arith");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-arith"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Arith dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToArith");
  }
  ::llvm::StringRef getName() const override { return "TosaToArith"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToArithBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> includeApplyRescale{*this, "include-apply-rescale", ::llvm::cl::desc("Whether to include the lowering for tosa.apply_rescale to arith"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> use32Bit{*this, "use-32-bit", ::llvm::cl::desc("Whether to prioritze lowering to 32-bit operations"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TosaToLinalgBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgBase;

  TosaToLinalgBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgBase(const TosaToLinalgBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalg");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaToLinalgNamedBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgNamedBase;

  TosaToLinalgNamedBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgNamedBase(const TosaToLinalgNamedBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg-named");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg-named"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg named operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalgNamed");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalgNamed"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgNamedBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToSCFBase;

  TosaToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToSCFBase(const TosaToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToSCF");
  }
  ::llvm::StringRef getName() const override { return "TosaToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect, scf::SCFDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToSCFBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaToTensorBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToTensorBase;

  TosaToTensorBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToTensorBase(const TosaToTensorBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-tensor");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-tensor"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Tensor dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToTensor");
  }
  ::llvm::StringRef getName() const override { return "TosaToTensor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToTensorBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
