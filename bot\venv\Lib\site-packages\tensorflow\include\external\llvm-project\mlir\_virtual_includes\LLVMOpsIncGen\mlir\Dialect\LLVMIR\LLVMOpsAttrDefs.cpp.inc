/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* AttrDef Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifdef GET_ATTRDEF_LIST
#undef GET_ATTRDEF_LIST

::mlir::LLVM::CConvAttr,
::mlir::LLVM::DIBasicTypeAttr,
::mlir::LLVM::DICompileUnitAttr,
::mlir::LLVM::DICompositeTypeAttr,
::mlir::LLVM::DIDerivedTypeAttr,
::mlir::LLVM::DIFileAttr,
::mlir::LLVM::DILexicalBlockAttr,
::mlir::LLVM::DILexicalBlockFileAttr,
::mlir::LLVM::DILocalVariableAttr,
::mlir::LLVM::DINamespaceAttr,
::mlir::LLVM::DINullTypeAttr,
::mlir::LLVM::DISubprogramAttr,
::mlir::LLVM::DISubrangeAttr,
::mlir::LLVM::DISubroutineTypeAttr,
::mlir::LLVM::FastmathFlagsAttr,
::mlir::LLVM::MemoryEffectsAttr,
::mlir::LLVM::LinkageAttr,
::mlir::LLVM::LoopAnnotationAttr,
::mlir::LLVM::LoopDistributeAttr,
::mlir::LLVM::LoopInterleaveAttr,
::mlir::LLVM::LoopLICMAttr,
::mlir::LLVM::LoopPeeledAttr,
::mlir::LLVM::LoopPipelineAttr,
::mlir::LLVM::LoopUnrollAndJamAttr,
::mlir::LLVM::LoopUnrollAttr,
::mlir::LLVM::LoopUnswitchAttr,
::mlir::LLVM::LoopVectorizeAttr

#endif  // GET_ATTRDEF_LIST

#ifdef GET_ATTRDEF_CLASSES
#undef GET_ATTRDEF_CLASSES

static ::mlir::OptionalParseResult generatedAttributeParser(::mlir::AsmParser &parser, ::llvm::StringRef *mnemonic, ::mlir::Type type, ::mlir::Attribute &value) {
  return ::mlir::AsmParser::KeywordSwitch<::mlir::OptionalParseResult>(parser)
    .Case(::mlir::LLVM::CConvAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::CConvAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DIBasicTypeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DIBasicTypeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DICompileUnitAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DICompileUnitAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DICompositeTypeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DICompositeTypeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DIDerivedTypeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DIDerivedTypeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DIFileAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DIFileAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DILexicalBlockAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DILexicalBlockAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DILexicalBlockFileAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DILexicalBlockFileAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DILocalVariableAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DILocalVariableAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DINamespaceAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DINamespaceAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DINullTypeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DINullTypeAttr::get(parser.getContext());
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DISubprogramAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DISubprogramAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DISubrangeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DISubrangeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::DISubroutineTypeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::DISubroutineTypeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::FastmathFlagsAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::FastmathFlagsAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::MemoryEffectsAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::MemoryEffectsAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LinkageAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LinkageAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopAnnotationAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopAnnotationAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopDistributeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopDistributeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopInterleaveAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopInterleaveAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopLICMAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopLICMAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopPeeledAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopPeeledAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopPipelineAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopPipelineAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopUnrollAndJamAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopUnrollAndJamAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopUnrollAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopUnrollAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopUnswitchAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopUnswitchAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Case(::mlir::LLVM::LoopVectorizeAttr::getMnemonic(), [&](llvm::StringRef, llvm::SMLoc) {
      value = ::mlir::LLVM::LoopVectorizeAttr::parse(parser, type);
      return ::mlir::success(!!value);
    })
    .Default([&](llvm::StringRef keyword, llvm::SMLoc) {
      *mnemonic = keyword;
      return std::nullopt;
    });
}

static ::mlir::LogicalResult generatedAttributePrinter(::mlir::Attribute def, ::mlir::AsmPrinter &printer) {
  return ::llvm::TypeSwitch<::mlir::Attribute, ::mlir::LogicalResult>(def)    .Case<::mlir::LLVM::CConvAttr>([&](auto t) {
      printer << ::mlir::LLVM::CConvAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DIBasicTypeAttr>([&](auto t) {
      printer << ::mlir::LLVM::DIBasicTypeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DICompileUnitAttr>([&](auto t) {
      printer << ::mlir::LLVM::DICompileUnitAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DICompositeTypeAttr>([&](auto t) {
      printer << ::mlir::LLVM::DICompositeTypeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DIDerivedTypeAttr>([&](auto t) {
      printer << ::mlir::LLVM::DIDerivedTypeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DIFileAttr>([&](auto t) {
      printer << ::mlir::LLVM::DIFileAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DILexicalBlockAttr>([&](auto t) {
      printer << ::mlir::LLVM::DILexicalBlockAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DILexicalBlockFileAttr>([&](auto t) {
      printer << ::mlir::LLVM::DILexicalBlockFileAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DILocalVariableAttr>([&](auto t) {
      printer << ::mlir::LLVM::DILocalVariableAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DINamespaceAttr>([&](auto t) {
      printer << ::mlir::LLVM::DINamespaceAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DINullTypeAttr>([&](auto t) {
      printer << ::mlir::LLVM::DINullTypeAttr::getMnemonic();
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DISubprogramAttr>([&](auto t) {
      printer << ::mlir::LLVM::DISubprogramAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DISubrangeAttr>([&](auto t) {
      printer << ::mlir::LLVM::DISubrangeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::DISubroutineTypeAttr>([&](auto t) {
      printer << ::mlir::LLVM::DISubroutineTypeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::FastmathFlagsAttr>([&](auto t) {
      printer << ::mlir::LLVM::FastmathFlagsAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::MemoryEffectsAttr>([&](auto t) {
      printer << ::mlir::LLVM::MemoryEffectsAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LinkageAttr>([&](auto t) {
      printer << ::mlir::LLVM::LinkageAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopAnnotationAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopAnnotationAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopDistributeAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopDistributeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopInterleaveAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopInterleaveAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopLICMAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopLICMAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopPeeledAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopPeeledAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopPipelineAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopPipelineAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopUnrollAndJamAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopUnrollAndJamAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopUnrollAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopUnrollAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopUnswitchAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopUnswitchAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Case<::mlir::LLVM::LoopVectorizeAttr>([&](auto t) {
      printer << ::mlir::LLVM::LoopVectorizeAttr::getMnemonic();
t.print(printer);
      return ::mlir::success();
    })
    .Default([](auto) { return ::mlir::failure(); });
}

namespace mlir {
namespace LLVM {
namespace detail {
struct CConvAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<CConv>;
  CConvAttrStorage(CConv CallingConv) : CallingConv(CallingConv) {}

  KeyTy getAsKey() const {
    return KeyTy(CallingConv);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (CallingConv == std::get<0>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey));
  }

  static CConvAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto CallingConv = std::get<0>(tblgenKey);
    return new (allocator.allocate<CConvAttrStorage>()) CConvAttrStorage(CallingConv);
  }

  CConv CallingConv;
};
} // namespace detail
CConvAttr CConvAttr::get(::mlir::MLIRContext *context, CConv CallingConv) {
  return Base::get(context, CallingConv);
}

::mlir::Attribute CConvAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<CConv> _result_CallingConv;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};

  // Parse variable 'CallingConv'
  _result_CallingConv = ::mlir::FieldParser<CConv>::parse(odsParser);
  if (::mlir::failed(_result_CallingConv)) {
    odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse CConvAttr parameter 'CallingConv' which is to be a `CConv`");
    return {};
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_CallingConv));
  return CConvAttr::get(odsParser.getContext(),
      CConv((*_result_CallingConv)));
}

void CConvAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  odsPrinter.printStrippedAttrOrType(getCallingConv());
  odsPrinter << ">";
}

CConv CConvAttr::getCallingConv() const {
  return getImpl()->CallingConv;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::CConvAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DIBasicTypeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<unsigned, StringAttr, uint64_t, unsigned>;
  DIBasicTypeAttrStorage(unsigned tag, StringAttr name, uint64_t sizeInBits, unsigned encoding) : tag(tag), name(name), sizeInBits(sizeInBits), encoding(encoding) {}

  KeyTy getAsKey() const {
    return KeyTy(tag, name, sizeInBits, encoding);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (tag == std::get<0>(tblgenKey)) && (name == std::get<1>(tblgenKey)) && (sizeInBits == std::get<2>(tblgenKey)) && (encoding == std::get<3>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey));
  }

  static DIBasicTypeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto tag = std::get<0>(tblgenKey);
    auto name = std::get<1>(tblgenKey);
    auto sizeInBits = std::get<2>(tblgenKey);
    auto encoding = std::get<3>(tblgenKey);
    return new (allocator.allocate<DIBasicTypeAttrStorage>()) DIBasicTypeAttrStorage(tag, name, sizeInBits, encoding);
  }

  unsigned tag;
  StringAttr name;
  uint64_t sizeInBits;
  unsigned encoding;
};
} // namespace detail
DIBasicTypeAttr DIBasicTypeAttr::get(::mlir::MLIRContext *context, unsigned tag, StringAttr name, uint64_t sizeInBits, unsigned encoding) {
  return Base::get(context, tag, name, sizeInBits, encoding);
}

DIBasicTypeAttr DIBasicTypeAttr::get(::mlir::MLIRContext *context, unsigned tag, const Twine &name, uint64_t sizeInBits, unsigned encoding) {
  return Base::get(context, tag, StringAttr::get(context, name), sizeInBits,
               encoding);
}

::mlir::Attribute DIBasicTypeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<unsigned> _result_tag;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<uint64_t> _result_sizeInBits;
  ::mlir::FailureOr<unsigned> _result_encoding;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_tag = false;
  bool _seen_name = false;
  bool _seen_sizeInBits = false;
  bool _seen_encoding = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_tag && _paramKey == "tag") {
        _seen_tag = true;

        // Parse variable 'tag'
        _result_tag =  [&]() -> FailureOr<unsigned> {
            SMLoc tagLoc = odsParser.getCurrentLocation();
            StringRef name;
            if (odsParser.parseKeyword(&name))
              return failure();

            if (unsigned tag = llvm::dwarf::getTag(name))
              return tag;
            return odsParser.emitError(tagLoc)
              << "invalid debug info debug info tag name: " << name;
          }() ;
        if (::mlir::failed(_result_tag)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIBasicTypeAttr parameter 'tag' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_name && _paramKey == "name") {
        _seen_name = true;

        // Parse variable 'name'
        _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_name)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIBasicTypeAttr parameter 'name' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_sizeInBits && _paramKey == "sizeInBits") {
        _seen_sizeInBits = true;

        // Parse variable 'sizeInBits'
        _result_sizeInBits = ::mlir::FieldParser<uint64_t>::parse(odsParser);
        if (::mlir::failed(_result_sizeInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIBasicTypeAttr parameter 'sizeInBits' which is to be a `uint64_t`");
          return {};
        }
      } else if (!_seen_encoding && _paramKey == "encoding") {
        _seen_encoding = true;

        // Parse variable 'encoding'
        _result_encoding =  [&]() -> FailureOr<unsigned> {
            SMLoc tagLoc = odsParser.getCurrentLocation();
            StringRef name;
            if (odsParser.parseKeyword(&name))
              return failure();

            if (unsigned tag = llvm::dwarf::getAttributeEncoding(name))
              return tag;
            return odsParser.emitError(tagLoc)
              << "invalid debug info debug info encoding name: " << name;
          }() ;
        if (::mlir::failed(_result_encoding)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIBasicTypeAttr parameter 'encoding' which is to be a `unsigned`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_tag) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "tag";
      return {};
    }
    if (!_seen_name) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "name";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_tag));
  assert(::mlir::succeeded(_result_name));
  return DIBasicTypeAttr::get(odsParser.getContext(),
      unsigned((*_result_tag)),
      StringAttr((*_result_name)),
      uint64_t((_result_sizeInBits.value_or(uint64_t()))),
      unsigned((_result_encoding.value_or(0))));
}

void DIBasicTypeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "tag = ";
    odsPrinter << llvm::dwarf::TagString(getTag());
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "name = ";
    odsPrinter.printStrippedAttrOrType(getName());
    if (!(getSizeInBits() == uint64_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "sizeInBits = ";
      if (!(getSizeInBits() == uint64_t())) {
        odsPrinter.printStrippedAttrOrType(getSizeInBits());
      }
    }
    if (!(getEncoding() == 0)) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "encoding = ";
      if (!(getEncoding() == 0)) {
        odsPrinter << llvm::dwarf::AttributeEncodingString(getEncoding());
      }
    }
  }
  odsPrinter << ">";
}

unsigned DIBasicTypeAttr::getTag() const {
  return getImpl()->tag;
}

StringAttr DIBasicTypeAttr::getName() const {
  return getImpl()->name;
}

uint64_t DIBasicTypeAttr::getSizeInBits() const {
  return getImpl()->sizeInBits;
}

unsigned DIBasicTypeAttr::getEncoding() const {
  return getImpl()->encoding;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DIBasicTypeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DICompileUnitAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<unsigned, DIFileAttr, StringAttr, bool, DIEmissionKind>;
  DICompileUnitAttrStorage(unsigned sourceLanguage, DIFileAttr file, StringAttr producer, bool isOptimized, DIEmissionKind emissionKind) : sourceLanguage(sourceLanguage), file(file), producer(producer), isOptimized(isOptimized), emissionKind(emissionKind) {}

  KeyTy getAsKey() const {
    return KeyTy(sourceLanguage, file, producer, isOptimized, emissionKind);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (sourceLanguage == std::get<0>(tblgenKey)) && (file == std::get<1>(tblgenKey)) && (producer == std::get<2>(tblgenKey)) && (isOptimized == std::get<3>(tblgenKey)) && (emissionKind == std::get<4>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey));
  }

  static DICompileUnitAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto sourceLanguage = std::get<0>(tblgenKey);
    auto file = std::get<1>(tblgenKey);
    auto producer = std::get<2>(tblgenKey);
    auto isOptimized = std::get<3>(tblgenKey);
    auto emissionKind = std::get<4>(tblgenKey);
    return new (allocator.allocate<DICompileUnitAttrStorage>()) DICompileUnitAttrStorage(sourceLanguage, file, producer, isOptimized, emissionKind);
  }

  unsigned sourceLanguage;
  DIFileAttr file;
  StringAttr producer;
  bool isOptimized;
  DIEmissionKind emissionKind;
};
} // namespace detail
DICompileUnitAttr DICompileUnitAttr::get(::mlir::MLIRContext *context, unsigned sourceLanguage, DIFileAttr file, StringAttr producer, bool isOptimized, DIEmissionKind emissionKind) {
  return Base::get(context, sourceLanguage, file, producer, isOptimized, emissionKind);
}

::mlir::Attribute DICompileUnitAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<unsigned> _result_sourceLanguage;
  ::mlir::FailureOr<DIFileAttr> _result_file;
  ::mlir::FailureOr<StringAttr> _result_producer;
  ::mlir::FailureOr<bool> _result_isOptimized;
  ::mlir::FailureOr<DIEmissionKind> _result_emissionKind;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_sourceLanguage = false;
  bool _seen_file = false;
  bool _seen_producer = false;
  bool _seen_isOptimized = false;
  bool _seen_emissionKind = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_sourceLanguage && _paramKey == "sourceLanguage") {
        _seen_sourceLanguage = true;

        // Parse variable 'sourceLanguage'
        _result_sourceLanguage =  [&]() -> FailureOr<unsigned> {
            SMLoc tagLoc = odsParser.getCurrentLocation();
            StringRef name;
            if (odsParser.parseKeyword(&name))
              return failure();

            if (unsigned tag = llvm::dwarf::getLanguage(name))
              return tag;
            return odsParser.emitError(tagLoc)
              << "invalid debug info debug info language name: " << name;
          }() ;
        if (::mlir::failed(_result_sourceLanguage)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompileUnitAttr parameter 'sourceLanguage' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_file && _paramKey == "file") {
        _seen_file = true;

        // Parse variable 'file'
        _result_file = ::mlir::FieldParser<DIFileAttr>::parse(odsParser);
        if (::mlir::failed(_result_file)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompileUnitAttr parameter 'file' which is to be a `DIFileAttr`");
          return {};
        }
      } else if (!_seen_producer && _paramKey == "producer") {
        _seen_producer = true;

        // Parse variable 'producer'
        _result_producer = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_producer)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompileUnitAttr parameter 'producer' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_isOptimized && _paramKey == "isOptimized") {
        _seen_isOptimized = true;

        // Parse variable 'isOptimized'
        _result_isOptimized = ::mlir::FieldParser<bool>::parse(odsParser);
        if (::mlir::failed(_result_isOptimized)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompileUnitAttr parameter 'isOptimized' which is to be a `bool`");
          return {};
        }
      } else if (!_seen_emissionKind && _paramKey == "emissionKind") {
        _seen_emissionKind = true;

        // Parse variable 'emissionKind'
        _result_emissionKind = ::mlir::FieldParser<DIEmissionKind>::parse(odsParser);
        if (::mlir::failed(_result_emissionKind)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompileUnitAttr parameter 'emissionKind' which is to be a `DIEmissionKind`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_sourceLanguage) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "sourceLanguage";
      return {};
    }
    if (!_seen_file) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "file";
      return {};
    }
    if (!_seen_isOptimized) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "isOptimized";
      return {};
    }
    if (!_seen_emissionKind) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "emissionKind";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_sourceLanguage));
  assert(::mlir::succeeded(_result_file));
  assert(::mlir::succeeded(_result_isOptimized));
  assert(::mlir::succeeded(_result_emissionKind));
  return DICompileUnitAttr::get(odsParser.getContext(),
      unsigned((*_result_sourceLanguage)),
      DIFileAttr((*_result_file)),
      StringAttr((_result_producer.value_or(StringAttr()))),
      bool((*_result_isOptimized)),
      DIEmissionKind((*_result_emissionKind)));
}

void DICompileUnitAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "sourceLanguage = ";
    odsPrinter << llvm::dwarf::LanguageString(getSourceLanguage());
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "file = ";
    odsPrinter.printStrippedAttrOrType(getFile());
    if (!(getProducer() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "producer = ";
      if (!(getProducer() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getProducer());
      }
    }
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "isOptimized = ";
    odsPrinter.printStrippedAttrOrType(getIsOptimized());
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "emissionKind = ";
    odsPrinter.printStrippedAttrOrType(getEmissionKind());
  }
  odsPrinter << ">";
}

unsigned DICompileUnitAttr::getSourceLanguage() const {
  return getImpl()->sourceLanguage;
}

DIFileAttr DICompileUnitAttr::getFile() const {
  return getImpl()->file;
}

StringAttr DICompileUnitAttr::getProducer() const {
  return getImpl()->producer;
}

bool DICompileUnitAttr::getIsOptimized() const {
  return getImpl()->isOptimized;
}

DIEmissionKind DICompileUnitAttr::getEmissionKind() const {
  return getImpl()->emissionKind;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DICompileUnitAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DICompositeTypeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<unsigned, StringAttr, DIFileAttr, uint32_t, DIScopeAttr, DITypeAttr, DIFlags, uint64_t, uint64_t, ::llvm::ArrayRef<DINodeAttr>>;
  DICompositeTypeAttrStorage(unsigned tag, StringAttr name, DIFileAttr file, uint32_t line, DIScopeAttr scope, DITypeAttr baseType, DIFlags flags, uint64_t sizeInBits, uint64_t alignInBits, ::llvm::ArrayRef<DINodeAttr> elements) : tag(tag), name(name), file(file), line(line), scope(scope), baseType(baseType), flags(flags), sizeInBits(sizeInBits), alignInBits(alignInBits), elements(elements) {}

  KeyTy getAsKey() const {
    return KeyTy(tag, name, file, line, scope, baseType, flags, sizeInBits, alignInBits, elements);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (tag == std::get<0>(tblgenKey)) && (name == std::get<1>(tblgenKey)) && (file == std::get<2>(tblgenKey)) && (line == std::get<3>(tblgenKey)) && (scope == std::get<4>(tblgenKey)) && (baseType == std::get<5>(tblgenKey)) && (flags == std::get<6>(tblgenKey)) && (sizeInBits == std::get<7>(tblgenKey)) && (alignInBits == std::get<8>(tblgenKey)) && (::llvm::ArrayRef<DINodeAttr>(elements) == ::llvm::ArrayRef<DINodeAttr>(std::get<9>(tblgenKey)));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey), std::get<7>(tblgenKey), std::get<8>(tblgenKey), std::get<9>(tblgenKey));
  }

  static DICompositeTypeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto tag = std::get<0>(tblgenKey);
    auto name = std::get<1>(tblgenKey);
    auto file = std::get<2>(tblgenKey);
    auto line = std::get<3>(tblgenKey);
    auto scope = std::get<4>(tblgenKey);
    auto baseType = std::get<5>(tblgenKey);
    auto flags = std::get<6>(tblgenKey);
    auto sizeInBits = std::get<7>(tblgenKey);
    auto alignInBits = std::get<8>(tblgenKey);
    auto elements = std::get<9>(tblgenKey);
    elements = allocator.copyInto(elements);
    return new (allocator.allocate<DICompositeTypeAttrStorage>()) DICompositeTypeAttrStorage(tag, name, file, line, scope, baseType, flags, sizeInBits, alignInBits, elements);
  }

  unsigned tag;
  StringAttr name;
  DIFileAttr file;
  uint32_t line;
  DIScopeAttr scope;
  DITypeAttr baseType;
  DIFlags flags;
  uint64_t sizeInBits;
  uint64_t alignInBits;
  ::llvm::ArrayRef<DINodeAttr> elements;
};
} // namespace detail
DICompositeTypeAttr DICompositeTypeAttr::get(::mlir::MLIRContext *context, unsigned tag, StringAttr name, DIFileAttr file, uint32_t line, DIScopeAttr scope, DITypeAttr baseType, DIFlags flags, uint64_t sizeInBits, uint64_t alignInBits, ::llvm::ArrayRef<DINodeAttr> elements) {
  return Base::get(context, tag, name, file, line, scope, baseType, flags, sizeInBits, alignInBits, elements);
}

::mlir::Attribute DICompositeTypeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<unsigned> _result_tag;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<DIFileAttr> _result_file;
  ::mlir::FailureOr<uint32_t> _result_line;
  ::mlir::FailureOr<DIScopeAttr> _result_scope;
  ::mlir::FailureOr<DITypeAttr> _result_baseType;
  ::mlir::FailureOr<DIFlags> _result_flags;
  ::mlir::FailureOr<uint64_t> _result_sizeInBits;
  ::mlir::FailureOr<uint64_t> _result_alignInBits;
  ::mlir::FailureOr<::llvm::SmallVector<DINodeAttr>> _result_elements;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_tag = false;
  bool _seen_name = false;
  bool _seen_file = false;
  bool _seen_line = false;
  bool _seen_scope = false;
  bool _seen_baseType = false;
  bool _seen_flags = false;
  bool _seen_sizeInBits = false;
  bool _seen_alignInBits = false;
  bool _seen_elements = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_tag && _paramKey == "tag") {
        _seen_tag = true;

        // Parse variable 'tag'
        _result_tag =  [&]() -> FailureOr<unsigned> {
            SMLoc tagLoc = odsParser.getCurrentLocation();
            StringRef name;
            if (odsParser.parseKeyword(&name))
              return failure();

            if (unsigned tag = llvm::dwarf::getTag(name))
              return tag;
            return odsParser.emitError(tagLoc)
              << "invalid debug info debug info tag name: " << name;
          }() ;
        if (::mlir::failed(_result_tag)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'tag' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_name && _paramKey == "name") {
        _seen_name = true;

        // Parse variable 'name'
        _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_name)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'name' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_file && _paramKey == "file") {
        _seen_file = true;

        // Parse variable 'file'
        _result_file = ::mlir::FieldParser<DIFileAttr>::parse(odsParser);
        if (::mlir::failed(_result_file)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'file' which is to be a `DIFileAttr`");
          return {};
        }
      } else if (!_seen_line && _paramKey == "line") {
        _seen_line = true;

        // Parse variable 'line'
        _result_line = ::mlir::FieldParser<uint32_t>::parse(odsParser);
        if (::mlir::failed(_result_line)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'line' which is to be a `uint32_t`");
          return {};
        }
      } else if (!_seen_scope && _paramKey == "scope") {
        _seen_scope = true;

        // Parse variable 'scope'
        _result_scope = ::mlir::FieldParser<DIScopeAttr>::parse(odsParser);
        if (::mlir::failed(_result_scope)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'scope' which is to be a `DIScopeAttr`");
          return {};
        }
      } else if (!_seen_baseType && _paramKey == "baseType") {
        _seen_baseType = true;

        // Parse variable 'baseType'
        _result_baseType = ::mlir::FieldParser<DITypeAttr>::parse(odsParser);
        if (::mlir::failed(_result_baseType)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'baseType' which is to be a `DITypeAttr`");
          return {};
        }
      } else if (!_seen_flags && _paramKey == "flags") {
        _seen_flags = true;

        // Parse variable 'flags'
        _result_flags = ::mlir::FieldParser<DIFlags>::parse(odsParser);
        if (::mlir::failed(_result_flags)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'flags' which is to be a `DIFlags`");
          return {};
        }
      } else if (!_seen_sizeInBits && _paramKey == "sizeInBits") {
        _seen_sizeInBits = true;

        // Parse variable 'sizeInBits'
        _result_sizeInBits = ::mlir::FieldParser<uint64_t>::parse(odsParser);
        if (::mlir::failed(_result_sizeInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'sizeInBits' which is to be a `uint64_t`");
          return {};
        }
      } else if (!_seen_alignInBits && _paramKey == "alignInBits") {
        _seen_alignInBits = true;

        // Parse variable 'alignInBits'
        _result_alignInBits = ::mlir::FieldParser<uint64_t>::parse(odsParser);
        if (::mlir::failed(_result_alignInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'alignInBits' which is to be a `uint64_t`");
          return {};
        }
      } else if (!_seen_elements && _paramKey == "elements") {
        _seen_elements = true;

        // Parse variable 'elements'
        _result_elements = ::mlir::FieldParser<::llvm::SmallVector<DINodeAttr>>::parse(odsParser);
        if (::mlir::failed(_result_elements)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DICompositeTypeAttr parameter 'elements' which is to be a `::llvm::ArrayRef<DINodeAttr>`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_tag) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "tag";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_tag));
  return DICompositeTypeAttr::get(odsParser.getContext(),
      unsigned((*_result_tag)),
      StringAttr((_result_name.value_or(StringAttr()))),
      DIFileAttr((_result_file.value_or(DIFileAttr()))),
      uint32_t((_result_line.value_or(uint32_t()))),
      DIScopeAttr((_result_scope.value_or(DIScopeAttr()))),
      DITypeAttr((_result_baseType.value_or(DITypeAttr()))),
      DIFlags((_result_flags.value_or(DIFlags()))),
      uint64_t((_result_sizeInBits.value_or(uint64_t()))),
      uint64_t((_result_alignInBits.value_or(uint64_t()))),
      ::llvm::ArrayRef<DINodeAttr>((_result_elements.value_or(::llvm::SmallVector<DINodeAttr>()))));
}

void DICompositeTypeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "tag = ";
    odsPrinter << llvm::dwarf::TagString(getTag());
    if (!(getName() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "name = ";
      if (!(getName() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getName());
      }
    }
    if (!(getFile() == DIFileAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "file = ";
      if (!(getFile() == DIFileAttr())) {
        odsPrinter.printStrippedAttrOrType(getFile());
      }
    }
    if (!(getLine() == uint32_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "line = ";
      if (!(getLine() == uint32_t())) {
        odsPrinter.printStrippedAttrOrType(getLine());
      }
    }
    if (!(getScope() == DIScopeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "scope = ";
      if (!(getScope() == DIScopeAttr())) {
        odsPrinter.printStrippedAttrOrType(getScope());
      }
    }
    if (!(getBaseType() == DITypeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "baseType = ";
      if (!(getBaseType() == DITypeAttr())) {
        odsPrinter.printStrippedAttrOrType(getBaseType());
      }
    }
    if (!(getFlags() == DIFlags())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "flags = ";
      if (!(getFlags() == DIFlags())) {
        odsPrinter.printStrippedAttrOrType(getFlags());
      }
    }
    if (!(getSizeInBits() == uint64_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "sizeInBits = ";
      if (!(getSizeInBits() == uint64_t())) {
        odsPrinter.printStrippedAttrOrType(getSizeInBits());
      }
    }
    if (!(getAlignInBits() == uint64_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "alignInBits = ";
      if (!(getAlignInBits() == uint64_t())) {
        odsPrinter.printStrippedAttrOrType(getAlignInBits());
      }
    }
    if (!(::llvm::ArrayRef<DINodeAttr>(getElements()) == ::llvm::ArrayRef<DINodeAttr>(::llvm::SmallVector<DINodeAttr>()))) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "elements = ";
      if (!(::llvm::ArrayRef<DINodeAttr>(getElements()) == ::llvm::ArrayRef<DINodeAttr>(::llvm::SmallVector<DINodeAttr>()))) {
        odsPrinter.printStrippedAttrOrType(getElements());
      }
    }
  }
  odsPrinter << ">";
}

unsigned DICompositeTypeAttr::getTag() const {
  return getImpl()->tag;
}

StringAttr DICompositeTypeAttr::getName() const {
  return getImpl()->name;
}

DIFileAttr DICompositeTypeAttr::getFile() const {
  return getImpl()->file;
}

uint32_t DICompositeTypeAttr::getLine() const {
  return getImpl()->line;
}

DIScopeAttr DICompositeTypeAttr::getScope() const {
  return getImpl()->scope;
}

DITypeAttr DICompositeTypeAttr::getBaseType() const {
  return getImpl()->baseType;
}

DIFlags DICompositeTypeAttr::getFlags() const {
  return getImpl()->flags;
}

uint64_t DICompositeTypeAttr::getSizeInBits() const {
  return getImpl()->sizeInBits;
}

uint64_t DICompositeTypeAttr::getAlignInBits() const {
  return getImpl()->alignInBits;
}

::llvm::ArrayRef<DINodeAttr> DICompositeTypeAttr::getElements() const {
  return getImpl()->elements;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DICompositeTypeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DIDerivedTypeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<unsigned, StringAttr, DITypeAttr, uint64_t, uint32_t, uint64_t>;
  DIDerivedTypeAttrStorage(unsigned tag, StringAttr name, DITypeAttr baseType, uint64_t sizeInBits, uint32_t alignInBits, uint64_t offsetInBits) : tag(tag), name(name), baseType(baseType), sizeInBits(sizeInBits), alignInBits(alignInBits), offsetInBits(offsetInBits) {}

  KeyTy getAsKey() const {
    return KeyTy(tag, name, baseType, sizeInBits, alignInBits, offsetInBits);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (tag == std::get<0>(tblgenKey)) && (name == std::get<1>(tblgenKey)) && (baseType == std::get<2>(tblgenKey)) && (sizeInBits == std::get<3>(tblgenKey)) && (alignInBits == std::get<4>(tblgenKey)) && (offsetInBits == std::get<5>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey));
  }

  static DIDerivedTypeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto tag = std::get<0>(tblgenKey);
    auto name = std::get<1>(tblgenKey);
    auto baseType = std::get<2>(tblgenKey);
    auto sizeInBits = std::get<3>(tblgenKey);
    auto alignInBits = std::get<4>(tblgenKey);
    auto offsetInBits = std::get<5>(tblgenKey);
    return new (allocator.allocate<DIDerivedTypeAttrStorage>()) DIDerivedTypeAttrStorage(tag, name, baseType, sizeInBits, alignInBits, offsetInBits);
  }

  unsigned tag;
  StringAttr name;
  DITypeAttr baseType;
  uint64_t sizeInBits;
  uint32_t alignInBits;
  uint64_t offsetInBits;
};
} // namespace detail
DIDerivedTypeAttr DIDerivedTypeAttr::get(::mlir::MLIRContext *context, unsigned tag, StringAttr name, DITypeAttr baseType, uint64_t sizeInBits, uint32_t alignInBits, uint64_t offsetInBits) {
  return Base::get(context, tag, name, baseType, sizeInBits, alignInBits, offsetInBits);
}

::mlir::Attribute DIDerivedTypeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<unsigned> _result_tag;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<DITypeAttr> _result_baseType;
  ::mlir::FailureOr<uint64_t> _result_sizeInBits;
  ::mlir::FailureOr<uint32_t> _result_alignInBits;
  ::mlir::FailureOr<uint64_t> _result_offsetInBits;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_tag = false;
  bool _seen_name = false;
  bool _seen_baseType = false;
  bool _seen_sizeInBits = false;
  bool _seen_alignInBits = false;
  bool _seen_offsetInBits = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_tag && _paramKey == "tag") {
        _seen_tag = true;

        // Parse variable 'tag'
        _result_tag =  [&]() -> FailureOr<unsigned> {
            SMLoc tagLoc = odsParser.getCurrentLocation();
            StringRef name;
            if (odsParser.parseKeyword(&name))
              return failure();

            if (unsigned tag = llvm::dwarf::getTag(name))
              return tag;
            return odsParser.emitError(tagLoc)
              << "invalid debug info debug info tag name: " << name;
          }() ;
        if (::mlir::failed(_result_tag)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIDerivedTypeAttr parameter 'tag' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_name && _paramKey == "name") {
        _seen_name = true;

        // Parse variable 'name'
        _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_name)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIDerivedTypeAttr parameter 'name' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_baseType && _paramKey == "baseType") {
        _seen_baseType = true;

        // Parse variable 'baseType'
        _result_baseType = ::mlir::FieldParser<DITypeAttr>::parse(odsParser);
        if (::mlir::failed(_result_baseType)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIDerivedTypeAttr parameter 'baseType' which is to be a `DITypeAttr`");
          return {};
        }
      } else if (!_seen_sizeInBits && _paramKey == "sizeInBits") {
        _seen_sizeInBits = true;

        // Parse variable 'sizeInBits'
        _result_sizeInBits = ::mlir::FieldParser<uint64_t>::parse(odsParser);
        if (::mlir::failed(_result_sizeInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIDerivedTypeAttr parameter 'sizeInBits' which is to be a `uint64_t`");
          return {};
        }
      } else if (!_seen_alignInBits && _paramKey == "alignInBits") {
        _seen_alignInBits = true;

        // Parse variable 'alignInBits'
        _result_alignInBits = ::mlir::FieldParser<uint32_t>::parse(odsParser);
        if (::mlir::failed(_result_alignInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIDerivedTypeAttr parameter 'alignInBits' which is to be a `uint32_t`");
          return {};
        }
      } else if (!_seen_offsetInBits && _paramKey == "offsetInBits") {
        _seen_offsetInBits = true;

        // Parse variable 'offsetInBits'
        _result_offsetInBits = ::mlir::FieldParser<uint64_t>::parse(odsParser);
        if (::mlir::failed(_result_offsetInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIDerivedTypeAttr parameter 'offsetInBits' which is to be a `uint64_t`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_tag) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "tag";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_tag));
  return DIDerivedTypeAttr::get(odsParser.getContext(),
      unsigned((*_result_tag)),
      StringAttr((_result_name.value_or(StringAttr()))),
      DITypeAttr((_result_baseType.value_or(DITypeAttr()))),
      uint64_t((_result_sizeInBits.value_or(uint64_t()))),
      uint32_t((_result_alignInBits.value_or(uint32_t()))),
      uint64_t((_result_offsetInBits.value_or(uint64_t()))));
}

void DIDerivedTypeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "tag = ";
    odsPrinter << llvm::dwarf::TagString(getTag());
    if (!(getName() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "name = ";
      if (!(getName() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getName());
      }
    }
    if (!(getBaseType() == DITypeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "baseType = ";
      if (!(getBaseType() == DITypeAttr())) {
        odsPrinter.printStrippedAttrOrType(getBaseType());
      }
    }
    if (!(getSizeInBits() == uint64_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "sizeInBits = ";
      if (!(getSizeInBits() == uint64_t())) {
        odsPrinter.printStrippedAttrOrType(getSizeInBits());
      }
    }
    if (!(getAlignInBits() == uint32_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "alignInBits = ";
      if (!(getAlignInBits() == uint32_t())) {
        odsPrinter.printStrippedAttrOrType(getAlignInBits());
      }
    }
    if (!(getOffsetInBits() == uint64_t())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "offsetInBits = ";
      if (!(getOffsetInBits() == uint64_t())) {
        odsPrinter.printStrippedAttrOrType(getOffsetInBits());
      }
    }
  }
  odsPrinter << ">";
}

unsigned DIDerivedTypeAttr::getTag() const {
  return getImpl()->tag;
}

StringAttr DIDerivedTypeAttr::getName() const {
  return getImpl()->name;
}

DITypeAttr DIDerivedTypeAttr::getBaseType() const {
  return getImpl()->baseType;
}

uint64_t DIDerivedTypeAttr::getSizeInBits() const {
  return getImpl()->sizeInBits;
}

uint32_t DIDerivedTypeAttr::getAlignInBits() const {
  return getImpl()->alignInBits;
}

uint64_t DIDerivedTypeAttr::getOffsetInBits() const {
  return getImpl()->offsetInBits;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DIDerivedTypeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DIFileAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<StringAttr, StringAttr>;
  DIFileAttrStorage(StringAttr name, StringAttr directory) : name(name), directory(directory) {}

  KeyTy getAsKey() const {
    return KeyTy(name, directory);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (name == std::get<0>(tblgenKey)) && (directory == std::get<1>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey));
  }

  static DIFileAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto name = std::get<0>(tblgenKey);
    auto directory = std::get<1>(tblgenKey);
    return new (allocator.allocate<DIFileAttrStorage>()) DIFileAttrStorage(name, directory);
  }

  StringAttr name;
  StringAttr directory;
};
} // namespace detail
DIFileAttr DIFileAttr::get(::mlir::MLIRContext *context, StringAttr name, StringAttr directory) {
  return Base::get(context, name, directory);
}

DIFileAttr DIFileAttr::get(::mlir::MLIRContext *context, StringRef name, StringRef directory) {
  return Base::get(context, StringAttr::get(context, name),
               StringAttr::get(context, directory));
}

::mlir::Attribute DIFileAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<StringAttr> _result_directory;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};

  // Parse variable 'name'
  _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
  if (::mlir::failed(_result_name)) {
    odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIFileAttr parameter 'name' which is to be a `StringAttr`");
    return {};
  }
  // Parse literal 'in'
  if (odsParser.parseKeyword("in")) return {};

  // Parse variable 'directory'
  _result_directory = ::mlir::FieldParser<StringAttr>::parse(odsParser);
  if (::mlir::failed(_result_directory)) {
    odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DIFileAttr parameter 'directory' which is to be a `StringAttr`");
    return {};
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_name));
  assert(::mlir::succeeded(_result_directory));
  return DIFileAttr::get(odsParser.getContext(),
      StringAttr((*_result_name)),
      StringAttr((*_result_directory)));
}

void DIFileAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  odsPrinter.printStrippedAttrOrType(getName());
  odsPrinter << ' ' << "in";
  odsPrinter << ' ';
  odsPrinter.printStrippedAttrOrType(getDirectory());
  odsPrinter << ">";
}

StringAttr DIFileAttr::getName() const {
  return getImpl()->name;
}

StringAttr DIFileAttr::getDirectory() const {
  return getImpl()->directory;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DIFileAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DILexicalBlockAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<DIScopeAttr, DIFileAttr, unsigned, unsigned>;
  DILexicalBlockAttrStorage(DIScopeAttr scope, DIFileAttr file, unsigned line, unsigned column) : scope(scope), file(file), line(line), column(column) {}

  KeyTy getAsKey() const {
    return KeyTy(scope, file, line, column);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (scope == std::get<0>(tblgenKey)) && (file == std::get<1>(tblgenKey)) && (line == std::get<2>(tblgenKey)) && (column == std::get<3>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey));
  }

  static DILexicalBlockAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto scope = std::get<0>(tblgenKey);
    auto file = std::get<1>(tblgenKey);
    auto line = std::get<2>(tblgenKey);
    auto column = std::get<3>(tblgenKey);
    return new (allocator.allocate<DILexicalBlockAttrStorage>()) DILexicalBlockAttrStorage(scope, file, line, column);
  }

  DIScopeAttr scope;
  DIFileAttr file;
  unsigned line;
  unsigned column;
};
} // namespace detail
DILexicalBlockAttr DILexicalBlockAttr::get(::mlir::MLIRContext *context, DIScopeAttr scope, DIFileAttr file, unsigned line, unsigned column) {
  return Base::get(context, scope, file, line, column);
}

DILexicalBlockAttr DILexicalBlockAttr::get(DIScopeAttr scope, DIFileAttr file, unsigned line, unsigned column) {
  return Base::get(scope.getContext(), scope, file, line, column);
}

::mlir::Attribute DILexicalBlockAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<DIScopeAttr> _result_scope;
  ::mlir::FailureOr<DIFileAttr> _result_file;
  ::mlir::FailureOr<unsigned> _result_line;
  ::mlir::FailureOr<unsigned> _result_column;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_scope = false;
  bool _seen_file = false;
  bool _seen_line = false;
  bool _seen_column = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_scope && _paramKey == "scope") {
        _seen_scope = true;

        // Parse variable 'scope'
        _result_scope = ::mlir::FieldParser<DIScopeAttr>::parse(odsParser);
        if (::mlir::failed(_result_scope)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockAttr parameter 'scope' which is to be a `DIScopeAttr`");
          return {};
        }
      } else if (!_seen_file && _paramKey == "file") {
        _seen_file = true;

        // Parse variable 'file'
        _result_file = ::mlir::FieldParser<DIFileAttr>::parse(odsParser);
        if (::mlir::failed(_result_file)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockAttr parameter 'file' which is to be a `DIFileAttr`");
          return {};
        }
      } else if (!_seen_line && _paramKey == "line") {
        _seen_line = true;

        // Parse variable 'line'
        _result_line = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_line)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockAttr parameter 'line' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_column && _paramKey == "column") {
        _seen_column = true;

        // Parse variable 'column'
        _result_column = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_column)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockAttr parameter 'column' which is to be a `unsigned`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_scope) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "scope";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_scope));
  return DILexicalBlockAttr::get(odsParser.getContext(),
      DIScopeAttr((*_result_scope)),
      DIFileAttr((_result_file.value_or(DIFileAttr()))),
      unsigned((_result_line.value_or(unsigned()))),
      unsigned((_result_column.value_or(unsigned()))));
}

void DILexicalBlockAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "scope = ";
    odsPrinter.printStrippedAttrOrType(getScope());
    if (!(getFile() == DIFileAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "file = ";
      if (!(getFile() == DIFileAttr())) {
        odsPrinter.printStrippedAttrOrType(getFile());
      }
    }
    if (!(getLine() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "line = ";
      if (!(getLine() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getLine());
      }
    }
    if (!(getColumn() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "column = ";
      if (!(getColumn() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getColumn());
      }
    }
  }
  odsPrinter << ">";
}

DIScopeAttr DILexicalBlockAttr::getScope() const {
  return getImpl()->scope;
}

DIFileAttr DILexicalBlockAttr::getFile() const {
  return getImpl()->file;
}

unsigned DILexicalBlockAttr::getLine() const {
  return getImpl()->line;
}

unsigned DILexicalBlockAttr::getColumn() const {
  return getImpl()->column;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DILexicalBlockAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DILexicalBlockFileAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<DIScopeAttr, DIFileAttr, unsigned>;
  DILexicalBlockFileAttrStorage(DIScopeAttr scope, DIFileAttr file, unsigned discriminator) : scope(scope), file(file), discriminator(discriminator) {}

  KeyTy getAsKey() const {
    return KeyTy(scope, file, discriminator);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (scope == std::get<0>(tblgenKey)) && (file == std::get<1>(tblgenKey)) && (discriminator == std::get<2>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey));
  }

  static DILexicalBlockFileAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto scope = std::get<0>(tblgenKey);
    auto file = std::get<1>(tblgenKey);
    auto discriminator = std::get<2>(tblgenKey);
    return new (allocator.allocate<DILexicalBlockFileAttrStorage>()) DILexicalBlockFileAttrStorage(scope, file, discriminator);
  }

  DIScopeAttr scope;
  DIFileAttr file;
  unsigned discriminator;
};
} // namespace detail
DILexicalBlockFileAttr DILexicalBlockFileAttr::get(::mlir::MLIRContext *context, DIScopeAttr scope, DIFileAttr file, unsigned discriminator) {
  return Base::get(context, scope, file, discriminator);
}

DILexicalBlockFileAttr DILexicalBlockFileAttr::get(DIScopeAttr scope, DIFileAttr file, unsigned discriminator) {
  return Base::get(scope.getContext(), scope, file, discriminator);
}

::mlir::Attribute DILexicalBlockFileAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<DIScopeAttr> _result_scope;
  ::mlir::FailureOr<DIFileAttr> _result_file;
  ::mlir::FailureOr<unsigned> _result_discriminator;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_scope = false;
  bool _seen_file = false;
  bool _seen_discriminator = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_scope && _paramKey == "scope") {
        _seen_scope = true;

        // Parse variable 'scope'
        _result_scope = ::mlir::FieldParser<DIScopeAttr>::parse(odsParser);
        if (::mlir::failed(_result_scope)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockFile parameter 'scope' which is to be a `DIScopeAttr`");
          return {};
        }
      } else if (!_seen_file && _paramKey == "file") {
        _seen_file = true;

        // Parse variable 'file'
        _result_file = ::mlir::FieldParser<DIFileAttr>::parse(odsParser);
        if (::mlir::failed(_result_file)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockFile parameter 'file' which is to be a `DIFileAttr`");
          return {};
        }
      } else if (!_seen_discriminator && _paramKey == "discriminator") {
        _seen_discriminator = true;

        // Parse variable 'discriminator'
        _result_discriminator = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_discriminator)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILexicalBlockFile parameter 'discriminator' which is to be a `unsigned`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_scope) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "scope";
      return {};
    }
    if (!_seen_discriminator) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "discriminator";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_scope));
  assert(::mlir::succeeded(_result_discriminator));
  return DILexicalBlockFileAttr::get(odsParser.getContext(),
      DIScopeAttr((*_result_scope)),
      DIFileAttr((_result_file.value_or(DIFileAttr()))),
      unsigned((*_result_discriminator)));
}

void DILexicalBlockFileAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "scope = ";
    odsPrinter.printStrippedAttrOrType(getScope());
    if (!(getFile() == DIFileAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "file = ";
      if (!(getFile() == DIFileAttr())) {
        odsPrinter.printStrippedAttrOrType(getFile());
      }
    }
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "discriminator = ";
    odsPrinter.printStrippedAttrOrType(getDiscriminator());
  }
  odsPrinter << ">";
}

DIScopeAttr DILexicalBlockFileAttr::getScope() const {
  return getImpl()->scope;
}

DIFileAttr DILexicalBlockFileAttr::getFile() const {
  return getImpl()->file;
}

unsigned DILexicalBlockFileAttr::getDiscriminator() const {
  return getImpl()->discriminator;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DILexicalBlockFileAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DILocalVariableAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<DIScopeAttr, StringAttr, DIFileAttr, unsigned, unsigned, unsigned, DITypeAttr>;
  DILocalVariableAttrStorage(DIScopeAttr scope, StringAttr name, DIFileAttr file, unsigned line, unsigned arg, unsigned alignInBits, DITypeAttr type) : scope(scope), name(name), file(file), line(line), arg(arg), alignInBits(alignInBits), type(type) {}

  KeyTy getAsKey() const {
    return KeyTy(scope, name, file, line, arg, alignInBits, type);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (scope == std::get<0>(tblgenKey)) && (name == std::get<1>(tblgenKey)) && (file == std::get<2>(tblgenKey)) && (line == std::get<3>(tblgenKey)) && (arg == std::get<4>(tblgenKey)) && (alignInBits == std::get<5>(tblgenKey)) && (type == std::get<6>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey));
  }

  static DILocalVariableAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto scope = std::get<0>(tblgenKey);
    auto name = std::get<1>(tblgenKey);
    auto file = std::get<2>(tblgenKey);
    auto line = std::get<3>(tblgenKey);
    auto arg = std::get<4>(tblgenKey);
    auto alignInBits = std::get<5>(tblgenKey);
    auto type = std::get<6>(tblgenKey);
    return new (allocator.allocate<DILocalVariableAttrStorage>()) DILocalVariableAttrStorage(scope, name, file, line, arg, alignInBits, type);
  }

  DIScopeAttr scope;
  StringAttr name;
  DIFileAttr file;
  unsigned line;
  unsigned arg;
  unsigned alignInBits;
  DITypeAttr type;
};
} // namespace detail
DILocalVariableAttr DILocalVariableAttr::get(::mlir::MLIRContext *context, DIScopeAttr scope, StringAttr name, DIFileAttr file, unsigned line, unsigned arg, unsigned alignInBits, DITypeAttr type) {
  return Base::get(context, scope, name, file, line, arg, alignInBits, type);
}

DILocalVariableAttr DILocalVariableAttr::get(DIScopeAttr scope, StringRef name, DIFileAttr file, unsigned line, unsigned arg, unsigned alignInBits, DITypeAttr type) {
  MLIRContext *ctx = scope.getContext();
  return Base::get(ctx, scope, StringAttr::get(ctx, name), file, line,
               arg, alignInBits, type);
}

::mlir::Attribute DILocalVariableAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<DIScopeAttr> _result_scope;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<DIFileAttr> _result_file;
  ::mlir::FailureOr<unsigned> _result_line;
  ::mlir::FailureOr<unsigned> _result_arg;
  ::mlir::FailureOr<unsigned> _result_alignInBits;
  ::mlir::FailureOr<DITypeAttr> _result_type;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_scope = false;
  bool _seen_name = false;
  bool _seen_file = false;
  bool _seen_line = false;
  bool _seen_arg = false;
  bool _seen_alignInBits = false;
  bool _seen_type = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_scope && _paramKey == "scope") {
        _seen_scope = true;

        // Parse variable 'scope'
        _result_scope = ::mlir::FieldParser<DIScopeAttr>::parse(odsParser);
        if (::mlir::failed(_result_scope)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'scope' which is to be a `DIScopeAttr`");
          return {};
        }
      } else if (!_seen_name && _paramKey == "name") {
        _seen_name = true;

        // Parse variable 'name'
        _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_name)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'name' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_file && _paramKey == "file") {
        _seen_file = true;

        // Parse variable 'file'
        _result_file = ::mlir::FieldParser<DIFileAttr>::parse(odsParser);
        if (::mlir::failed(_result_file)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'file' which is to be a `DIFileAttr`");
          return {};
        }
      } else if (!_seen_line && _paramKey == "line") {
        _seen_line = true;

        // Parse variable 'line'
        _result_line = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_line)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'line' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_arg && _paramKey == "arg") {
        _seen_arg = true;

        // Parse variable 'arg'
        _result_arg = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_arg)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'arg' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_alignInBits && _paramKey == "alignInBits") {
        _seen_alignInBits = true;

        // Parse variable 'alignInBits'
        _result_alignInBits = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_alignInBits)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'alignInBits' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_type && _paramKey == "type") {
        _seen_type = true;

        // Parse variable 'type'
        _result_type = ::mlir::FieldParser<DITypeAttr>::parse(odsParser);
        if (::mlir::failed(_result_type)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DILocalVariableAttr parameter 'type' which is to be a `DITypeAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_scope) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "scope";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_scope));
  return DILocalVariableAttr::get(odsParser.getContext(),
      DIScopeAttr((*_result_scope)),
      StringAttr((_result_name.value_or(StringAttr()))),
      DIFileAttr((_result_file.value_or(DIFileAttr()))),
      unsigned((_result_line.value_or(unsigned()))),
      unsigned((_result_arg.value_or(unsigned()))),
      unsigned((_result_alignInBits.value_or(unsigned()))),
      DITypeAttr((_result_type.value_or(DITypeAttr()))));
}

void DILocalVariableAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "scope = ";
    odsPrinter.printStrippedAttrOrType(getScope());
    if (!(getName() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "name = ";
      if (!(getName() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getName());
      }
    }
    if (!(getFile() == DIFileAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "file = ";
      if (!(getFile() == DIFileAttr())) {
        odsPrinter.printStrippedAttrOrType(getFile());
      }
    }
    if (!(getLine() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "line = ";
      if (!(getLine() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getLine());
      }
    }
    if (!(getArg() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "arg = ";
      if (!(getArg() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getArg());
      }
    }
    if (!(getAlignInBits() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "alignInBits = ";
      if (!(getAlignInBits() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getAlignInBits());
      }
    }
    if (!(getType() == DITypeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "type = ";
      if (!(getType() == DITypeAttr())) {
        odsPrinter.printStrippedAttrOrType(getType());
      }
    }
  }
  odsPrinter << ">";
}

DIScopeAttr DILocalVariableAttr::getScope() const {
  return getImpl()->scope;
}

StringAttr DILocalVariableAttr::getName() const {
  return getImpl()->name;
}

DIFileAttr DILocalVariableAttr::getFile() const {
  return getImpl()->file;
}

unsigned DILocalVariableAttr::getLine() const {
  return getImpl()->line;
}

unsigned DILocalVariableAttr::getArg() const {
  return getImpl()->arg;
}

unsigned DILocalVariableAttr::getAlignInBits() const {
  return getImpl()->alignInBits;
}

DITypeAttr DILocalVariableAttr::getType() const {
  return getImpl()->type;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DILocalVariableAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DINamespaceAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<StringAttr, DIScopeAttr, bool>;
  DINamespaceAttrStorage(StringAttr name, DIScopeAttr scope, bool exportSymbols) : name(name), scope(scope), exportSymbols(exportSymbols) {}

  KeyTy getAsKey() const {
    return KeyTy(name, scope, exportSymbols);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (name == std::get<0>(tblgenKey)) && (scope == std::get<1>(tblgenKey)) && (exportSymbols == std::get<2>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey));
  }

  static DINamespaceAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto name = std::get<0>(tblgenKey);
    auto scope = std::get<1>(tblgenKey);
    auto exportSymbols = std::get<2>(tblgenKey);
    return new (allocator.allocate<DINamespaceAttrStorage>()) DINamespaceAttrStorage(name, scope, exportSymbols);
  }

  StringAttr name;
  DIScopeAttr scope;
  bool exportSymbols;
};
} // namespace detail
DINamespaceAttr DINamespaceAttr::get(::mlir::MLIRContext *context, StringAttr name, DIScopeAttr scope, bool exportSymbols) {
  return Base::get(context, name, scope, exportSymbols);
}

::mlir::Attribute DINamespaceAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<DIScopeAttr> _result_scope;
  ::mlir::FailureOr<bool> _result_exportSymbols;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_name = false;
  bool _seen_scope = false;
  bool _seen_exportSymbols = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_name && _paramKey == "name") {
        _seen_name = true;

        // Parse variable 'name'
        _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_name)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DINamespaceAttr parameter 'name' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_scope && _paramKey == "scope") {
        _seen_scope = true;

        // Parse variable 'scope'
        _result_scope = ::mlir::FieldParser<DIScopeAttr>::parse(odsParser);
        if (::mlir::failed(_result_scope)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DINamespaceAttr parameter 'scope' which is to be a `DIScopeAttr`");
          return {};
        }
      } else if (!_seen_exportSymbols && _paramKey == "exportSymbols") {
        _seen_exportSymbols = true;

        // Parse variable 'exportSymbols'
        _result_exportSymbols = ::mlir::FieldParser<bool>::parse(odsParser);
        if (::mlir::failed(_result_exportSymbols)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DINamespaceAttr parameter 'exportSymbols' which is to be a `bool`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_exportSymbols) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "exportSymbols";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_exportSymbols));
  return DINamespaceAttr::get(odsParser.getContext(),
      StringAttr((_result_name.value_or(StringAttr()))),
      DIScopeAttr((_result_scope.value_or(DIScopeAttr()))),
      bool((*_result_exportSymbols)));
}

void DINamespaceAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getName() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "name = ";
      if (!(getName() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getName());
      }
    }
    if (!(getScope() == DIScopeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "scope = ";
      if (!(getScope() == DIScopeAttr())) {
        odsPrinter.printStrippedAttrOrType(getScope());
      }
    }
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "exportSymbols = ";
    odsPrinter.printStrippedAttrOrType(getExportSymbols());
  }
  odsPrinter << ">";
}

StringAttr DINamespaceAttr::getName() const {
  return getImpl()->name;
}

DIScopeAttr DINamespaceAttr::getScope() const {
  return getImpl()->scope;
}

bool DINamespaceAttr::getExportSymbols() const {
  return getImpl()->exportSymbols;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DINamespaceAttr)
namespace mlir {
namespace LLVM {
} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DINullTypeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DISubprogramAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<DICompileUnitAttr, DIScopeAttr, StringAttr, StringAttr, DIFileAttr, unsigned, unsigned, DISubprogramFlags, DISubroutineTypeAttr>;
  DISubprogramAttrStorage(DICompileUnitAttr compileUnit, DIScopeAttr scope, StringAttr name, StringAttr linkageName, DIFileAttr file, unsigned line, unsigned scopeLine, DISubprogramFlags subprogramFlags, DISubroutineTypeAttr type) : compileUnit(compileUnit), scope(scope), name(name), linkageName(linkageName), file(file), line(line), scopeLine(scopeLine), subprogramFlags(subprogramFlags), type(type) {}

  KeyTy getAsKey() const {
    return KeyTy(compileUnit, scope, name, linkageName, file, line, scopeLine, subprogramFlags, type);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (compileUnit == std::get<0>(tblgenKey)) && (scope == std::get<1>(tblgenKey)) && (name == std::get<2>(tblgenKey)) && (linkageName == std::get<3>(tblgenKey)) && (file == std::get<4>(tblgenKey)) && (line == std::get<5>(tblgenKey)) && (scopeLine == std::get<6>(tblgenKey)) && (subprogramFlags == std::get<7>(tblgenKey)) && (type == std::get<8>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey), std::get<7>(tblgenKey), std::get<8>(tblgenKey));
  }

  static DISubprogramAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto compileUnit = std::get<0>(tblgenKey);
    auto scope = std::get<1>(tblgenKey);
    auto name = std::get<2>(tblgenKey);
    auto linkageName = std::get<3>(tblgenKey);
    auto file = std::get<4>(tblgenKey);
    auto line = std::get<5>(tblgenKey);
    auto scopeLine = std::get<6>(tblgenKey);
    auto subprogramFlags = std::get<7>(tblgenKey);
    auto type = std::get<8>(tblgenKey);
    return new (allocator.allocate<DISubprogramAttrStorage>()) DISubprogramAttrStorage(compileUnit, scope, name, linkageName, file, line, scopeLine, subprogramFlags, type);
  }

  DICompileUnitAttr compileUnit;
  DIScopeAttr scope;
  StringAttr name;
  StringAttr linkageName;
  DIFileAttr file;
  unsigned line;
  unsigned scopeLine;
  DISubprogramFlags subprogramFlags;
  DISubroutineTypeAttr type;
};
} // namespace detail
DISubprogramAttr DISubprogramAttr::get(::mlir::MLIRContext *context, DICompileUnitAttr compileUnit, DIScopeAttr scope, StringAttr name, StringAttr linkageName, DIFileAttr file, unsigned line, unsigned scopeLine, DISubprogramFlags subprogramFlags, DISubroutineTypeAttr type) {
  return Base::get(context, compileUnit, scope, name, linkageName, file, line, scopeLine, subprogramFlags, type);
}

DISubprogramAttr DISubprogramAttr::get(DICompileUnitAttr compileUnit, DIScopeAttr scope, StringRef name, StringRef linkageName, DIFileAttr file, unsigned line, unsigned scopeLine, DISubprogramFlags subprogramFlags, DISubroutineTypeAttr type) {
  MLIRContext *ctx = file.getContext();
  return Base::get(ctx, compileUnit, scope, StringAttr::get(ctx, name),
               StringAttr::get(ctx, linkageName), file, line,
               scopeLine, subprogramFlags, type);
}

::mlir::Attribute DISubprogramAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<DICompileUnitAttr> _result_compileUnit;
  ::mlir::FailureOr<DIScopeAttr> _result_scope;
  ::mlir::FailureOr<StringAttr> _result_name;
  ::mlir::FailureOr<StringAttr> _result_linkageName;
  ::mlir::FailureOr<DIFileAttr> _result_file;
  ::mlir::FailureOr<unsigned> _result_line;
  ::mlir::FailureOr<unsigned> _result_scopeLine;
  ::mlir::FailureOr<DISubprogramFlags> _result_subprogramFlags;
  ::mlir::FailureOr<DISubroutineTypeAttr> _result_type;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_compileUnit = false;
  bool _seen_scope = false;
  bool _seen_name = false;
  bool _seen_linkageName = false;
  bool _seen_file = false;
  bool _seen_line = false;
  bool _seen_scopeLine = false;
  bool _seen_subprogramFlags = false;
  bool _seen_type = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_compileUnit && _paramKey == "compileUnit") {
        _seen_compileUnit = true;

        // Parse variable 'compileUnit'
        _result_compileUnit = ::mlir::FieldParser<DICompileUnitAttr>::parse(odsParser);
        if (::mlir::failed(_result_compileUnit)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'compileUnit' which is to be a `DICompileUnitAttr`");
          return {};
        }
      } else if (!_seen_scope && _paramKey == "scope") {
        _seen_scope = true;

        // Parse variable 'scope'
        _result_scope = ::mlir::FieldParser<DIScopeAttr>::parse(odsParser);
        if (::mlir::failed(_result_scope)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'scope' which is to be a `DIScopeAttr`");
          return {};
        }
      } else if (!_seen_name && _paramKey == "name") {
        _seen_name = true;

        // Parse variable 'name'
        _result_name = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_name)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'name' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_linkageName && _paramKey == "linkageName") {
        _seen_linkageName = true;

        // Parse variable 'linkageName'
        _result_linkageName = ::mlir::FieldParser<StringAttr>::parse(odsParser);
        if (::mlir::failed(_result_linkageName)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'linkageName' which is to be a `StringAttr`");
          return {};
        }
      } else if (!_seen_file && _paramKey == "file") {
        _seen_file = true;

        // Parse variable 'file'
        _result_file = ::mlir::FieldParser<DIFileAttr>::parse(odsParser);
        if (::mlir::failed(_result_file)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'file' which is to be a `DIFileAttr`");
          return {};
        }
      } else if (!_seen_line && _paramKey == "line") {
        _seen_line = true;

        // Parse variable 'line'
        _result_line = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_line)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'line' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_scopeLine && _paramKey == "scopeLine") {
        _seen_scopeLine = true;

        // Parse variable 'scopeLine'
        _result_scopeLine = ::mlir::FieldParser<unsigned>::parse(odsParser);
        if (::mlir::failed(_result_scopeLine)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'scopeLine' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_subprogramFlags && _paramKey == "subprogramFlags") {
        _seen_subprogramFlags = true;

        // Parse variable 'subprogramFlags'
        _result_subprogramFlags = ::mlir::FieldParser<DISubprogramFlags>::parse(odsParser);
        if (::mlir::failed(_result_subprogramFlags)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'subprogramFlags' which is to be a `DISubprogramFlags`");
          return {};
        }
      } else if (!_seen_type && _paramKey == "type") {
        _seen_type = true;

        // Parse variable 'type'
        _result_type = ::mlir::FieldParser<DISubroutineTypeAttr>::parse(odsParser);
        if (::mlir::failed(_result_type)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubprogramAttr parameter 'type' which is to be a `DISubroutineTypeAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    do {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
    } while(!odsParser.parseOptionalComma());
    if (!_seen_scope) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "scope";
      return {};
    }
    if (!_seen_file) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "file";
      return {};
    }
    if (!_seen_subprogramFlags) {
      odsParser.emitError(odsParser.getCurrentLocation(), "struct is missing required parameter: ") << "subprogramFlags";
      return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_scope));
  assert(::mlir::succeeded(_result_file));
  assert(::mlir::succeeded(_result_subprogramFlags));
  return DISubprogramAttr::get(odsParser.getContext(),
      DICompileUnitAttr((_result_compileUnit.value_or(DICompileUnitAttr()))),
      DIScopeAttr((*_result_scope)),
      StringAttr((_result_name.value_or(StringAttr()))),
      StringAttr((_result_linkageName.value_or(StringAttr()))),
      DIFileAttr((*_result_file)),
      unsigned((_result_line.value_or(unsigned()))),
      unsigned((_result_scopeLine.value_or(unsigned()))),
      DISubprogramFlags((*_result_subprogramFlags)),
      DISubroutineTypeAttr((_result_type.value_or(DISubroutineTypeAttr()))));
}

void DISubprogramAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getCompileUnit() == DICompileUnitAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "compileUnit = ";
      if (!(getCompileUnit() == DICompileUnitAttr())) {
        odsPrinter.printStrippedAttrOrType(getCompileUnit());
      }
    }
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "scope = ";
    odsPrinter.printStrippedAttrOrType(getScope());
    if (!(getName() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "name = ";
      if (!(getName() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getName());
      }
    }
    if (!(getLinkageName() == StringAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "linkageName = ";
      if (!(getLinkageName() == StringAttr())) {
        odsPrinter.printStrippedAttrOrType(getLinkageName());
      }
    }
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "file = ";
    odsPrinter.printStrippedAttrOrType(getFile());
    if (!(getLine() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "line = ";
      if (!(getLine() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getLine());
      }
    }
    if (!(getScopeLine() == unsigned())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "scopeLine = ";
      if (!(getScopeLine() == unsigned())) {
        odsPrinter.printStrippedAttrOrType(getScopeLine());
      }
    }
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "subprogramFlags = ";
    odsPrinter.printStrippedAttrOrType(getSubprogramFlags());
    if (!(getType() == DISubroutineTypeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "type = ";
      if (!(getType() == DISubroutineTypeAttr())) {
        odsPrinter.printStrippedAttrOrType(getType());
      }
    }
  }
  odsPrinter << ">";
}

DICompileUnitAttr DISubprogramAttr::getCompileUnit() const {
  return getImpl()->compileUnit;
}

DIScopeAttr DISubprogramAttr::getScope() const {
  return getImpl()->scope;
}

StringAttr DISubprogramAttr::getName() const {
  return getImpl()->name;
}

StringAttr DISubprogramAttr::getLinkageName() const {
  return getImpl()->linkageName;
}

DIFileAttr DISubprogramAttr::getFile() const {
  return getImpl()->file;
}

unsigned DISubprogramAttr::getLine() const {
  return getImpl()->line;
}

unsigned DISubprogramAttr::getScopeLine() const {
  return getImpl()->scopeLine;
}

DISubprogramFlags DISubprogramAttr::getSubprogramFlags() const {
  return getImpl()->subprogramFlags;
}

DISubroutineTypeAttr DISubprogramAttr::getType() const {
  return getImpl()->type;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DISubprogramAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DISubrangeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<IntegerAttr, IntegerAttr, IntegerAttr, IntegerAttr>;
  DISubrangeAttrStorage(IntegerAttr count, IntegerAttr lowerBound, IntegerAttr upperBound, IntegerAttr stride) : count(count), lowerBound(lowerBound), upperBound(upperBound), stride(stride) {}

  KeyTy getAsKey() const {
    return KeyTy(count, lowerBound, upperBound, stride);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (count == std::get<0>(tblgenKey)) && (lowerBound == std::get<1>(tblgenKey)) && (upperBound == std::get<2>(tblgenKey)) && (stride == std::get<3>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey));
  }

  static DISubrangeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto count = std::get<0>(tblgenKey);
    auto lowerBound = std::get<1>(tblgenKey);
    auto upperBound = std::get<2>(tblgenKey);
    auto stride = std::get<3>(tblgenKey);
    return new (allocator.allocate<DISubrangeAttrStorage>()) DISubrangeAttrStorage(count, lowerBound, upperBound, stride);
  }

  IntegerAttr count;
  IntegerAttr lowerBound;
  IntegerAttr upperBound;
  IntegerAttr stride;
};
} // namespace detail
DISubrangeAttr DISubrangeAttr::get(::mlir::MLIRContext *context, IntegerAttr count, IntegerAttr lowerBound, IntegerAttr upperBound, IntegerAttr stride) {
  return Base::get(context, count, lowerBound, upperBound, stride);
}

::mlir::Attribute DISubrangeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<IntegerAttr> _result_count;
  ::mlir::FailureOr<IntegerAttr> _result_lowerBound;
  ::mlir::FailureOr<IntegerAttr> _result_upperBound;
  ::mlir::FailureOr<IntegerAttr> _result_stride;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_count = false;
  bool _seen_lowerBound = false;
  bool _seen_upperBound = false;
  bool _seen_stride = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_count && _paramKey == "count") {
        _seen_count = true;

        // Parse variable 'count'
        _result_count = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_count)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubrangeAttr parameter 'count' which is to be a `IntegerAttr`");
          return {};
        }
      } else if (!_seen_lowerBound && _paramKey == "lowerBound") {
        _seen_lowerBound = true;

        // Parse variable 'lowerBound'
        _result_lowerBound = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_lowerBound)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubrangeAttr parameter 'lowerBound' which is to be a `IntegerAttr`");
          return {};
        }
      } else if (!_seen_upperBound && _paramKey == "upperBound") {
        _seen_upperBound = true;

        // Parse variable 'upperBound'
        _result_upperBound = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_upperBound)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubrangeAttr parameter 'upperBound' which is to be a `IntegerAttr`");
          return {};
        }
      } else if (!_seen_stride && _paramKey == "stride") {
        _seen_stride = true;

        // Parse variable 'stride'
        _result_stride = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_stride)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubrangeAttr parameter 'stride' which is to be a `IntegerAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return DISubrangeAttr::get(odsParser.getContext(),
      IntegerAttr((_result_count.value_or(IntegerAttr()))),
      IntegerAttr((_result_lowerBound.value_or(IntegerAttr()))),
      IntegerAttr((_result_upperBound.value_or(IntegerAttr()))),
      IntegerAttr((_result_stride.value_or(IntegerAttr()))));
}

void DISubrangeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getCount() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "count = ";
      if (!(getCount() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getCount());
      }
    }
    if (!(getLowerBound() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "lowerBound = ";
      if (!(getLowerBound() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getLowerBound());
      }
    }
    if (!(getUpperBound() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "upperBound = ";
      if (!(getUpperBound() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getUpperBound());
      }
    }
    if (!(getStride() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "stride = ";
      if (!(getStride() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getStride());
      }
    }
  }
  odsPrinter << ">";
}

IntegerAttr DISubrangeAttr::getCount() const {
  return getImpl()->count;
}

IntegerAttr DISubrangeAttr::getLowerBound() const {
  return getImpl()->lowerBound;
}

IntegerAttr DISubrangeAttr::getUpperBound() const {
  return getImpl()->upperBound;
}

IntegerAttr DISubrangeAttr::getStride() const {
  return getImpl()->stride;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DISubrangeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct DISubroutineTypeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<unsigned, ::llvm::ArrayRef<DITypeAttr>>;
  DISubroutineTypeAttrStorage(unsigned callingConvention, ::llvm::ArrayRef<DITypeAttr> types) : callingConvention(callingConvention), types(types) {}

  KeyTy getAsKey() const {
    return KeyTy(callingConvention, types);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (callingConvention == std::get<0>(tblgenKey)) && (::llvm::ArrayRef<DITypeAttr>(types) == ::llvm::ArrayRef<DITypeAttr>(std::get<1>(tblgenKey)));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey));
  }

  static DISubroutineTypeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto callingConvention = std::get<0>(tblgenKey);
    auto types = std::get<1>(tblgenKey);
    types = allocator.copyInto(types);
    return new (allocator.allocate<DISubroutineTypeAttrStorage>()) DISubroutineTypeAttrStorage(callingConvention, types);
  }

  unsigned callingConvention;
  ::llvm::ArrayRef<DITypeAttr> types;
};
} // namespace detail
DISubroutineTypeAttr DISubroutineTypeAttr::get(::mlir::MLIRContext *context, unsigned callingConvention, ::llvm::ArrayRef<DITypeAttr> types) {
  return Base::get(context, callingConvention, types);
}

DISubroutineTypeAttr DISubroutineTypeAttr::get(::mlir::MLIRContext *context, ArrayRef<DITypeAttr> types) {
  return Base::get(context, /*callingConvention=*/0, types);
}

::mlir::Attribute DISubroutineTypeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<unsigned> _result_callingConvention;
  ::mlir::FailureOr<::llvm::SmallVector<DITypeAttr>> _result_types;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_callingConvention = false;
  bool _seen_types = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_callingConvention && _paramKey == "callingConvention") {
        _seen_callingConvention = true;

        // Parse variable 'callingConvention'
        _result_callingConvention =  [&]() -> FailureOr<unsigned> {
            SMLoc tagLoc = odsParser.getCurrentLocation();
            StringRef name;
            if (odsParser.parseKeyword(&name))
              return failure();

            if (unsigned tag = llvm::dwarf::getCallingConvention(name))
              return tag;
            return odsParser.emitError(tagLoc)
              << "invalid debug info debug info calling convention name: " << name;
          }() ;
        if (::mlir::failed(_result_callingConvention)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubroutineTypeAttr parameter 'callingConvention' which is to be a `unsigned`");
          return {};
        }
      } else if (!_seen_types && _paramKey == "types") {
        _seen_types = true;

        // Parse variable 'types'
        _result_types = ::mlir::FieldParser<::llvm::SmallVector<DITypeAttr>>::parse(odsParser);
        if (::mlir::failed(_result_types)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_DISubroutineTypeAttr parameter 'types' which is to be a `::llvm::ArrayRef<DITypeAttr>`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return DISubroutineTypeAttr::get(odsParser.getContext(),
      unsigned((_result_callingConvention.value_or(0))),
      ::llvm::ArrayRef<DITypeAttr>((_result_types.value_or(::llvm::SmallVector<DITypeAttr>()))));
}

void DISubroutineTypeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getCallingConvention() == 0)) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "callingConvention = ";
      if (!(getCallingConvention() == 0)) {
        odsPrinter << llvm::dwarf::ConventionString(getCallingConvention());
      }
    }
    if (!(::llvm::ArrayRef<DITypeAttr>(getTypes()) == ::llvm::ArrayRef<DITypeAttr>(::llvm::SmallVector<DITypeAttr>()))) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "types = ";
      if (!(::llvm::ArrayRef<DITypeAttr>(getTypes()) == ::llvm::ArrayRef<DITypeAttr>(::llvm::SmallVector<DITypeAttr>()))) {
        odsPrinter.printStrippedAttrOrType(getTypes());
      }
    }
  }
  odsPrinter << ">";
}

unsigned DISubroutineTypeAttr::getCallingConvention() const {
  return getImpl()->callingConvention;
}

::llvm::ArrayRef<DITypeAttr> DISubroutineTypeAttr::getTypes() const {
  return getImpl()->types;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::DISubroutineTypeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct FastmathFlagsAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<::mlir::LLVM::FastmathFlags>;
  FastmathFlagsAttrStorage(::mlir::LLVM::FastmathFlags value) : value(value) {}

  KeyTy getAsKey() const {
    return KeyTy(value);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (value == std::get<0>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey));
  }

  static FastmathFlagsAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto value = std::get<0>(tblgenKey);
    return new (allocator.allocate<FastmathFlagsAttrStorage>()) FastmathFlagsAttrStorage(value);
  }

  ::mlir::LLVM::FastmathFlags value;
};
} // namespace detail
FastmathFlagsAttr FastmathFlagsAttr::get(::mlir::MLIRContext *context, ::mlir::LLVM::FastmathFlags value) {
  return Base::get(context, value);
}

::mlir::Attribute FastmathFlagsAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<::mlir::LLVM::FastmathFlags> _result_value;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};

  // Parse variable 'value'
  _result_value = [&]() -> ::mlir::FailureOr<::mlir::LLVM::FastmathFlags> {
      ::mlir::LLVM::FastmathFlags flags = {};
      auto loc = odsParser.getCurrentLocation();
      ::llvm::StringRef enumKeyword;
      do {
        if (::mlir::failed(odsParser.parseKeyword(&enumKeyword)))
          return ::mlir::failure();
        auto maybeEnum = ::mlir::LLVM::symbolizeFastmathFlags(enumKeyword);
        if (!maybeEnum) {
            return {(::mlir::LogicalResult)(odsParser.emitError(loc) << "expected " << "::mlir::LLVM::FastmathFlags" << " to be one of: " << "none" << ", " << "nnan" << ", " << "ninf" << ", " << "nsz" << ", " << "arcp" << ", " << "contract" << ", " << "afn" << ", " << "reassoc" << ", " << "fast")};
        }
        flags = flags | *maybeEnum;
      } while(::mlir::succeeded(odsParser.parseOptionalComma()));
      return flags;
    }();
  if (::mlir::failed(_result_value)) {
    odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_FastmathFlagsAttr parameter 'value' which is to be a `::mlir::LLVM::FastmathFlags`");
    return {};
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_value));
  return FastmathFlagsAttr::get(odsParser.getContext(),
      ::mlir::LLVM::FastmathFlags((*_result_value)));
}

void FastmathFlagsAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  odsPrinter << stringifyFastmathFlags(getValue());
  odsPrinter << ">";
}

::mlir::LLVM::FastmathFlags FastmathFlagsAttr::getValue() const {
  return getImpl()->value;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::FastmathFlagsAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct MemoryEffectsAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<ModRefInfo, ModRefInfo, ModRefInfo>;
  MemoryEffectsAttrStorage(ModRefInfo other, ModRefInfo argMem, ModRefInfo inaccessibleMem) : other(other), argMem(argMem), inaccessibleMem(inaccessibleMem) {}

  KeyTy getAsKey() const {
    return KeyTy(other, argMem, inaccessibleMem);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (other == std::get<0>(tblgenKey)) && (argMem == std::get<1>(tblgenKey)) && (inaccessibleMem == std::get<2>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey));
  }

  static MemoryEffectsAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto other = std::get<0>(tblgenKey);
    auto argMem = std::get<1>(tblgenKey);
    auto inaccessibleMem = std::get<2>(tblgenKey);
    return new (allocator.allocate<MemoryEffectsAttrStorage>()) MemoryEffectsAttrStorage(other, argMem, inaccessibleMem);
  }

  ModRefInfo other;
  ModRefInfo argMem;
  ModRefInfo inaccessibleMem;
};
} // namespace detail
MemoryEffectsAttr MemoryEffectsAttr::get(::mlir::MLIRContext *context, ModRefInfo other, ModRefInfo argMem, ModRefInfo inaccessibleMem) {
  return Base::get(context, other, argMem, inaccessibleMem);
}

::mlir::Attribute MemoryEffectsAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<ModRefInfo> _result_other;
  ::mlir::FailureOr<ModRefInfo> _result_argMem;
  ::mlir::FailureOr<ModRefInfo> _result_inaccessibleMem;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_other = false;
  bool _seen_argMem = false;
  bool _seen_inaccessibleMem = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_other && _paramKey == "other") {
        _seen_other = true;

        // Parse variable 'other'
        _result_other = ::mlir::FieldParser<ModRefInfo>::parse(odsParser);
        if (::mlir::failed(_result_other)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_MemoryEffectsAttr parameter 'other' which is to be a `ModRefInfo`");
          return {};
        }
      } else if (!_seen_argMem && _paramKey == "argMem") {
        _seen_argMem = true;

        // Parse variable 'argMem'
        _result_argMem = ::mlir::FieldParser<ModRefInfo>::parse(odsParser);
        if (::mlir::failed(_result_argMem)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_MemoryEffectsAttr parameter 'argMem' which is to be a `ModRefInfo`");
          return {};
        }
      } else if (!_seen_inaccessibleMem && _paramKey == "inaccessibleMem") {
        _seen_inaccessibleMem = true;

        // Parse variable 'inaccessibleMem'
        _result_inaccessibleMem = ::mlir::FieldParser<ModRefInfo>::parse(odsParser);
        if (::mlir::failed(_result_inaccessibleMem)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LLVM_MemoryEffectsAttr parameter 'inaccessibleMem' which is to be a `ModRefInfo`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    for (unsigned odsStructIndex = 0; odsStructIndex < 3; ++odsStructIndex) {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
      if ((odsStructIndex != 3 - 1) && odsParser.parseComma())
        return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_other));
  assert(::mlir::succeeded(_result_argMem));
  assert(::mlir::succeeded(_result_inaccessibleMem));
  return MemoryEffectsAttr::get(odsParser.getContext(),
      ModRefInfo((*_result_other)),
      ModRefInfo((*_result_argMem)),
      ModRefInfo((*_result_inaccessibleMem)));
}

void MemoryEffectsAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "other = ";
    odsPrinter.printStrippedAttrOrType(getOther());
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "argMem = ";
    odsPrinter.printStrippedAttrOrType(getArgMem());
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "inaccessibleMem = ";
    odsPrinter.printStrippedAttrOrType(getInaccessibleMem());
  }
  odsPrinter << ">";
}

ModRefInfo MemoryEffectsAttr::getOther() const {
  return getImpl()->other;
}

ModRefInfo MemoryEffectsAttr::getArgMem() const {
  return getImpl()->argMem;
}

ModRefInfo MemoryEffectsAttr::getInaccessibleMem() const {
  return getImpl()->inaccessibleMem;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::MemoryEffectsAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LinkageAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<linkage::Linkage>;
  LinkageAttrStorage(linkage::Linkage linkage) : linkage(linkage) {}

  KeyTy getAsKey() const {
    return KeyTy(linkage);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (linkage == std::get<0>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey));
  }

  static LinkageAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto linkage = std::get<0>(tblgenKey);
    return new (allocator.allocate<LinkageAttrStorage>()) LinkageAttrStorage(linkage);
  }

  linkage::Linkage linkage;
};
} // namespace detail
LinkageAttr LinkageAttr::get(::mlir::MLIRContext *context, linkage::Linkage linkage) {
  return Base::get(context, linkage);
}

::mlir::Attribute LinkageAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<linkage::Linkage> _result_linkage;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};

  // Parse variable 'linkage'
  _result_linkage = ::mlir::FieldParser<linkage::Linkage>::parse(odsParser);
  if (::mlir::failed(_result_linkage)) {
    odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LinkageAttr parameter 'linkage' which is to be a `linkage::Linkage`");
    return {};
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_linkage));
  return LinkageAttr::get(odsParser.getContext(),
      linkage::Linkage((*_result_linkage)));
}

void LinkageAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  odsPrinter.printStrippedAttrOrType(getLinkage());
  odsPrinter << ">";
}

linkage::Linkage LinkageAttr::getLinkage() const {
  return getImpl()->linkage;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LinkageAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopAnnotationAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, LoopVectorizeAttr, LoopInterleaveAttr, LoopUnrollAttr, LoopUnrollAndJamAttr, LoopLICMAttr, LoopDistributeAttr, LoopPipelineAttr, LoopPeeledAttr, LoopUnswitchAttr, BoolAttr, BoolAttr, ::llvm::ArrayRef<SymbolRefAttr>>;
  LoopAnnotationAttrStorage(BoolAttr disableNonforced, LoopVectorizeAttr vectorize, LoopInterleaveAttr interleave, LoopUnrollAttr unroll, LoopUnrollAndJamAttr unrollAndJam, LoopLICMAttr licm, LoopDistributeAttr distribute, LoopPipelineAttr pipeline, LoopPeeledAttr peeled, LoopUnswitchAttr unswitch, BoolAttr mustProgress, BoolAttr isVectorized, ::llvm::ArrayRef<SymbolRefAttr> parallelAccesses) : disableNonforced(disableNonforced), vectorize(vectorize), interleave(interleave), unroll(unroll), unrollAndJam(unrollAndJam), licm(licm), distribute(distribute), pipeline(pipeline), peeled(peeled), unswitch(unswitch), mustProgress(mustProgress), isVectorized(isVectorized), parallelAccesses(parallelAccesses) {}

  KeyTy getAsKey() const {
    return KeyTy(disableNonforced, vectorize, interleave, unroll, unrollAndJam, licm, distribute, pipeline, peeled, unswitch, mustProgress, isVectorized, parallelAccesses);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disableNonforced == std::get<0>(tblgenKey)) && (vectorize == std::get<1>(tblgenKey)) && (interleave == std::get<2>(tblgenKey)) && (unroll == std::get<3>(tblgenKey)) && (unrollAndJam == std::get<4>(tblgenKey)) && (licm == std::get<5>(tblgenKey)) && (distribute == std::get<6>(tblgenKey)) && (pipeline == std::get<7>(tblgenKey)) && (peeled == std::get<8>(tblgenKey)) && (unswitch == std::get<9>(tblgenKey)) && (mustProgress == std::get<10>(tblgenKey)) && (isVectorized == std::get<11>(tblgenKey)) && (::llvm::ArrayRef<SymbolRefAttr>(parallelAccesses) == ::llvm::ArrayRef<SymbolRefAttr>(std::get<12>(tblgenKey)));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey), std::get<7>(tblgenKey), std::get<8>(tblgenKey), std::get<9>(tblgenKey), std::get<10>(tblgenKey), std::get<11>(tblgenKey), std::get<12>(tblgenKey));
  }

  static LoopAnnotationAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disableNonforced = std::get<0>(tblgenKey);
    auto vectorize = std::get<1>(tblgenKey);
    auto interleave = std::get<2>(tblgenKey);
    auto unroll = std::get<3>(tblgenKey);
    auto unrollAndJam = std::get<4>(tblgenKey);
    auto licm = std::get<5>(tblgenKey);
    auto distribute = std::get<6>(tblgenKey);
    auto pipeline = std::get<7>(tblgenKey);
    auto peeled = std::get<8>(tblgenKey);
    auto unswitch = std::get<9>(tblgenKey);
    auto mustProgress = std::get<10>(tblgenKey);
    auto isVectorized = std::get<11>(tblgenKey);
    auto parallelAccesses = std::get<12>(tblgenKey);
    parallelAccesses = allocator.copyInto(parallelAccesses);
    return new (allocator.allocate<LoopAnnotationAttrStorage>()) LoopAnnotationAttrStorage(disableNonforced, vectorize, interleave, unroll, unrollAndJam, licm, distribute, pipeline, peeled, unswitch, mustProgress, isVectorized, parallelAccesses);
  }

  BoolAttr disableNonforced;
  LoopVectorizeAttr vectorize;
  LoopInterleaveAttr interleave;
  LoopUnrollAttr unroll;
  LoopUnrollAndJamAttr unrollAndJam;
  LoopLICMAttr licm;
  LoopDistributeAttr distribute;
  LoopPipelineAttr pipeline;
  LoopPeeledAttr peeled;
  LoopUnswitchAttr unswitch;
  BoolAttr mustProgress;
  BoolAttr isVectorized;
  ::llvm::ArrayRef<SymbolRefAttr> parallelAccesses;
};
} // namespace detail
LoopAnnotationAttr LoopAnnotationAttr::get(::mlir::MLIRContext *context, BoolAttr disableNonforced, LoopVectorizeAttr vectorize, LoopInterleaveAttr interleave, LoopUnrollAttr unroll, LoopUnrollAndJamAttr unrollAndJam, LoopLICMAttr licm, LoopDistributeAttr distribute, LoopPipelineAttr pipeline, LoopPeeledAttr peeled, LoopUnswitchAttr unswitch, BoolAttr mustProgress, BoolAttr isVectorized, ::llvm::ArrayRef<SymbolRefAttr> parallelAccesses) {
  return Base::get(context, disableNonforced, vectorize, interleave, unroll, unrollAndJam, licm, distribute, pipeline, peeled, unswitch, mustProgress, isVectorized, parallelAccesses);
}

::mlir::Attribute LoopAnnotationAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disableNonforced;
  ::mlir::FailureOr<LoopVectorizeAttr> _result_vectorize;
  ::mlir::FailureOr<LoopInterleaveAttr> _result_interleave;
  ::mlir::FailureOr<LoopUnrollAttr> _result_unroll;
  ::mlir::FailureOr<LoopUnrollAndJamAttr> _result_unrollAndJam;
  ::mlir::FailureOr<LoopLICMAttr> _result_licm;
  ::mlir::FailureOr<LoopDistributeAttr> _result_distribute;
  ::mlir::FailureOr<LoopPipelineAttr> _result_pipeline;
  ::mlir::FailureOr<LoopPeeledAttr> _result_peeled;
  ::mlir::FailureOr<LoopUnswitchAttr> _result_unswitch;
  ::mlir::FailureOr<BoolAttr> _result_mustProgress;
  ::mlir::FailureOr<BoolAttr> _result_isVectorized;
  ::mlir::FailureOr<::llvm::SmallVector<SymbolRefAttr>> _result_parallelAccesses;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disableNonforced = false;
  bool _seen_vectorize = false;
  bool _seen_interleave = false;
  bool _seen_unroll = false;
  bool _seen_unrollAndJam = false;
  bool _seen_licm = false;
  bool _seen_distribute = false;
  bool _seen_pipeline = false;
  bool _seen_peeled = false;
  bool _seen_unswitch = false;
  bool _seen_mustProgress = false;
  bool _seen_isVectorized = false;
  bool _seen_parallelAccesses = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disableNonforced && _paramKey == "disableNonforced") {
        _seen_disableNonforced = true;

        // Parse variable 'disableNonforced'
        _result_disableNonforced = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disableNonforced)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'disableNonforced' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_vectorize && _paramKey == "vectorize") {
        _seen_vectorize = true;

        // Parse variable 'vectorize'
        _result_vectorize = ::mlir::FieldParser<LoopVectorizeAttr>::parse(odsParser);
        if (::mlir::failed(_result_vectorize)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'vectorize' which is to be a `LoopVectorizeAttr`");
          return {};
        }
      } else if (!_seen_interleave && _paramKey == "interleave") {
        _seen_interleave = true;

        // Parse variable 'interleave'
        _result_interleave = ::mlir::FieldParser<LoopInterleaveAttr>::parse(odsParser);
        if (::mlir::failed(_result_interleave)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'interleave' which is to be a `LoopInterleaveAttr`");
          return {};
        }
      } else if (!_seen_unroll && _paramKey == "unroll") {
        _seen_unroll = true;

        // Parse variable 'unroll'
        _result_unroll = ::mlir::FieldParser<LoopUnrollAttr>::parse(odsParser);
        if (::mlir::failed(_result_unroll)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'unroll' which is to be a `LoopUnrollAttr`");
          return {};
        }
      } else if (!_seen_unrollAndJam && _paramKey == "unrollAndJam") {
        _seen_unrollAndJam = true;

        // Parse variable 'unrollAndJam'
        _result_unrollAndJam = ::mlir::FieldParser<LoopUnrollAndJamAttr>::parse(odsParser);
        if (::mlir::failed(_result_unrollAndJam)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'unrollAndJam' which is to be a `LoopUnrollAndJamAttr`");
          return {};
        }
      } else if (!_seen_licm && _paramKey == "licm") {
        _seen_licm = true;

        // Parse variable 'licm'
        _result_licm = ::mlir::FieldParser<LoopLICMAttr>::parse(odsParser);
        if (::mlir::failed(_result_licm)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'licm' which is to be a `LoopLICMAttr`");
          return {};
        }
      } else if (!_seen_distribute && _paramKey == "distribute") {
        _seen_distribute = true;

        // Parse variable 'distribute'
        _result_distribute = ::mlir::FieldParser<LoopDistributeAttr>::parse(odsParser);
        if (::mlir::failed(_result_distribute)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'distribute' which is to be a `LoopDistributeAttr`");
          return {};
        }
      } else if (!_seen_pipeline && _paramKey == "pipeline") {
        _seen_pipeline = true;

        // Parse variable 'pipeline'
        _result_pipeline = ::mlir::FieldParser<LoopPipelineAttr>::parse(odsParser);
        if (::mlir::failed(_result_pipeline)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'pipeline' which is to be a `LoopPipelineAttr`");
          return {};
        }
      } else if (!_seen_peeled && _paramKey == "peeled") {
        _seen_peeled = true;

        // Parse variable 'peeled'
        _result_peeled = ::mlir::FieldParser<LoopPeeledAttr>::parse(odsParser);
        if (::mlir::failed(_result_peeled)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'peeled' which is to be a `LoopPeeledAttr`");
          return {};
        }
      } else if (!_seen_unswitch && _paramKey == "unswitch") {
        _seen_unswitch = true;

        // Parse variable 'unswitch'
        _result_unswitch = ::mlir::FieldParser<LoopUnswitchAttr>::parse(odsParser);
        if (::mlir::failed(_result_unswitch)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'unswitch' which is to be a `LoopUnswitchAttr`");
          return {};
        }
      } else if (!_seen_mustProgress && _paramKey == "mustProgress") {
        _seen_mustProgress = true;

        // Parse variable 'mustProgress'
        _result_mustProgress = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_mustProgress)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'mustProgress' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_isVectorized && _paramKey == "isVectorized") {
        _seen_isVectorized = true;

        // Parse variable 'isVectorized'
        _result_isVectorized = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_isVectorized)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'isVectorized' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_parallelAccesses && _paramKey == "parallelAccesses") {
        _seen_parallelAccesses = true;

        // Parse variable 'parallelAccesses'
        _result_parallelAccesses = ::mlir::FieldParser<::llvm::SmallVector<SymbolRefAttr>>::parse(odsParser);
        if (::mlir::failed(_result_parallelAccesses)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopAnnotationAttr parameter 'parallelAccesses' which is to be a `::llvm::ArrayRef<SymbolRefAttr>`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopAnnotationAttr::get(odsParser.getContext(),
      BoolAttr((_result_disableNonforced.value_or(BoolAttr()))),
      LoopVectorizeAttr((_result_vectorize.value_or(LoopVectorizeAttr()))),
      LoopInterleaveAttr((_result_interleave.value_or(LoopInterleaveAttr()))),
      LoopUnrollAttr((_result_unroll.value_or(LoopUnrollAttr()))),
      LoopUnrollAndJamAttr((_result_unrollAndJam.value_or(LoopUnrollAndJamAttr()))),
      LoopLICMAttr((_result_licm.value_or(LoopLICMAttr()))),
      LoopDistributeAttr((_result_distribute.value_or(LoopDistributeAttr()))),
      LoopPipelineAttr((_result_pipeline.value_or(LoopPipelineAttr()))),
      LoopPeeledAttr((_result_peeled.value_or(LoopPeeledAttr()))),
      LoopUnswitchAttr((_result_unswitch.value_or(LoopUnswitchAttr()))),
      BoolAttr((_result_mustProgress.value_or(BoolAttr()))),
      BoolAttr((_result_isVectorized.value_or(BoolAttr()))),
      ::llvm::ArrayRef<SymbolRefAttr>((_result_parallelAccesses.value_or(::llvm::SmallVector<SymbolRefAttr>()))));
}

void LoopAnnotationAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisableNonforced() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disableNonforced = ";
      if (!(getDisableNonforced() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisableNonforced());
      }
    }
    if (!(getVectorize() == LoopVectorizeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "vectorize = ";
      if (!(getVectorize() == LoopVectorizeAttr())) {
        odsPrinter.printStrippedAttrOrType(getVectorize());
      }
    }
    if (!(getInterleave() == LoopInterleaveAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "interleave = ";
      if (!(getInterleave() == LoopInterleaveAttr())) {
        odsPrinter.printStrippedAttrOrType(getInterleave());
      }
    }
    if (!(getUnroll() == LoopUnrollAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "unroll = ";
      if (!(getUnroll() == LoopUnrollAttr())) {
        odsPrinter.printStrippedAttrOrType(getUnroll());
      }
    }
    if (!(getUnrollAndJam() == LoopUnrollAndJamAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "unrollAndJam = ";
      if (!(getUnrollAndJam() == LoopUnrollAndJamAttr())) {
        odsPrinter.printStrippedAttrOrType(getUnrollAndJam());
      }
    }
    if (!(getLicm() == LoopLICMAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "licm = ";
      if (!(getLicm() == LoopLICMAttr())) {
        odsPrinter.printStrippedAttrOrType(getLicm());
      }
    }
    if (!(getDistribute() == LoopDistributeAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "distribute = ";
      if (!(getDistribute() == LoopDistributeAttr())) {
        odsPrinter.printStrippedAttrOrType(getDistribute());
      }
    }
    if (!(getPipeline() == LoopPipelineAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "pipeline = ";
      if (!(getPipeline() == LoopPipelineAttr())) {
        odsPrinter.printStrippedAttrOrType(getPipeline());
      }
    }
    if (!(getPeeled() == LoopPeeledAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "peeled = ";
      if (!(getPeeled() == LoopPeeledAttr())) {
        odsPrinter.printStrippedAttrOrType(getPeeled());
      }
    }
    if (!(getUnswitch() == LoopUnswitchAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "unswitch = ";
      if (!(getUnswitch() == LoopUnswitchAttr())) {
        odsPrinter.printStrippedAttrOrType(getUnswitch());
      }
    }
    if (!(getMustProgress() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "mustProgress = ";
      if (!(getMustProgress() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getMustProgress());
      }
    }
    if (!(getIsVectorized() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "isVectorized = ";
      if (!(getIsVectorized() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getIsVectorized());
      }
    }
    if (!(::llvm::ArrayRef<SymbolRefAttr>(getParallelAccesses()) == ::llvm::ArrayRef<SymbolRefAttr>(::llvm::SmallVector<SymbolRefAttr>()))) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "parallelAccesses = ";
      if (!(::llvm::ArrayRef<SymbolRefAttr>(getParallelAccesses()) == ::llvm::ArrayRef<SymbolRefAttr>(::llvm::SmallVector<SymbolRefAttr>()))) {
        odsPrinter.printStrippedAttrOrType(getParallelAccesses());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopAnnotationAttr::getDisableNonforced() const {
  return getImpl()->disableNonforced;
}

LoopVectorizeAttr LoopAnnotationAttr::getVectorize() const {
  return getImpl()->vectorize;
}

LoopInterleaveAttr LoopAnnotationAttr::getInterleave() const {
  return getImpl()->interleave;
}

LoopUnrollAttr LoopAnnotationAttr::getUnroll() const {
  return getImpl()->unroll;
}

LoopUnrollAndJamAttr LoopAnnotationAttr::getUnrollAndJam() const {
  return getImpl()->unrollAndJam;
}

LoopLICMAttr LoopAnnotationAttr::getLicm() const {
  return getImpl()->licm;
}

LoopDistributeAttr LoopAnnotationAttr::getDistribute() const {
  return getImpl()->distribute;
}

LoopPipelineAttr LoopAnnotationAttr::getPipeline() const {
  return getImpl()->pipeline;
}

LoopPeeledAttr LoopAnnotationAttr::getPeeled() const {
  return getImpl()->peeled;
}

LoopUnswitchAttr LoopAnnotationAttr::getUnswitch() const {
  return getImpl()->unswitch;
}

BoolAttr LoopAnnotationAttr::getMustProgress() const {
  return getImpl()->mustProgress;
}

BoolAttr LoopAnnotationAttr::getIsVectorized() const {
  return getImpl()->isVectorized;
}

::llvm::ArrayRef<SymbolRefAttr> LoopAnnotationAttr::getParallelAccesses() const {
  return getImpl()->parallelAccesses;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopAnnotationAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopDistributeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr>;
  LoopDistributeAttrStorage(BoolAttr disable, LoopAnnotationAttr followupCoincident, LoopAnnotationAttr followupSequential, LoopAnnotationAttr followupFallback, LoopAnnotationAttr followupAll) : disable(disable), followupCoincident(followupCoincident), followupSequential(followupSequential), followupFallback(followupFallback), followupAll(followupAll) {}

  KeyTy getAsKey() const {
    return KeyTy(disable, followupCoincident, followupSequential, followupFallback, followupAll);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disable == std::get<0>(tblgenKey)) && (followupCoincident == std::get<1>(tblgenKey)) && (followupSequential == std::get<2>(tblgenKey)) && (followupFallback == std::get<3>(tblgenKey)) && (followupAll == std::get<4>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey));
  }

  static LoopDistributeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disable = std::get<0>(tblgenKey);
    auto followupCoincident = std::get<1>(tblgenKey);
    auto followupSequential = std::get<2>(tblgenKey);
    auto followupFallback = std::get<3>(tblgenKey);
    auto followupAll = std::get<4>(tblgenKey);
    return new (allocator.allocate<LoopDistributeAttrStorage>()) LoopDistributeAttrStorage(disable, followupCoincident, followupSequential, followupFallback, followupAll);
  }

  BoolAttr disable;
  LoopAnnotationAttr followupCoincident;
  LoopAnnotationAttr followupSequential;
  LoopAnnotationAttr followupFallback;
  LoopAnnotationAttr followupAll;
};
} // namespace detail
LoopDistributeAttr LoopDistributeAttr::get(::mlir::MLIRContext *context, BoolAttr disable, LoopAnnotationAttr followupCoincident, LoopAnnotationAttr followupSequential, LoopAnnotationAttr followupFallback, LoopAnnotationAttr followupAll) {
  return Base::get(context, disable, followupCoincident, followupSequential, followupFallback, followupAll);
}

::mlir::Attribute LoopDistributeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disable;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupCoincident;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupSequential;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupFallback;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupAll;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disable = false;
  bool _seen_followupCoincident = false;
  bool _seen_followupSequential = false;
  bool _seen_followupFallback = false;
  bool _seen_followupAll = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disable && _paramKey == "disable") {
        _seen_disable = true;

        // Parse variable 'disable'
        _result_disable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopDistributeAttr parameter 'disable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_followupCoincident && _paramKey == "followupCoincident") {
        _seen_followupCoincident = true;

        // Parse variable 'followupCoincident'
        _result_followupCoincident = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupCoincident)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopDistributeAttr parameter 'followupCoincident' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupSequential && _paramKey == "followupSequential") {
        _seen_followupSequential = true;

        // Parse variable 'followupSequential'
        _result_followupSequential = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupSequential)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopDistributeAttr parameter 'followupSequential' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupFallback && _paramKey == "followupFallback") {
        _seen_followupFallback = true;

        // Parse variable 'followupFallback'
        _result_followupFallback = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupFallback)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopDistributeAttr parameter 'followupFallback' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupAll && _paramKey == "followupAll") {
        _seen_followupAll = true;

        // Parse variable 'followupAll'
        _result_followupAll = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupAll)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopDistributeAttr parameter 'followupAll' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopDistributeAttr::get(odsParser.getContext(),
      BoolAttr((_result_disable.value_or(BoolAttr()))),
      LoopAnnotationAttr((_result_followupCoincident.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupSequential.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupFallback.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupAll.value_or(LoopAnnotationAttr()))));
}

void LoopDistributeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disable = ";
      if (!(getDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisable());
      }
    }
    if (!(getFollowupCoincident() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupCoincident = ";
      if (!(getFollowupCoincident() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupCoincident());
      }
    }
    if (!(getFollowupSequential() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupSequential = ";
      if (!(getFollowupSequential() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupSequential());
      }
    }
    if (!(getFollowupFallback() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupFallback = ";
      if (!(getFollowupFallback() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupFallback());
      }
    }
    if (!(getFollowupAll() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupAll = ";
      if (!(getFollowupAll() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupAll());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopDistributeAttr::getDisable() const {
  return getImpl()->disable;
}

LoopAnnotationAttr LoopDistributeAttr::getFollowupCoincident() const {
  return getImpl()->followupCoincident;
}

LoopAnnotationAttr LoopDistributeAttr::getFollowupSequential() const {
  return getImpl()->followupSequential;
}

LoopAnnotationAttr LoopDistributeAttr::getFollowupFallback() const {
  return getImpl()->followupFallback;
}

LoopAnnotationAttr LoopDistributeAttr::getFollowupAll() const {
  return getImpl()->followupAll;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopDistributeAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopInterleaveAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<IntegerAttr>;
  LoopInterleaveAttrStorage(IntegerAttr count) : count(count) {}

  KeyTy getAsKey() const {
    return KeyTy(count);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (count == std::get<0>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey));
  }

  static LoopInterleaveAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto count = std::get<0>(tblgenKey);
    return new (allocator.allocate<LoopInterleaveAttrStorage>()) LoopInterleaveAttrStorage(count);
  }

  IntegerAttr count;
};
} // namespace detail
LoopInterleaveAttr LoopInterleaveAttr::get(::mlir::MLIRContext *context, IntegerAttr count) {
  return Base::get(context, count);
}

::mlir::Attribute LoopInterleaveAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<IntegerAttr> _result_count;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_count = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_count && _paramKey == "count") {
        _seen_count = true;

        // Parse variable 'count'
        _result_count = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_count)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopInterleaveAttr parameter 'count' which is to be a `IntegerAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    for (unsigned odsStructIndex = 0; odsStructIndex < 1; ++odsStructIndex) {
      ::llvm::StringRef _paramKey;
      if (odsParser.parseKeyword(&_paramKey)) {
        odsParser.emitError(odsParser.getCurrentLocation(),
                           "expected a parameter name in struct");
        return {};
      }
      if (!_loop_body(_paramKey)) return {};
      if ((odsStructIndex != 1 - 1) && odsParser.parseComma())
        return {};
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  assert(::mlir::succeeded(_result_count));
  return LoopInterleaveAttr::get(odsParser.getContext(),
      IntegerAttr((*_result_count)));
}

void LoopInterleaveAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!_firstPrinted) odsPrinter << ", ";
    _firstPrinted = false;
    odsPrinter << "count = ";
    odsPrinter.printStrippedAttrOrType(getCount());
  }
  odsPrinter << ">";
}

IntegerAttr LoopInterleaveAttr::getCount() const {
  return getImpl()->count;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopInterleaveAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopLICMAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, BoolAttr>;
  LoopLICMAttrStorage(BoolAttr disable, BoolAttr versioningDisable) : disable(disable), versioningDisable(versioningDisable) {}

  KeyTy getAsKey() const {
    return KeyTy(disable, versioningDisable);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disable == std::get<0>(tblgenKey)) && (versioningDisable == std::get<1>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey));
  }

  static LoopLICMAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disable = std::get<0>(tblgenKey);
    auto versioningDisable = std::get<1>(tblgenKey);
    return new (allocator.allocate<LoopLICMAttrStorage>()) LoopLICMAttrStorage(disable, versioningDisable);
  }

  BoolAttr disable;
  BoolAttr versioningDisable;
};
} // namespace detail
LoopLICMAttr LoopLICMAttr::get(::mlir::MLIRContext *context, BoolAttr disable, BoolAttr versioningDisable) {
  return Base::get(context, disable, versioningDisable);
}

::mlir::Attribute LoopLICMAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disable;
  ::mlir::FailureOr<BoolAttr> _result_versioningDisable;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disable = false;
  bool _seen_versioningDisable = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disable && _paramKey == "disable") {
        _seen_disable = true;

        // Parse variable 'disable'
        _result_disable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopLICMAttr parameter 'disable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_versioningDisable && _paramKey == "versioningDisable") {
        _seen_versioningDisable = true;

        // Parse variable 'versioningDisable'
        _result_versioningDisable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_versioningDisable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopLICMAttr parameter 'versioningDisable' which is to be a `BoolAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopLICMAttr::get(odsParser.getContext(),
      BoolAttr((_result_disable.value_or(BoolAttr()))),
      BoolAttr((_result_versioningDisable.value_or(BoolAttr()))));
}

void LoopLICMAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disable = ";
      if (!(getDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisable());
      }
    }
    if (!(getVersioningDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "versioningDisable = ";
      if (!(getVersioningDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getVersioningDisable());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopLICMAttr::getDisable() const {
  return getImpl()->disable;
}

BoolAttr LoopLICMAttr::getVersioningDisable() const {
  return getImpl()->versioningDisable;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopLICMAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopPeeledAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<IntegerAttr>;
  LoopPeeledAttrStorage(IntegerAttr count) : count(count) {}

  KeyTy getAsKey() const {
    return KeyTy(count);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (count == std::get<0>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey));
  }

  static LoopPeeledAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto count = std::get<0>(tblgenKey);
    return new (allocator.allocate<LoopPeeledAttrStorage>()) LoopPeeledAttrStorage(count);
  }

  IntegerAttr count;
};
} // namespace detail
LoopPeeledAttr LoopPeeledAttr::get(::mlir::MLIRContext *context, IntegerAttr count) {
  return Base::get(context, count);
}

::mlir::Attribute LoopPeeledAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<IntegerAttr> _result_count;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_count = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_count && _paramKey == "count") {
        _seen_count = true;

        // Parse variable 'count'
        _result_count = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_count)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopPeeledAttr parameter 'count' which is to be a `IntegerAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopPeeledAttr::get(odsParser.getContext(),
      IntegerAttr((_result_count.value_or(IntegerAttr()))));
}

void LoopPeeledAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getCount() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "count = ";
      if (!(getCount() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getCount());
      }
    }
  }
  odsPrinter << ">";
}

IntegerAttr LoopPeeledAttr::getCount() const {
  return getImpl()->count;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopPeeledAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopPipelineAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, IntegerAttr>;
  LoopPipelineAttrStorage(BoolAttr disable, IntegerAttr initiationinterval) : disable(disable), initiationinterval(initiationinterval) {}

  KeyTy getAsKey() const {
    return KeyTy(disable, initiationinterval);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disable == std::get<0>(tblgenKey)) && (initiationinterval == std::get<1>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey));
  }

  static LoopPipelineAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disable = std::get<0>(tblgenKey);
    auto initiationinterval = std::get<1>(tblgenKey);
    return new (allocator.allocate<LoopPipelineAttrStorage>()) LoopPipelineAttrStorage(disable, initiationinterval);
  }

  BoolAttr disable;
  IntegerAttr initiationinterval;
};
} // namespace detail
LoopPipelineAttr LoopPipelineAttr::get(::mlir::MLIRContext *context, BoolAttr disable, IntegerAttr initiationinterval) {
  return Base::get(context, disable, initiationinterval);
}

::mlir::Attribute LoopPipelineAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disable;
  ::mlir::FailureOr<IntegerAttr> _result_initiationinterval;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disable = false;
  bool _seen_initiationinterval = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disable && _paramKey == "disable") {
        _seen_disable = true;

        // Parse variable 'disable'
        _result_disable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopPipelineAttr parameter 'disable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_initiationinterval && _paramKey == "initiationinterval") {
        _seen_initiationinterval = true;

        // Parse variable 'initiationinterval'
        _result_initiationinterval = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_initiationinterval)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopPipelineAttr parameter 'initiationinterval' which is to be a `IntegerAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopPipelineAttr::get(odsParser.getContext(),
      BoolAttr((_result_disable.value_or(BoolAttr()))),
      IntegerAttr((_result_initiationinterval.value_or(IntegerAttr()))));
}

void LoopPipelineAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disable = ";
      if (!(getDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisable());
      }
    }
    if (!(getInitiationinterval() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "initiationinterval = ";
      if (!(getInitiationinterval() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getInitiationinterval());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopPipelineAttr::getDisable() const {
  return getImpl()->disable;
}

IntegerAttr LoopPipelineAttr::getInitiationinterval() const {
  return getImpl()->initiationinterval;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopPipelineAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopUnrollAndJamAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, IntegerAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr>;
  LoopUnrollAndJamAttrStorage(BoolAttr disable, IntegerAttr count, LoopAnnotationAttr followupOuter, LoopAnnotationAttr followupInner, LoopAnnotationAttr followupRemainderOuter, LoopAnnotationAttr followupRemainderInner, LoopAnnotationAttr followupAll) : disable(disable), count(count), followupOuter(followupOuter), followupInner(followupInner), followupRemainderOuter(followupRemainderOuter), followupRemainderInner(followupRemainderInner), followupAll(followupAll) {}

  KeyTy getAsKey() const {
    return KeyTy(disable, count, followupOuter, followupInner, followupRemainderOuter, followupRemainderInner, followupAll);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disable == std::get<0>(tblgenKey)) && (count == std::get<1>(tblgenKey)) && (followupOuter == std::get<2>(tblgenKey)) && (followupInner == std::get<3>(tblgenKey)) && (followupRemainderOuter == std::get<4>(tblgenKey)) && (followupRemainderInner == std::get<5>(tblgenKey)) && (followupAll == std::get<6>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey));
  }

  static LoopUnrollAndJamAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disable = std::get<0>(tblgenKey);
    auto count = std::get<1>(tblgenKey);
    auto followupOuter = std::get<2>(tblgenKey);
    auto followupInner = std::get<3>(tblgenKey);
    auto followupRemainderOuter = std::get<4>(tblgenKey);
    auto followupRemainderInner = std::get<5>(tblgenKey);
    auto followupAll = std::get<6>(tblgenKey);
    return new (allocator.allocate<LoopUnrollAndJamAttrStorage>()) LoopUnrollAndJamAttrStorage(disable, count, followupOuter, followupInner, followupRemainderOuter, followupRemainderInner, followupAll);
  }

  BoolAttr disable;
  IntegerAttr count;
  LoopAnnotationAttr followupOuter;
  LoopAnnotationAttr followupInner;
  LoopAnnotationAttr followupRemainderOuter;
  LoopAnnotationAttr followupRemainderInner;
  LoopAnnotationAttr followupAll;
};
} // namespace detail
LoopUnrollAndJamAttr LoopUnrollAndJamAttr::get(::mlir::MLIRContext *context, BoolAttr disable, IntegerAttr count, LoopAnnotationAttr followupOuter, LoopAnnotationAttr followupInner, LoopAnnotationAttr followupRemainderOuter, LoopAnnotationAttr followupRemainderInner, LoopAnnotationAttr followupAll) {
  return Base::get(context, disable, count, followupOuter, followupInner, followupRemainderOuter, followupRemainderInner, followupAll);
}

::mlir::Attribute LoopUnrollAndJamAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disable;
  ::mlir::FailureOr<IntegerAttr> _result_count;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupOuter;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupInner;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupRemainderOuter;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupRemainderInner;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupAll;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disable = false;
  bool _seen_count = false;
  bool _seen_followupOuter = false;
  bool _seen_followupInner = false;
  bool _seen_followupRemainderOuter = false;
  bool _seen_followupRemainderInner = false;
  bool _seen_followupAll = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disable && _paramKey == "disable") {
        _seen_disable = true;

        // Parse variable 'disable'
        _result_disable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'disable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_count && _paramKey == "count") {
        _seen_count = true;

        // Parse variable 'count'
        _result_count = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_count)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'count' which is to be a `IntegerAttr`");
          return {};
        }
      } else if (!_seen_followupOuter && _paramKey == "followupOuter") {
        _seen_followupOuter = true;

        // Parse variable 'followupOuter'
        _result_followupOuter = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupOuter)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'followupOuter' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupInner && _paramKey == "followupInner") {
        _seen_followupInner = true;

        // Parse variable 'followupInner'
        _result_followupInner = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupInner)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'followupInner' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupRemainderOuter && _paramKey == "followupRemainderOuter") {
        _seen_followupRemainderOuter = true;

        // Parse variable 'followupRemainderOuter'
        _result_followupRemainderOuter = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupRemainderOuter)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'followupRemainderOuter' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupRemainderInner && _paramKey == "followupRemainderInner") {
        _seen_followupRemainderInner = true;

        // Parse variable 'followupRemainderInner'
        _result_followupRemainderInner = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupRemainderInner)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'followupRemainderInner' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupAll && _paramKey == "followupAll") {
        _seen_followupAll = true;

        // Parse variable 'followupAll'
        _result_followupAll = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupAll)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAndJamAttr parameter 'followupAll' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopUnrollAndJamAttr::get(odsParser.getContext(),
      BoolAttr((_result_disable.value_or(BoolAttr()))),
      IntegerAttr((_result_count.value_or(IntegerAttr()))),
      LoopAnnotationAttr((_result_followupOuter.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupInner.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupRemainderOuter.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupRemainderInner.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupAll.value_or(LoopAnnotationAttr()))));
}

void LoopUnrollAndJamAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disable = ";
      if (!(getDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisable());
      }
    }
    if (!(getCount() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "count = ";
      if (!(getCount() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getCount());
      }
    }
    if (!(getFollowupOuter() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupOuter = ";
      if (!(getFollowupOuter() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupOuter());
      }
    }
    if (!(getFollowupInner() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupInner = ";
      if (!(getFollowupInner() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupInner());
      }
    }
    if (!(getFollowupRemainderOuter() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupRemainderOuter = ";
      if (!(getFollowupRemainderOuter() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupRemainderOuter());
      }
    }
    if (!(getFollowupRemainderInner() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupRemainderInner = ";
      if (!(getFollowupRemainderInner() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupRemainderInner());
      }
    }
    if (!(getFollowupAll() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupAll = ";
      if (!(getFollowupAll() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupAll());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopUnrollAndJamAttr::getDisable() const {
  return getImpl()->disable;
}

IntegerAttr LoopUnrollAndJamAttr::getCount() const {
  return getImpl()->count;
}

LoopAnnotationAttr LoopUnrollAndJamAttr::getFollowupOuter() const {
  return getImpl()->followupOuter;
}

LoopAnnotationAttr LoopUnrollAndJamAttr::getFollowupInner() const {
  return getImpl()->followupInner;
}

LoopAnnotationAttr LoopUnrollAndJamAttr::getFollowupRemainderOuter() const {
  return getImpl()->followupRemainderOuter;
}

LoopAnnotationAttr LoopUnrollAndJamAttr::getFollowupRemainderInner() const {
  return getImpl()->followupRemainderInner;
}

LoopAnnotationAttr LoopUnrollAndJamAttr::getFollowupAll() const {
  return getImpl()->followupAll;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopUnrollAndJamAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopUnrollAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, IntegerAttr, BoolAttr, BoolAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr>;
  LoopUnrollAttrStorage(BoolAttr disable, IntegerAttr count, BoolAttr runtimeDisable, BoolAttr full, LoopAnnotationAttr followupUnrolled, LoopAnnotationAttr followupRemainder, LoopAnnotationAttr followupAll) : disable(disable), count(count), runtimeDisable(runtimeDisable), full(full), followupUnrolled(followupUnrolled), followupRemainder(followupRemainder), followupAll(followupAll) {}

  KeyTy getAsKey() const {
    return KeyTy(disable, count, runtimeDisable, full, followupUnrolled, followupRemainder, followupAll);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disable == std::get<0>(tblgenKey)) && (count == std::get<1>(tblgenKey)) && (runtimeDisable == std::get<2>(tblgenKey)) && (full == std::get<3>(tblgenKey)) && (followupUnrolled == std::get<4>(tblgenKey)) && (followupRemainder == std::get<5>(tblgenKey)) && (followupAll == std::get<6>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey));
  }

  static LoopUnrollAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disable = std::get<0>(tblgenKey);
    auto count = std::get<1>(tblgenKey);
    auto runtimeDisable = std::get<2>(tblgenKey);
    auto full = std::get<3>(tblgenKey);
    auto followupUnrolled = std::get<4>(tblgenKey);
    auto followupRemainder = std::get<5>(tblgenKey);
    auto followupAll = std::get<6>(tblgenKey);
    return new (allocator.allocate<LoopUnrollAttrStorage>()) LoopUnrollAttrStorage(disable, count, runtimeDisable, full, followupUnrolled, followupRemainder, followupAll);
  }

  BoolAttr disable;
  IntegerAttr count;
  BoolAttr runtimeDisable;
  BoolAttr full;
  LoopAnnotationAttr followupUnrolled;
  LoopAnnotationAttr followupRemainder;
  LoopAnnotationAttr followupAll;
};
} // namespace detail
LoopUnrollAttr LoopUnrollAttr::get(::mlir::MLIRContext *context, BoolAttr disable, IntegerAttr count, BoolAttr runtimeDisable, BoolAttr full, LoopAnnotationAttr followupUnrolled, LoopAnnotationAttr followupRemainder, LoopAnnotationAttr followupAll) {
  return Base::get(context, disable, count, runtimeDisable, full, followupUnrolled, followupRemainder, followupAll);
}

::mlir::Attribute LoopUnrollAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disable;
  ::mlir::FailureOr<IntegerAttr> _result_count;
  ::mlir::FailureOr<BoolAttr> _result_runtimeDisable;
  ::mlir::FailureOr<BoolAttr> _result_full;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupUnrolled;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupRemainder;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupAll;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disable = false;
  bool _seen_count = false;
  bool _seen_runtimeDisable = false;
  bool _seen_full = false;
  bool _seen_followupUnrolled = false;
  bool _seen_followupRemainder = false;
  bool _seen_followupAll = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disable && _paramKey == "disable") {
        _seen_disable = true;

        // Parse variable 'disable'
        _result_disable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'disable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_count && _paramKey == "count") {
        _seen_count = true;

        // Parse variable 'count'
        _result_count = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_count)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'count' which is to be a `IntegerAttr`");
          return {};
        }
      } else if (!_seen_runtimeDisable && _paramKey == "runtimeDisable") {
        _seen_runtimeDisable = true;

        // Parse variable 'runtimeDisable'
        _result_runtimeDisable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_runtimeDisable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'runtimeDisable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_full && _paramKey == "full") {
        _seen_full = true;

        // Parse variable 'full'
        _result_full = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_full)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'full' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_followupUnrolled && _paramKey == "followupUnrolled") {
        _seen_followupUnrolled = true;

        // Parse variable 'followupUnrolled'
        _result_followupUnrolled = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupUnrolled)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'followupUnrolled' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupRemainder && _paramKey == "followupRemainder") {
        _seen_followupRemainder = true;

        // Parse variable 'followupRemainder'
        _result_followupRemainder = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupRemainder)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'followupRemainder' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupAll && _paramKey == "followupAll") {
        _seen_followupAll = true;

        // Parse variable 'followupAll'
        _result_followupAll = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupAll)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnrollAttr parameter 'followupAll' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopUnrollAttr::get(odsParser.getContext(),
      BoolAttr((_result_disable.value_or(BoolAttr()))),
      IntegerAttr((_result_count.value_or(IntegerAttr()))),
      BoolAttr((_result_runtimeDisable.value_or(BoolAttr()))),
      BoolAttr((_result_full.value_or(BoolAttr()))),
      LoopAnnotationAttr((_result_followupUnrolled.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupRemainder.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupAll.value_or(LoopAnnotationAttr()))));
}

void LoopUnrollAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disable = ";
      if (!(getDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisable());
      }
    }
    if (!(getCount() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "count = ";
      if (!(getCount() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getCount());
      }
    }
    if (!(getRuntimeDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "runtimeDisable = ";
      if (!(getRuntimeDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getRuntimeDisable());
      }
    }
    if (!(getFull() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "full = ";
      if (!(getFull() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getFull());
      }
    }
    if (!(getFollowupUnrolled() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupUnrolled = ";
      if (!(getFollowupUnrolled() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupUnrolled());
      }
    }
    if (!(getFollowupRemainder() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupRemainder = ";
      if (!(getFollowupRemainder() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupRemainder());
      }
    }
    if (!(getFollowupAll() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupAll = ";
      if (!(getFollowupAll() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupAll());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopUnrollAttr::getDisable() const {
  return getImpl()->disable;
}

IntegerAttr LoopUnrollAttr::getCount() const {
  return getImpl()->count;
}

BoolAttr LoopUnrollAttr::getRuntimeDisable() const {
  return getImpl()->runtimeDisable;
}

BoolAttr LoopUnrollAttr::getFull() const {
  return getImpl()->full;
}

LoopAnnotationAttr LoopUnrollAttr::getFollowupUnrolled() const {
  return getImpl()->followupUnrolled;
}

LoopAnnotationAttr LoopUnrollAttr::getFollowupRemainder() const {
  return getImpl()->followupRemainder;
}

LoopAnnotationAttr LoopUnrollAttr::getFollowupAll() const {
  return getImpl()->followupAll;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopUnrollAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopUnswitchAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr>;
  LoopUnswitchAttrStorage(BoolAttr partialDisable) : partialDisable(partialDisable) {}

  KeyTy getAsKey() const {
    return KeyTy(partialDisable);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (partialDisable == std::get<0>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey));
  }

  static LoopUnswitchAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto partialDisable = std::get<0>(tblgenKey);
    return new (allocator.allocate<LoopUnswitchAttrStorage>()) LoopUnswitchAttrStorage(partialDisable);
  }

  BoolAttr partialDisable;
};
} // namespace detail
LoopUnswitchAttr LoopUnswitchAttr::get(::mlir::MLIRContext *context, BoolAttr partialDisable) {
  return Base::get(context, partialDisable);
}

::mlir::Attribute LoopUnswitchAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_partialDisable;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_partialDisable = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_partialDisable && _paramKey == "partialDisable") {
        _seen_partialDisable = true;

        // Parse variable 'partialDisable'
        _result_partialDisable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_partialDisable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopUnswitchAttr parameter 'partialDisable' which is to be a `BoolAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopUnswitchAttr::get(odsParser.getContext(),
      BoolAttr((_result_partialDisable.value_or(BoolAttr()))));
}

void LoopUnswitchAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getPartialDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "partialDisable = ";
      if (!(getPartialDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getPartialDisable());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopUnswitchAttr::getPartialDisable() const {
  return getImpl()->partialDisable;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopUnswitchAttr)
namespace mlir {
namespace LLVM {
namespace detail {
struct LoopVectorizeAttrStorage : public ::mlir::AttributeStorage {
  using KeyTy = std::tuple<BoolAttr, BoolAttr, BoolAttr, IntegerAttr, LoopAnnotationAttr, LoopAnnotationAttr, LoopAnnotationAttr>;
  LoopVectorizeAttrStorage(BoolAttr disable, BoolAttr predicateEnable, BoolAttr scalableEnable, IntegerAttr width, LoopAnnotationAttr followupVectorized, LoopAnnotationAttr followupEpilogue, LoopAnnotationAttr followupAll) : disable(disable), predicateEnable(predicateEnable), scalableEnable(scalableEnable), width(width), followupVectorized(followupVectorized), followupEpilogue(followupEpilogue), followupAll(followupAll) {}

  KeyTy getAsKey() const {
    return KeyTy(disable, predicateEnable, scalableEnable, width, followupVectorized, followupEpilogue, followupAll);
  }

  bool operator==(const KeyTy &tblgenKey) const {
    return (disable == std::get<0>(tblgenKey)) && (predicateEnable == std::get<1>(tblgenKey)) && (scalableEnable == std::get<2>(tblgenKey)) && (width == std::get<3>(tblgenKey)) && (followupVectorized == std::get<4>(tblgenKey)) && (followupEpilogue == std::get<5>(tblgenKey)) && (followupAll == std::get<6>(tblgenKey));
  }

  static ::llvm::hash_code hashKey(const KeyTy &tblgenKey) {
    return ::llvm::hash_combine(std::get<0>(tblgenKey), std::get<1>(tblgenKey), std::get<2>(tblgenKey), std::get<3>(tblgenKey), std::get<4>(tblgenKey), std::get<5>(tblgenKey), std::get<6>(tblgenKey));
  }

  static LoopVectorizeAttrStorage *construct(::mlir::AttributeStorageAllocator &allocator, const KeyTy &tblgenKey) {
    auto disable = std::get<0>(tblgenKey);
    auto predicateEnable = std::get<1>(tblgenKey);
    auto scalableEnable = std::get<2>(tblgenKey);
    auto width = std::get<3>(tblgenKey);
    auto followupVectorized = std::get<4>(tblgenKey);
    auto followupEpilogue = std::get<5>(tblgenKey);
    auto followupAll = std::get<6>(tblgenKey);
    return new (allocator.allocate<LoopVectorizeAttrStorage>()) LoopVectorizeAttrStorage(disable, predicateEnable, scalableEnable, width, followupVectorized, followupEpilogue, followupAll);
  }

  BoolAttr disable;
  BoolAttr predicateEnable;
  BoolAttr scalableEnable;
  IntegerAttr width;
  LoopAnnotationAttr followupVectorized;
  LoopAnnotationAttr followupEpilogue;
  LoopAnnotationAttr followupAll;
};
} // namespace detail
LoopVectorizeAttr LoopVectorizeAttr::get(::mlir::MLIRContext *context, BoolAttr disable, BoolAttr predicateEnable, BoolAttr scalableEnable, IntegerAttr width, LoopAnnotationAttr followupVectorized, LoopAnnotationAttr followupEpilogue, LoopAnnotationAttr followupAll) {
  return Base::get(context, disable, predicateEnable, scalableEnable, width, followupVectorized, followupEpilogue, followupAll);
}

::mlir::Attribute LoopVectorizeAttr::parse(::mlir::AsmParser &odsParser, ::mlir::Type odsType) {
  ::mlir::Builder odsBuilder(odsParser.getContext());
  ::llvm::SMLoc odsLoc = odsParser.getCurrentLocation();
  (void) odsLoc;
  ::mlir::FailureOr<BoolAttr> _result_disable;
  ::mlir::FailureOr<BoolAttr> _result_predicateEnable;
  ::mlir::FailureOr<BoolAttr> _result_scalableEnable;
  ::mlir::FailureOr<IntegerAttr> _result_width;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupVectorized;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupEpilogue;
  ::mlir::FailureOr<LoopAnnotationAttr> _result_followupAll;
  // Parse literal '<'
  if (odsParser.parseLess()) return {};
  // Parse parameter struct
  bool _seen_disable = false;
  bool _seen_predicateEnable = false;
  bool _seen_scalableEnable = false;
  bool _seen_width = false;
  bool _seen_followupVectorized = false;
  bool _seen_followupEpilogue = false;
  bool _seen_followupAll = false;
  {
    const auto _loop_body = [&](::llvm::StringRef _paramKey) -> bool {
      // Parse literal '='
      if (odsParser.parseEqual()) return {};
      if (!_seen_disable && _paramKey == "disable") {
        _seen_disable = true;

        // Parse variable 'disable'
        _result_disable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_disable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'disable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_predicateEnable && _paramKey == "predicateEnable") {
        _seen_predicateEnable = true;

        // Parse variable 'predicateEnable'
        _result_predicateEnable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_predicateEnable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'predicateEnable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_scalableEnable && _paramKey == "scalableEnable") {
        _seen_scalableEnable = true;

        // Parse variable 'scalableEnable'
        _result_scalableEnable = ::mlir::FieldParser<BoolAttr>::parse(odsParser);
        if (::mlir::failed(_result_scalableEnable)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'scalableEnable' which is to be a `BoolAttr`");
          return {};
        }
      } else if (!_seen_width && _paramKey == "width") {
        _seen_width = true;

        // Parse variable 'width'
        _result_width = ::mlir::FieldParser<IntegerAttr>::parse(odsParser);
        if (::mlir::failed(_result_width)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'width' which is to be a `IntegerAttr`");
          return {};
        }
      } else if (!_seen_followupVectorized && _paramKey == "followupVectorized") {
        _seen_followupVectorized = true;

        // Parse variable 'followupVectorized'
        _result_followupVectorized = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupVectorized)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'followupVectorized' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupEpilogue && _paramKey == "followupEpilogue") {
        _seen_followupEpilogue = true;

        // Parse variable 'followupEpilogue'
        _result_followupEpilogue = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupEpilogue)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'followupEpilogue' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else if (!_seen_followupAll && _paramKey == "followupAll") {
        _seen_followupAll = true;

        // Parse variable 'followupAll'
        _result_followupAll = ::mlir::FieldParser<LoopAnnotationAttr>::parse(odsParser);
        if (::mlir::failed(_result_followupAll)) {
          odsParser.emitError(odsParser.getCurrentLocation(), "failed to parse LoopVectorizeAttr parameter 'followupAll' which is to be a `LoopAnnotationAttr`");
          return {};
        }
      } else {
        odsParser.emitError(odsParser.getCurrentLocation(), "duplicate or unknown struct parameter name: ") << _paramKey;
        return {};
      }
      return true;
    };
    ::llvm::StringRef _paramKey;
    if (!odsParser.parseOptionalKeyword(&_paramKey)) {
      if (!_loop_body(_paramKey)) return {};
      while (!odsParser.parseOptionalComma()) {
        ::llvm::StringRef _paramKey;
        if (odsParser.parseKeyword(&_paramKey)) {
          odsParser.emitError(odsParser.getCurrentLocation(),
                             "expected a parameter name in struct");
          return {};
        }
        if (!_loop_body(_paramKey)) return {};
      }
    }
  }
  // Parse literal '>'
  if (odsParser.parseGreater()) return {};
  return LoopVectorizeAttr::get(odsParser.getContext(),
      BoolAttr((_result_disable.value_or(BoolAttr()))),
      BoolAttr((_result_predicateEnable.value_or(BoolAttr()))),
      BoolAttr((_result_scalableEnable.value_or(BoolAttr()))),
      IntegerAttr((_result_width.value_or(IntegerAttr()))),
      LoopAnnotationAttr((_result_followupVectorized.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupEpilogue.value_or(LoopAnnotationAttr()))),
      LoopAnnotationAttr((_result_followupAll.value_or(LoopAnnotationAttr()))));
}

void LoopVectorizeAttr::print(::mlir::AsmPrinter &odsPrinter) const {
  ::mlir::Builder odsBuilder(getContext());
  odsPrinter << "<";
  {
    bool _firstPrinted = true;
    if (!(getDisable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "disable = ";
      if (!(getDisable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getDisable());
      }
    }
    if (!(getPredicateEnable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "predicateEnable = ";
      if (!(getPredicateEnable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getPredicateEnable());
      }
    }
    if (!(getScalableEnable() == BoolAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "scalableEnable = ";
      if (!(getScalableEnable() == BoolAttr())) {
        odsPrinter.printStrippedAttrOrType(getScalableEnable());
      }
    }
    if (!(getWidth() == IntegerAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "width = ";
      if (!(getWidth() == IntegerAttr())) {
        odsPrinter.printStrippedAttrOrType(getWidth());
      }
    }
    if (!(getFollowupVectorized() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupVectorized = ";
      if (!(getFollowupVectorized() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupVectorized());
      }
    }
    if (!(getFollowupEpilogue() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupEpilogue = ";
      if (!(getFollowupEpilogue() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupEpilogue());
      }
    }
    if (!(getFollowupAll() == LoopAnnotationAttr())) {
      if (!_firstPrinted) odsPrinter << ", ";
      _firstPrinted = false;
      odsPrinter << "followupAll = ";
      if (!(getFollowupAll() == LoopAnnotationAttr())) {
        odsPrinter.printStrippedAttrOrType(getFollowupAll());
      }
    }
  }
  odsPrinter << ">";
}

BoolAttr LoopVectorizeAttr::getDisable() const {
  return getImpl()->disable;
}

BoolAttr LoopVectorizeAttr::getPredicateEnable() const {
  return getImpl()->predicateEnable;
}

BoolAttr LoopVectorizeAttr::getScalableEnable() const {
  return getImpl()->scalableEnable;
}

IntegerAttr LoopVectorizeAttr::getWidth() const {
  return getImpl()->width;
}

LoopAnnotationAttr LoopVectorizeAttr::getFollowupVectorized() const {
  return getImpl()->followupVectorized;
}

LoopAnnotationAttr LoopVectorizeAttr::getFollowupEpilogue() const {
  return getImpl()->followupEpilogue;
}

LoopAnnotationAttr LoopVectorizeAttr::getFollowupAll() const {
  return getImpl()->followupAll;
}

} // namespace LLVM
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::LLVM::LoopVectorizeAttr)
namespace mlir {
namespace LLVM {

/// Parse an attribute registered to this dialect.
::mlir::Attribute LLVMDialect::parseAttribute(::mlir::DialectAsmParser &parser,
                                      ::mlir::Type type) const {
  ::llvm::SMLoc typeLoc = parser.getCurrentLocation();
  ::llvm::StringRef attrTag;
  {
    ::mlir::Attribute attr;
    auto parseResult = generatedAttributeParser(parser, &attrTag, type, attr);
    if (parseResult.has_value())
      return attr;
  }
  
  parser.emitError(typeLoc) << "unknown attribute `"
      << attrTag << "` in dialect `" << getNamespace() << "`";
  return {};
}
/// Print an attribute registered to this dialect.
void LLVMDialect::printAttribute(::mlir::Attribute attr,
                         ::mlir::DialectAsmPrinter &printer) const {
  if (::mlir::succeeded(generatedAttributePrinter(attr, printer)))
    return;
  
}
} // namespace LLVM
} // namespace mlir

#endif  // GET_ATTRDEF_CLASSES

