# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.experimental.dtensor namespace.
"""

import sys as _sys

from tensorflow.dtensor.python.accelerator_util import initialize_accelerator_system
from tensorflow.dtensor.python.accelerator_util import initialize_accelerator_system as initialize_multi_client
from tensorflow.dtensor.python.accelerator_util import initialize_accelerator_system as initialize_tpu_system
from tensorflow.dtensor.python.accelerator_util import shutdown_accelerator_system
from tensorflow.dtensor.python.accelerator_util import shutdown_accelerator_system as shutdown_tpu_system
from tensorflow.dtensor.python.api import call_with_layout
from tensorflow.dtensor.python.api import check_layout
from tensorflow.dtensor.python.api import copy_to_mesh
from tensorflow.dtensor.python.api import default_mesh
from tensorflow.dtensor.python.api import device_name
from tensorflow.dtensor.python.api import fetch_layout
from tensorflow.dtensor.python.api import is_dtensor
from tensorflow.dtensor.python.api import pack
from tensorflow.dtensor.python.api import relayout
from tensorflow.dtensor.python.api import run_on
from tensorflow.dtensor.python.api import unpack
from tensorflow.dtensor.python.config import client_id
from tensorflow.dtensor.python.config import full_job_name
from tensorflow.dtensor.python.config import heartbeat_enabled
from tensorflow.dtensor.python.config import job_name
from tensorflow.dtensor.python.config import jobs
from tensorflow.dtensor.python.config import local_devices
from tensorflow.dtensor.python.config import num_clients
from tensorflow.dtensor.python.config import num_global_devices
from tensorflow.dtensor.python.config import num_local_devices
from tensorflow.dtensor.python.config import preferred_device_type
from tensorflow.dtensor.python.d_checkpoint import DTensorCheckpoint
from tensorflow.dtensor.python.d_variable import DVariable
from tensorflow.dtensor.python.input_util import DTensorDataset
from tensorflow.dtensor.python.layout import Layout
from tensorflow.dtensor.python.layout import MATCH
from tensorflow.dtensor.python.layout import Mesh
from tensorflow.dtensor.python.layout import UNSHARDED
from tensorflow.dtensor.python.mesh_util import barrier
from tensorflow.dtensor.python.mesh_util import create_distributed_mesh
from tensorflow.dtensor.python.mesh_util import create_mesh
from tensorflow.dtensor.python.save_restore import enable_save_as_bf16
from tensorflow.dtensor.python.save_restore import name_based_restore
from tensorflow.dtensor.python.save_restore import name_based_save
from tensorflow.dtensor.python.save_restore import sharded_save
from tensorflow.dtensor.python.tpu_util import create_tpu_mesh