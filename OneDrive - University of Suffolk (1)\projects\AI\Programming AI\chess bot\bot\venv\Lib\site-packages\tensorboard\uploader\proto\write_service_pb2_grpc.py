# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from tensorboard.uploader.proto import write_service_pb2 as tensorboard_dot_uploader_dot_proto_dot_write__service__pb2


class TensorBoardWriterServiceStub(object):
    """Service for writing data to TensorBoard.dev.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateExperiment = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/CreateExperiment',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.CreateExperimentRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.CreateExperimentResponse.FromString,
                )
        self.UpdateExperiment = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/UpdateExperiment',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.UpdateExperimentRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.UpdateExperimentResponse.FromString,
                )
        self.DeleteExperiment = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/DeleteExperiment',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteExperimentRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteExperimentResponse.FromString,
                )
        self.PurgeData = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/PurgeData',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.PurgeDataRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.PurgeDataResponse.FromString,
                )
        self.WriteScalar = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/WriteScalar',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteScalarRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteScalarResponse.FromString,
                )
        self.WriteTensor = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/WriteTensor',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteTensorRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteTensorResponse.FromString,
                )
        self.GetOrCreateBlobSequence = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/GetOrCreateBlobSequence',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetOrCreateBlobSequenceRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetOrCreateBlobSequenceResponse.FromString,
                )
        self.GetBlobMetadata = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/GetBlobMetadata',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetBlobMetadataRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetBlobMetadataResponse.FromString,
                )
        self.WriteBlob = channel.stream_stream(
                '/tensorboard.service.TensorBoardWriterService/WriteBlob',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteBlobRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteBlobResponse.FromString,
                )
        self.DeleteOwnUser = channel.unary_unary(
                '/tensorboard.service.TensorBoardWriterService/DeleteOwnUser',
                request_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteOwnUserRequest.SerializeToString,
                response_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteOwnUserResponse.FromString,
                )


class TensorBoardWriterServiceServicer(object):
    """Service for writing data to TensorBoard.dev.
    """

    def CreateExperiment(self, request, context):
        """Request for a new location to write TensorBoard readable events.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateExperiment(self, request, context):
        """Request to mutate metadata associated with an experiment.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteExperiment(self, request, context):
        """Request that an experiment be deleted, along with all tags and scalars
        that it contains. This call may only be made by the original owner of the
        experiment.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeData(self, request, context):
        """Request that unreachable data be purged. Used only for testing;
        disabled in production.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def WriteScalar(self, request, context):
        """Request additional scalar data be stored in TensorBoard.dev.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def WriteTensor(self, request, context):
        """Request additional tensor data be stored in TensorBoard.dev.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOrCreateBlobSequence(self, request, context):
        """Request to obtain a specific BlobSequence entry, creating it if needed,
        to be subsequently populated with blobs.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBlobMetadata(self, request, context):
        """Request the current status of blob data being stored in TensorBoard.dev,
        to support resumable uploads.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def WriteBlob(self, request_iterator, context):
        """Request additional blob data be stored in TensorBoard.dev.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteOwnUser(self, request, context):
        """Request that the calling user and all their data be permanently deleted.
        Used for testing purposes.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TensorBoardWriterServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateExperiment': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateExperiment,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.CreateExperimentRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.CreateExperimentResponse.SerializeToString,
            ),
            'UpdateExperiment': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateExperiment,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.UpdateExperimentRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.UpdateExperimentResponse.SerializeToString,
            ),
            'DeleteExperiment': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteExperiment,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteExperimentRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteExperimentResponse.SerializeToString,
            ),
            'PurgeData': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeData,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.PurgeDataRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.PurgeDataResponse.SerializeToString,
            ),
            'WriteScalar': grpc.unary_unary_rpc_method_handler(
                    servicer.WriteScalar,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteScalarRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteScalarResponse.SerializeToString,
            ),
            'WriteTensor': grpc.unary_unary_rpc_method_handler(
                    servicer.WriteTensor,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteTensorRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteTensorResponse.SerializeToString,
            ),
            'GetOrCreateBlobSequence': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOrCreateBlobSequence,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetOrCreateBlobSequenceRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetOrCreateBlobSequenceResponse.SerializeToString,
            ),
            'GetBlobMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBlobMetadata,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetBlobMetadataRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetBlobMetadataResponse.SerializeToString,
            ),
            'WriteBlob': grpc.stream_stream_rpc_method_handler(
                    servicer.WriteBlob,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteBlobRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteBlobResponse.SerializeToString,
            ),
            'DeleteOwnUser': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteOwnUser,
                    request_deserializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteOwnUserRequest.FromString,
                    response_serializer=tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteOwnUserResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'tensorboard.service.TensorBoardWriterService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TensorBoardWriterService(object):
    """Service for writing data to TensorBoard.dev.
    """

    @staticmethod
    def CreateExperiment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/CreateExperiment',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.CreateExperimentRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.CreateExperimentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateExperiment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/UpdateExperiment',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.UpdateExperimentRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.UpdateExperimentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteExperiment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/DeleteExperiment',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteExperimentRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteExperimentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PurgeData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/PurgeData',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.PurgeDataRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.PurgeDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def WriteScalar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/WriteScalar',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteScalarRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteScalarResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def WriteTensor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/WriteTensor',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteTensorRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteTensorResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetOrCreateBlobSequence(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/GetOrCreateBlobSequence',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetOrCreateBlobSequenceRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetOrCreateBlobSequenceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetBlobMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/GetBlobMetadata',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetBlobMetadataRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.GetBlobMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def WriteBlob(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/tensorboard.service.TensorBoardWriterService/WriteBlob',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteBlobRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.WriteBlobResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteOwnUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tensorboard.service.TensorBoardWriterService/DeleteOwnUser',
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteOwnUserRequest.SerializeToString,
            tensorboard_dot_uploader_dot_proto_dot_write__service__pb2.DeleteOwnUserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
