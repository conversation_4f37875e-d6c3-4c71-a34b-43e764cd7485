# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.estimator.tpu namespace.
"""

import sys as _sys

from tensorflow_estimator._api.v1.estimator.tpu import experimental
from tensorflow_estimator.python.estimator.tpu.tpu_config import InputPipelineConfig
from tensorflow_estimator.python.estimator.tpu.tpu_config import RunConfig
from tensorflow_estimator.python.estimator.tpu.tpu_config import TPUConfig
from tensorflow_estimator.python.estimator.tpu.tpu_estimator import TPUEstimator
from tensorflow_estimator.python.estimator.tpu.tpu_estimator import TPUEstimatorSpec
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "estimator.tpu", public_apis=None, deprecation=True,
      has_lite=False)
