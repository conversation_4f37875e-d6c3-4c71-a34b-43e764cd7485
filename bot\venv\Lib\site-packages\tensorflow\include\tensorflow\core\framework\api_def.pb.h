// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/api_def.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto;
namespace tensorflow {
class ApiDef;
struct ApiDefDefaultTypeInternal;
extern ApiDefDefaultTypeInternal _ApiDef_default_instance_;
class ApiDef_Arg;
struct ApiDef_ArgDefaultTypeInternal;
extern ApiDef_ArgDefaultTypeInternal _ApiDef_Arg_default_instance_;
class ApiDef_Attr;
struct ApiDef_AttrDefaultTypeInternal;
extern ApiDef_AttrDefaultTypeInternal _ApiDef_Attr_default_instance_;
class ApiDef_Endpoint;
struct ApiDef_EndpointDefaultTypeInternal;
extern ApiDef_EndpointDefaultTypeInternal _ApiDef_Endpoint_default_instance_;
class ApiDefs;
struct ApiDefsDefaultTypeInternal;
extern ApiDefsDefaultTypeInternal _ApiDefs_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ApiDef* Arena::CreateMaybeMessage<::tensorflow::ApiDef>(Arena*);
template<> ::tensorflow::ApiDef_Arg* Arena::CreateMaybeMessage<::tensorflow::ApiDef_Arg>(Arena*);
template<> ::tensorflow::ApiDef_Attr* Arena::CreateMaybeMessage<::tensorflow::ApiDef_Attr>(Arena*);
template<> ::tensorflow::ApiDef_Endpoint* Arena::CreateMaybeMessage<::tensorflow::ApiDef_Endpoint>(Arena*);
template<> ::tensorflow::ApiDefs* Arena::CreateMaybeMessage<::tensorflow::ApiDefs>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum ApiDef_Visibility : int {
  ApiDef_Visibility_DEFAULT_VISIBILITY = 0,
  ApiDef_Visibility_VISIBLE = 1,
  ApiDef_Visibility_SKIP = 2,
  ApiDef_Visibility_HIDDEN = 3,
  ApiDef_Visibility_ApiDef_Visibility_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ApiDef_Visibility_ApiDef_Visibility_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ApiDef_Visibility_IsValid(int value);
constexpr ApiDef_Visibility ApiDef_Visibility_Visibility_MIN = ApiDef_Visibility_DEFAULT_VISIBILITY;
constexpr ApiDef_Visibility ApiDef_Visibility_Visibility_MAX = ApiDef_Visibility_HIDDEN;
constexpr int ApiDef_Visibility_Visibility_ARRAYSIZE = ApiDef_Visibility_Visibility_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ApiDef_Visibility_descriptor();
template<typename T>
inline const std::string& ApiDef_Visibility_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ApiDef_Visibility>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ApiDef_Visibility_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ApiDef_Visibility_descriptor(), enum_t_value);
}
inline bool ApiDef_Visibility_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ApiDef_Visibility* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ApiDef_Visibility>(
    ApiDef_Visibility_descriptor(), name, value);
}
// ===================================================================

class ApiDef_Endpoint final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef.Endpoint) */ {
 public:
  inline ApiDef_Endpoint() : ApiDef_Endpoint(nullptr) {}
  ~ApiDef_Endpoint() override;
  explicit PROTOBUF_CONSTEXPR ApiDef_Endpoint(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ApiDef_Endpoint(const ApiDef_Endpoint& from);
  ApiDef_Endpoint(ApiDef_Endpoint&& from) noexcept
    : ApiDef_Endpoint() {
    *this = ::std::move(from);
  }

  inline ApiDef_Endpoint& operator=(const ApiDef_Endpoint& from) {
    CopyFrom(from);
    return *this;
  }
  inline ApiDef_Endpoint& operator=(ApiDef_Endpoint&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ApiDef_Endpoint& default_instance() {
    return *internal_default_instance();
  }
  static inline const ApiDef_Endpoint* internal_default_instance() {
    return reinterpret_cast<const ApiDef_Endpoint*>(
               &_ApiDef_Endpoint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ApiDef_Endpoint& a, ApiDef_Endpoint& b) {
    a.Swap(&b);
  }
  inline void Swap(ApiDef_Endpoint* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ApiDef_Endpoint* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ApiDef_Endpoint* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ApiDef_Endpoint>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ApiDef_Endpoint& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ApiDef_Endpoint& from) {
    ApiDef_Endpoint::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef_Endpoint* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ApiDef.Endpoint";
  }
  protected:
  explicit ApiDef_Endpoint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDeprecatedFieldNumber = 3,
    kDeprecationVersionFieldNumber = 4,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bool deprecated = 3;
  void clear_deprecated();
  bool deprecated() const;
  void set_deprecated(bool value);
  private:
  bool _internal_deprecated() const;
  void _internal_set_deprecated(bool value);
  public:

  // int32 deprecation_version = 4;
  void clear_deprecation_version();
  int32_t deprecation_version() const;
  void set_deprecation_version(int32_t value);
  private:
  int32_t _internal_deprecation_version() const;
  void _internal_set_deprecation_version(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef.Endpoint)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    bool deprecated_;
    int32_t deprecation_version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto;
};
// -------------------------------------------------------------------

class ApiDef_Arg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef.Arg) */ {
 public:
  inline ApiDef_Arg() : ApiDef_Arg(nullptr) {}
  ~ApiDef_Arg() override;
  explicit PROTOBUF_CONSTEXPR ApiDef_Arg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ApiDef_Arg(const ApiDef_Arg& from);
  ApiDef_Arg(ApiDef_Arg&& from) noexcept
    : ApiDef_Arg() {
    *this = ::std::move(from);
  }

  inline ApiDef_Arg& operator=(const ApiDef_Arg& from) {
    CopyFrom(from);
    return *this;
  }
  inline ApiDef_Arg& operator=(ApiDef_Arg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ApiDef_Arg& default_instance() {
    return *internal_default_instance();
  }
  static inline const ApiDef_Arg* internal_default_instance() {
    return reinterpret_cast<const ApiDef_Arg*>(
               &_ApiDef_Arg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ApiDef_Arg& a, ApiDef_Arg& b) {
    a.Swap(&b);
  }
  inline void Swap(ApiDef_Arg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ApiDef_Arg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ApiDef_Arg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ApiDef_Arg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ApiDef_Arg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ApiDef_Arg& from) {
    ApiDef_Arg::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef_Arg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ApiDef.Arg";
  }
  protected:
  explicit ApiDef_Arg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kRenameToFieldNumber = 2,
    kDescriptionFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string rename_to = 2;
  void clear_rename_to();
  const std::string& rename_to() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rename_to(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rename_to();
  PROTOBUF_NODISCARD std::string* release_rename_to();
  void set_allocated_rename_to(std::string* rename_to);
  private:
  const std::string& _internal_rename_to() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rename_to(const std::string& value);
  std::string* _internal_mutable_rename_to();
  public:

  // string description = 3;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef.Arg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rename_to_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto;
};
// -------------------------------------------------------------------

class ApiDef_Attr final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef.Attr) */ {
 public:
  inline ApiDef_Attr() : ApiDef_Attr(nullptr) {}
  ~ApiDef_Attr() override;
  explicit PROTOBUF_CONSTEXPR ApiDef_Attr(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ApiDef_Attr(const ApiDef_Attr& from);
  ApiDef_Attr(ApiDef_Attr&& from) noexcept
    : ApiDef_Attr() {
    *this = ::std::move(from);
  }

  inline ApiDef_Attr& operator=(const ApiDef_Attr& from) {
    CopyFrom(from);
    return *this;
  }
  inline ApiDef_Attr& operator=(ApiDef_Attr&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ApiDef_Attr& default_instance() {
    return *internal_default_instance();
  }
  static inline const ApiDef_Attr* internal_default_instance() {
    return reinterpret_cast<const ApiDef_Attr*>(
               &_ApiDef_Attr_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ApiDef_Attr& a, ApiDef_Attr& b) {
    a.Swap(&b);
  }
  inline void Swap(ApiDef_Attr* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ApiDef_Attr* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ApiDef_Attr* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ApiDef_Attr>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ApiDef_Attr& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ApiDef_Attr& from) {
    ApiDef_Attr::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef_Attr* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ApiDef.Attr";
  }
  protected:
  explicit ApiDef_Attr(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kRenameToFieldNumber = 2,
    kDescriptionFieldNumber = 4,
    kDefaultValueFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string rename_to = 2;
  void clear_rename_to();
  const std::string& rename_to() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rename_to(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rename_to();
  PROTOBUF_NODISCARD std::string* release_rename_to();
  void set_allocated_rename_to(std::string* rename_to);
  private:
  const std::string& _internal_rename_to() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rename_to(const std::string& value);
  std::string* _internal_mutable_rename_to();
  public:

  // string description = 4;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // .tensorflow.AttrValue default_value = 3;
  bool has_default_value() const;
  private:
  bool _internal_has_default_value() const;
  public:
  void clear_default_value();
  const ::tensorflow::AttrValue& default_value() const;
  PROTOBUF_NODISCARD ::tensorflow::AttrValue* release_default_value();
  ::tensorflow::AttrValue* mutable_default_value();
  void set_allocated_default_value(::tensorflow::AttrValue* default_value);
  private:
  const ::tensorflow::AttrValue& _internal_default_value() const;
  ::tensorflow::AttrValue* _internal_mutable_default_value();
  public:
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::AttrValue* default_value);
  ::tensorflow::AttrValue* unsafe_arena_release_default_value();

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef.Attr)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rename_to_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    ::tensorflow::AttrValue* default_value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto;
};
// -------------------------------------------------------------------

class ApiDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef) */ {
 public:
  inline ApiDef() : ApiDef(nullptr) {}
  ~ApiDef() override;
  explicit PROTOBUF_CONSTEXPR ApiDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ApiDef(const ApiDef& from);
  ApiDef(ApiDef&& from) noexcept
    : ApiDef() {
    *this = ::std::move(from);
  }

  inline ApiDef& operator=(const ApiDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline ApiDef& operator=(ApiDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ApiDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const ApiDef* internal_default_instance() {
    return reinterpret_cast<const ApiDef*>(
               &_ApiDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ApiDef& a, ApiDef& b) {
    a.Swap(&b);
  }
  inline void Swap(ApiDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ApiDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ApiDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ApiDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ApiDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ApiDef& from) {
    ApiDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ApiDef";
  }
  protected:
  explicit ApiDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ApiDef_Endpoint Endpoint;
  typedef ApiDef_Arg Arg;
  typedef ApiDef_Attr Attr;

  typedef ApiDef_Visibility Visibility;
  static constexpr Visibility DEFAULT_VISIBILITY =
    ApiDef_Visibility_DEFAULT_VISIBILITY;
  static constexpr Visibility VISIBLE =
    ApiDef_Visibility_VISIBLE;
  static constexpr Visibility SKIP =
    ApiDef_Visibility_SKIP;
  static constexpr Visibility HIDDEN =
    ApiDef_Visibility_HIDDEN;
  static inline bool Visibility_IsValid(int value) {
    return ApiDef_Visibility_IsValid(value);
  }
  static constexpr Visibility Visibility_MIN =
    ApiDef_Visibility_Visibility_MIN;
  static constexpr Visibility Visibility_MAX =
    ApiDef_Visibility_Visibility_MAX;
  static constexpr int Visibility_ARRAYSIZE =
    ApiDef_Visibility_Visibility_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Visibility_descriptor() {
    return ApiDef_Visibility_descriptor();
  }
  template<typename T>
  static inline const std::string& Visibility_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Visibility>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Visibility_Name.");
    return ApiDef_Visibility_Name(enum_t_value);
  }
  static inline bool Visibility_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Visibility* value) {
    return ApiDef_Visibility_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kEndpointFieldNumber = 3,
    kInArgFieldNumber = 4,
    kOutArgFieldNumber = 5,
    kAttrFieldNumber = 6,
    kArgOrderFieldNumber = 11,
    kGraphOpNameFieldNumber = 1,
    kSummaryFieldNumber = 7,
    kDescriptionFieldNumber = 8,
    kDescriptionPrefixFieldNumber = 9,
    kDescriptionSuffixFieldNumber = 10,
    kDeprecationMessageFieldNumber = 12,
    kVisibilityFieldNumber = 2,
    kDeprecationVersionFieldNumber = 13,
  };
  // repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
  int endpoint_size() const;
  private:
  int _internal_endpoint_size() const;
  public:
  void clear_endpoint();
  ::tensorflow::ApiDef_Endpoint* mutable_endpoint(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >*
      mutable_endpoint();
  private:
  const ::tensorflow::ApiDef_Endpoint& _internal_endpoint(int index) const;
  ::tensorflow::ApiDef_Endpoint* _internal_add_endpoint();
  public:
  const ::tensorflow::ApiDef_Endpoint& endpoint(int index) const;
  ::tensorflow::ApiDef_Endpoint* add_endpoint();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >&
      endpoint() const;

  // repeated .tensorflow.ApiDef.Arg in_arg = 4;
  int in_arg_size() const;
  private:
  int _internal_in_arg_size() const;
  public:
  void clear_in_arg();
  ::tensorflow::ApiDef_Arg* mutable_in_arg(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
      mutable_in_arg();
  private:
  const ::tensorflow::ApiDef_Arg& _internal_in_arg(int index) const;
  ::tensorflow::ApiDef_Arg* _internal_add_in_arg();
  public:
  const ::tensorflow::ApiDef_Arg& in_arg(int index) const;
  ::tensorflow::ApiDef_Arg* add_in_arg();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
      in_arg() const;

  // repeated .tensorflow.ApiDef.Arg out_arg = 5;
  int out_arg_size() const;
  private:
  int _internal_out_arg_size() const;
  public:
  void clear_out_arg();
  ::tensorflow::ApiDef_Arg* mutable_out_arg(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
      mutable_out_arg();
  private:
  const ::tensorflow::ApiDef_Arg& _internal_out_arg(int index) const;
  ::tensorflow::ApiDef_Arg* _internal_add_out_arg();
  public:
  const ::tensorflow::ApiDef_Arg& out_arg(int index) const;
  ::tensorflow::ApiDef_Arg* add_out_arg();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
      out_arg() const;

  // repeated .tensorflow.ApiDef.Attr attr = 6;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  ::tensorflow::ApiDef_Attr* mutable_attr(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Attr >*
      mutable_attr();
  private:
  const ::tensorflow::ApiDef_Attr& _internal_attr(int index) const;
  ::tensorflow::ApiDef_Attr* _internal_add_attr();
  public:
  const ::tensorflow::ApiDef_Attr& attr(int index) const;
  ::tensorflow::ApiDef_Attr* add_attr();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Attr >&
      attr() const;

  // repeated string arg_order = 11;
  int arg_order_size() const;
  private:
  int _internal_arg_order_size() const;
  public:
  void clear_arg_order();
  const std::string& arg_order(int index) const;
  std::string* mutable_arg_order(int index);
  void set_arg_order(int index, const std::string& value);
  void set_arg_order(int index, std::string&& value);
  void set_arg_order(int index, const char* value);
  void set_arg_order(int index, const char* value, size_t size);
  std::string* add_arg_order();
  void add_arg_order(const std::string& value);
  void add_arg_order(std::string&& value);
  void add_arg_order(const char* value);
  void add_arg_order(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& arg_order() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_arg_order();
  private:
  const std::string& _internal_arg_order(int index) const;
  std::string* _internal_add_arg_order();
  public:

  // string graph_op_name = 1;
  void clear_graph_op_name();
  const std::string& graph_op_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_op_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_op_name();
  PROTOBUF_NODISCARD std::string* release_graph_op_name();
  void set_allocated_graph_op_name(std::string* graph_op_name);
  private:
  const std::string& _internal_graph_op_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_op_name(const std::string& value);
  std::string* _internal_mutable_graph_op_name();
  public:

  // string summary = 7;
  void clear_summary();
  const std::string& summary() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_summary(ArgT0&& arg0, ArgT... args);
  std::string* mutable_summary();
  PROTOBUF_NODISCARD std::string* release_summary();
  void set_allocated_summary(std::string* summary);
  private:
  const std::string& _internal_summary() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_summary(const std::string& value);
  std::string* _internal_mutable_summary();
  public:

  // string description = 8;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string description_prefix = 9;
  void clear_description_prefix();
  const std::string& description_prefix() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description_prefix(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description_prefix();
  PROTOBUF_NODISCARD std::string* release_description_prefix();
  void set_allocated_description_prefix(std::string* description_prefix);
  private:
  const std::string& _internal_description_prefix() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description_prefix(const std::string& value);
  std::string* _internal_mutable_description_prefix();
  public:

  // string description_suffix = 10;
  void clear_description_suffix();
  const std::string& description_suffix() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description_suffix(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description_suffix();
  PROTOBUF_NODISCARD std::string* release_description_suffix();
  void set_allocated_description_suffix(std::string* description_suffix);
  private:
  const std::string& _internal_description_suffix() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description_suffix(const std::string& value);
  std::string* _internal_mutable_description_suffix();
  public:

  // string deprecation_message = 12;
  void clear_deprecation_message();
  const std::string& deprecation_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_deprecation_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_deprecation_message();
  PROTOBUF_NODISCARD std::string* release_deprecation_message();
  void set_allocated_deprecation_message(std::string* deprecation_message);
  private:
  const std::string& _internal_deprecation_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_deprecation_message(const std::string& value);
  std::string* _internal_mutable_deprecation_message();
  public:

  // .tensorflow.ApiDef.Visibility visibility = 2;
  void clear_visibility();
  ::tensorflow::ApiDef_Visibility visibility() const;
  void set_visibility(::tensorflow::ApiDef_Visibility value);
  private:
  ::tensorflow::ApiDef_Visibility _internal_visibility() const;
  void _internal_set_visibility(::tensorflow::ApiDef_Visibility value);
  public:

  // int32 deprecation_version = 13;
  void clear_deprecation_version();
  int32_t deprecation_version() const;
  void set_deprecation_version(int32_t value);
  private:
  int32_t _internal_deprecation_version() const;
  void _internal_set_deprecation_version(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint > endpoint_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg > in_arg_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg > out_arg_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Attr > attr_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> arg_order_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_op_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr summary_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_prefix_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_suffix_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr deprecation_message_;
    int visibility_;
    int32_t deprecation_version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto;
};
// -------------------------------------------------------------------

class ApiDefs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDefs) */ {
 public:
  inline ApiDefs() : ApiDefs(nullptr) {}
  ~ApiDefs() override;
  explicit PROTOBUF_CONSTEXPR ApiDefs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ApiDefs(const ApiDefs& from);
  ApiDefs(ApiDefs&& from) noexcept
    : ApiDefs() {
    *this = ::std::move(from);
  }

  inline ApiDefs& operator=(const ApiDefs& from) {
    CopyFrom(from);
    return *this;
  }
  inline ApiDefs& operator=(ApiDefs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ApiDefs& default_instance() {
    return *internal_default_instance();
  }
  static inline const ApiDefs* internal_default_instance() {
    return reinterpret_cast<const ApiDefs*>(
               &_ApiDefs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ApiDefs& a, ApiDefs& b) {
    a.Swap(&b);
  }
  inline void Swap(ApiDefs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ApiDefs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ApiDefs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ApiDefs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ApiDefs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ApiDefs& from) {
    ApiDefs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDefs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ApiDefs";
  }
  protected:
  explicit ApiDefs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpFieldNumber = 1,
  };
  // repeated .tensorflow.ApiDef op = 1;
  int op_size() const;
  private:
  int _internal_op_size() const;
  public:
  void clear_op();
  ::tensorflow::ApiDef* mutable_op(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef >*
      mutable_op();
  private:
  const ::tensorflow::ApiDef& _internal_op(int index) const;
  ::tensorflow::ApiDef* _internal_add_op();
  public:
  const ::tensorflow::ApiDef& op(int index) const;
  ::tensorflow::ApiDef* add_op();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef >&
      op() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDefs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef > op_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ApiDef_Endpoint

// string name = 1;
inline void ApiDef_Endpoint::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ApiDef_Endpoint::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Endpoint.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Endpoint::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Endpoint.name)
}
inline std::string* ApiDef_Endpoint::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Endpoint.name)
  return _s;
}
inline const std::string& ApiDef_Endpoint::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ApiDef_Endpoint::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Endpoint::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Endpoint::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Endpoint.name)
  return _impl_.name_.Release();
}
inline void ApiDef_Endpoint::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Endpoint.name)
}

// bool deprecated = 3;
inline void ApiDef_Endpoint::clear_deprecated() {
  _impl_.deprecated_ = false;
}
inline bool ApiDef_Endpoint::_internal_deprecated() const {
  return _impl_.deprecated_;
}
inline bool ApiDef_Endpoint::deprecated() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Endpoint.deprecated)
  return _internal_deprecated();
}
inline void ApiDef_Endpoint::_internal_set_deprecated(bool value) {
  
  _impl_.deprecated_ = value;
}
inline void ApiDef_Endpoint::set_deprecated(bool value) {
  _internal_set_deprecated(value);
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Endpoint.deprecated)
}

// int32 deprecation_version = 4;
inline void ApiDef_Endpoint::clear_deprecation_version() {
  _impl_.deprecation_version_ = 0;
}
inline int32_t ApiDef_Endpoint::_internal_deprecation_version() const {
  return _impl_.deprecation_version_;
}
inline int32_t ApiDef_Endpoint::deprecation_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Endpoint.deprecation_version)
  return _internal_deprecation_version();
}
inline void ApiDef_Endpoint::_internal_set_deprecation_version(int32_t value) {
  
  _impl_.deprecation_version_ = value;
}
inline void ApiDef_Endpoint::set_deprecation_version(int32_t value) {
  _internal_set_deprecation_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Endpoint.deprecation_version)
}

// -------------------------------------------------------------------

// ApiDef_Arg

// string name = 1;
inline void ApiDef_Arg::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ApiDef_Arg::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Arg.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Arg::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Arg.name)
}
inline std::string* ApiDef_Arg::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Arg.name)
  return _s;
}
inline const std::string& ApiDef_Arg::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ApiDef_Arg::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Arg::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Arg::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Arg.name)
  return _impl_.name_.Release();
}
inline void ApiDef_Arg::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Arg.name)
}

// string rename_to = 2;
inline void ApiDef_Arg::clear_rename_to() {
  _impl_.rename_to_.ClearToEmpty();
}
inline const std::string& ApiDef_Arg::rename_to() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Arg.rename_to)
  return _internal_rename_to();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Arg::set_rename_to(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rename_to_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Arg.rename_to)
}
inline std::string* ApiDef_Arg::mutable_rename_to() {
  std::string* _s = _internal_mutable_rename_to();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Arg.rename_to)
  return _s;
}
inline const std::string& ApiDef_Arg::_internal_rename_to() const {
  return _impl_.rename_to_.Get();
}
inline void ApiDef_Arg::_internal_set_rename_to(const std::string& value) {
  
  _impl_.rename_to_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Arg::_internal_mutable_rename_to() {
  
  return _impl_.rename_to_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Arg::release_rename_to() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Arg.rename_to)
  return _impl_.rename_to_.Release();
}
inline void ApiDef_Arg::set_allocated_rename_to(std::string* rename_to) {
  if (rename_to != nullptr) {
    
  } else {
    
  }
  _impl_.rename_to_.SetAllocated(rename_to, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rename_to_.IsDefault()) {
    _impl_.rename_to_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Arg.rename_to)
}

// string description = 3;
inline void ApiDef_Arg::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& ApiDef_Arg::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Arg.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Arg::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Arg.description)
}
inline std::string* ApiDef_Arg::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Arg.description)
  return _s;
}
inline const std::string& ApiDef_Arg::_internal_description() const {
  return _impl_.description_.Get();
}
inline void ApiDef_Arg::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Arg::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Arg::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Arg.description)
  return _impl_.description_.Release();
}
inline void ApiDef_Arg::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Arg.description)
}

// -------------------------------------------------------------------

// ApiDef_Attr

// string name = 1;
inline void ApiDef_Attr::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ApiDef_Attr::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Attr::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Attr.name)
}
inline std::string* ApiDef_Attr::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.name)
  return _s;
}
inline const std::string& ApiDef_Attr::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ApiDef_Attr::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Attr::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Attr::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.name)
  return _impl_.name_.Release();
}
inline void ApiDef_Attr::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.name)
}

// string rename_to = 2;
inline void ApiDef_Attr::clear_rename_to() {
  _impl_.rename_to_.ClearToEmpty();
}
inline const std::string& ApiDef_Attr::rename_to() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.rename_to)
  return _internal_rename_to();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Attr::set_rename_to(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rename_to_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Attr.rename_to)
}
inline std::string* ApiDef_Attr::mutable_rename_to() {
  std::string* _s = _internal_mutable_rename_to();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.rename_to)
  return _s;
}
inline const std::string& ApiDef_Attr::_internal_rename_to() const {
  return _impl_.rename_to_.Get();
}
inline void ApiDef_Attr::_internal_set_rename_to(const std::string& value) {
  
  _impl_.rename_to_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Attr::_internal_mutable_rename_to() {
  
  return _impl_.rename_to_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Attr::release_rename_to() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.rename_to)
  return _impl_.rename_to_.Release();
}
inline void ApiDef_Attr::set_allocated_rename_to(std::string* rename_to) {
  if (rename_to != nullptr) {
    
  } else {
    
  }
  _impl_.rename_to_.SetAllocated(rename_to, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rename_to_.IsDefault()) {
    _impl_.rename_to_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.rename_to)
}

// .tensorflow.AttrValue default_value = 3;
inline bool ApiDef_Attr::_internal_has_default_value() const {
  return this != internal_default_instance() && _impl_.default_value_ != nullptr;
}
inline bool ApiDef_Attr::has_default_value() const {
  return _internal_has_default_value();
}
inline const ::tensorflow::AttrValue& ApiDef_Attr::_internal_default_value() const {
  const ::tensorflow::AttrValue* p = _impl_.default_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::AttrValue&>(
      ::tensorflow::_AttrValue_default_instance_);
}
inline const ::tensorflow::AttrValue& ApiDef_Attr::default_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.default_value)
  return _internal_default_value();
}
inline void ApiDef_Attr::unsafe_arena_set_allocated_default_value(
    ::tensorflow::AttrValue* default_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.default_value_);
  }
  _impl_.default_value_ = default_value;
  if (default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Attr.default_value)
}
inline ::tensorflow::AttrValue* ApiDef_Attr::release_default_value() {
  
  ::tensorflow::AttrValue* temp = _impl_.default_value_;
  _impl_.default_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::AttrValue* ApiDef_Attr::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.default_value)
  
  ::tensorflow::AttrValue* temp = _impl_.default_value_;
  _impl_.default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* ApiDef_Attr::_internal_mutable_default_value() {
  
  if (_impl_.default_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaForAllocation());
    _impl_.default_value_ = p;
  }
  return _impl_.default_value_;
}
inline ::tensorflow::AttrValue* ApiDef_Attr::mutable_default_value() {
  ::tensorflow::AttrValue* _msg = _internal_mutable_default_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.default_value)
  return _msg;
}
inline void ApiDef_Attr::set_allocated_default_value(::tensorflow::AttrValue* default_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.default_value_);
  }
  if (default_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value));
    if (message_arena != submessage_arena) {
      default_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.default_value)
}

// string description = 4;
inline void ApiDef_Attr::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& ApiDef_Attr::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef_Attr::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Attr.description)
}
inline std::string* ApiDef_Attr::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.description)
  return _s;
}
inline const std::string& ApiDef_Attr::_internal_description() const {
  return _impl_.description_.Get();
}
inline void ApiDef_Attr::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef_Attr::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef_Attr::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.description)
  return _impl_.description_.Release();
}
inline void ApiDef_Attr::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.description)
}

// -------------------------------------------------------------------

// ApiDef

// string graph_op_name = 1;
inline void ApiDef::clear_graph_op_name() {
  _impl_.graph_op_name_.ClearToEmpty();
}
inline const std::string& ApiDef::graph_op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.graph_op_name)
  return _internal_graph_op_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef::set_graph_op_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_op_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.graph_op_name)
}
inline std::string* ApiDef::mutable_graph_op_name() {
  std::string* _s = _internal_mutable_graph_op_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.graph_op_name)
  return _s;
}
inline const std::string& ApiDef::_internal_graph_op_name() const {
  return _impl_.graph_op_name_.Get();
}
inline void ApiDef::_internal_set_graph_op_name(const std::string& value) {
  
  _impl_.graph_op_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef::_internal_mutable_graph_op_name() {
  
  return _impl_.graph_op_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef::release_graph_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.graph_op_name)
  return _impl_.graph_op_name_.Release();
}
inline void ApiDef::set_allocated_graph_op_name(std::string* graph_op_name) {
  if (graph_op_name != nullptr) {
    
  } else {
    
  }
  _impl_.graph_op_name_.SetAllocated(graph_op_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_op_name_.IsDefault()) {
    _impl_.graph_op_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.graph_op_name)
}

// string deprecation_message = 12;
inline void ApiDef::clear_deprecation_message() {
  _impl_.deprecation_message_.ClearToEmpty();
}
inline const std::string& ApiDef::deprecation_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.deprecation_message)
  return _internal_deprecation_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef::set_deprecation_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.deprecation_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.deprecation_message)
}
inline std::string* ApiDef::mutable_deprecation_message() {
  std::string* _s = _internal_mutable_deprecation_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.deprecation_message)
  return _s;
}
inline const std::string& ApiDef::_internal_deprecation_message() const {
  return _impl_.deprecation_message_.Get();
}
inline void ApiDef::_internal_set_deprecation_message(const std::string& value) {
  
  _impl_.deprecation_message_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef::_internal_mutable_deprecation_message() {
  
  return _impl_.deprecation_message_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef::release_deprecation_message() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.deprecation_message)
  return _impl_.deprecation_message_.Release();
}
inline void ApiDef::set_allocated_deprecation_message(std::string* deprecation_message) {
  if (deprecation_message != nullptr) {
    
  } else {
    
  }
  _impl_.deprecation_message_.SetAllocated(deprecation_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.deprecation_message_.IsDefault()) {
    _impl_.deprecation_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.deprecation_message)
}

// int32 deprecation_version = 13;
inline void ApiDef::clear_deprecation_version() {
  _impl_.deprecation_version_ = 0;
}
inline int32_t ApiDef::_internal_deprecation_version() const {
  return _impl_.deprecation_version_;
}
inline int32_t ApiDef::deprecation_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.deprecation_version)
  return _internal_deprecation_version();
}
inline void ApiDef::_internal_set_deprecation_version(int32_t value) {
  
  _impl_.deprecation_version_ = value;
}
inline void ApiDef::set_deprecation_version(int32_t value) {
  _internal_set_deprecation_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.deprecation_version)
}

// .tensorflow.ApiDef.Visibility visibility = 2;
inline void ApiDef::clear_visibility() {
  _impl_.visibility_ = 0;
}
inline ::tensorflow::ApiDef_Visibility ApiDef::_internal_visibility() const {
  return static_cast< ::tensorflow::ApiDef_Visibility >(_impl_.visibility_);
}
inline ::tensorflow::ApiDef_Visibility ApiDef::visibility() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.visibility)
  return _internal_visibility();
}
inline void ApiDef::_internal_set_visibility(::tensorflow::ApiDef_Visibility value) {
  
  _impl_.visibility_ = value;
}
inline void ApiDef::set_visibility(::tensorflow::ApiDef_Visibility value) {
  _internal_set_visibility(value);
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.visibility)
}

// repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
inline int ApiDef::_internal_endpoint_size() const {
  return _impl_.endpoint_.size();
}
inline int ApiDef::endpoint_size() const {
  return _internal_endpoint_size();
}
inline void ApiDef::clear_endpoint() {
  _impl_.endpoint_.Clear();
}
inline ::tensorflow::ApiDef_Endpoint* ApiDef::mutable_endpoint(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.endpoint)
  return _impl_.endpoint_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >*
ApiDef::mutable_endpoint() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.endpoint)
  return &_impl_.endpoint_;
}
inline const ::tensorflow::ApiDef_Endpoint& ApiDef::_internal_endpoint(int index) const {
  return _impl_.endpoint_.Get(index);
}
inline const ::tensorflow::ApiDef_Endpoint& ApiDef::endpoint(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.endpoint)
  return _internal_endpoint(index);
}
inline ::tensorflow::ApiDef_Endpoint* ApiDef::_internal_add_endpoint() {
  return _impl_.endpoint_.Add();
}
inline ::tensorflow::ApiDef_Endpoint* ApiDef::add_endpoint() {
  ::tensorflow::ApiDef_Endpoint* _add = _internal_add_endpoint();
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.endpoint)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >&
ApiDef::endpoint() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.endpoint)
  return _impl_.endpoint_;
}

// repeated .tensorflow.ApiDef.Arg in_arg = 4;
inline int ApiDef::_internal_in_arg_size() const {
  return _impl_.in_arg_.size();
}
inline int ApiDef::in_arg_size() const {
  return _internal_in_arg_size();
}
inline void ApiDef::clear_in_arg() {
  _impl_.in_arg_.Clear();
}
inline ::tensorflow::ApiDef_Arg* ApiDef::mutable_in_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.in_arg)
  return _impl_.in_arg_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
ApiDef::mutable_in_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.in_arg)
  return &_impl_.in_arg_;
}
inline const ::tensorflow::ApiDef_Arg& ApiDef::_internal_in_arg(int index) const {
  return _impl_.in_arg_.Get(index);
}
inline const ::tensorflow::ApiDef_Arg& ApiDef::in_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.in_arg)
  return _internal_in_arg(index);
}
inline ::tensorflow::ApiDef_Arg* ApiDef::_internal_add_in_arg() {
  return _impl_.in_arg_.Add();
}
inline ::tensorflow::ApiDef_Arg* ApiDef::add_in_arg() {
  ::tensorflow::ApiDef_Arg* _add = _internal_add_in_arg();
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.in_arg)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
ApiDef::in_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.in_arg)
  return _impl_.in_arg_;
}

// repeated .tensorflow.ApiDef.Arg out_arg = 5;
inline int ApiDef::_internal_out_arg_size() const {
  return _impl_.out_arg_.size();
}
inline int ApiDef::out_arg_size() const {
  return _internal_out_arg_size();
}
inline void ApiDef::clear_out_arg() {
  _impl_.out_arg_.Clear();
}
inline ::tensorflow::ApiDef_Arg* ApiDef::mutable_out_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.out_arg)
  return _impl_.out_arg_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
ApiDef::mutable_out_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.out_arg)
  return &_impl_.out_arg_;
}
inline const ::tensorflow::ApiDef_Arg& ApiDef::_internal_out_arg(int index) const {
  return _impl_.out_arg_.Get(index);
}
inline const ::tensorflow::ApiDef_Arg& ApiDef::out_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.out_arg)
  return _internal_out_arg(index);
}
inline ::tensorflow::ApiDef_Arg* ApiDef::_internal_add_out_arg() {
  return _impl_.out_arg_.Add();
}
inline ::tensorflow::ApiDef_Arg* ApiDef::add_out_arg() {
  ::tensorflow::ApiDef_Arg* _add = _internal_add_out_arg();
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.out_arg)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
ApiDef::out_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.out_arg)
  return _impl_.out_arg_;
}

// repeated string arg_order = 11;
inline int ApiDef::_internal_arg_order_size() const {
  return _impl_.arg_order_.size();
}
inline int ApiDef::arg_order_size() const {
  return _internal_arg_order_size();
}
inline void ApiDef::clear_arg_order() {
  _impl_.arg_order_.Clear();
}
inline std::string* ApiDef::add_arg_order() {
  std::string* _s = _internal_add_arg_order();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ApiDef.arg_order)
  return _s;
}
inline const std::string& ApiDef::_internal_arg_order(int index) const {
  return _impl_.arg_order_.Get(index);
}
inline const std::string& ApiDef::arg_order(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.arg_order)
  return _internal_arg_order(index);
}
inline std::string* ApiDef::mutable_arg_order(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.arg_order)
  return _impl_.arg_order_.Mutable(index);
}
inline void ApiDef::set_arg_order(int index, const std::string& value) {
  _impl_.arg_order_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::set_arg_order(int index, std::string&& value) {
  _impl_.arg_order_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::set_arg_order(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.arg_order_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::set_arg_order(int index, const char* value, size_t size) {
  _impl_.arg_order_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.arg_order)
}
inline std::string* ApiDef::_internal_add_arg_order() {
  return _impl_.arg_order_.Add();
}
inline void ApiDef::add_arg_order(const std::string& value) {
  _impl_.arg_order_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::add_arg_order(std::string&& value) {
  _impl_.arg_order_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::add_arg_order(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.arg_order_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::add_arg_order(const char* value, size_t size) {
  _impl_.arg_order_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ApiDef.arg_order)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ApiDef::arg_order() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.arg_order)
  return _impl_.arg_order_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ApiDef::mutable_arg_order() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.arg_order)
  return &_impl_.arg_order_;
}

// repeated .tensorflow.ApiDef.Attr attr = 6;
inline int ApiDef::_internal_attr_size() const {
  return _impl_.attr_.size();
}
inline int ApiDef::attr_size() const {
  return _internal_attr_size();
}
inline void ApiDef::clear_attr() {
  _impl_.attr_.Clear();
}
inline ::tensorflow::ApiDef_Attr* ApiDef::mutable_attr(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.attr)
  return _impl_.attr_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Attr >*
ApiDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.attr)
  return &_impl_.attr_;
}
inline const ::tensorflow::ApiDef_Attr& ApiDef::_internal_attr(int index) const {
  return _impl_.attr_.Get(index);
}
inline const ::tensorflow::ApiDef_Attr& ApiDef::attr(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.attr)
  return _internal_attr(index);
}
inline ::tensorflow::ApiDef_Attr* ApiDef::_internal_add_attr() {
  return _impl_.attr_.Add();
}
inline ::tensorflow::ApiDef_Attr* ApiDef::add_attr() {
  ::tensorflow::ApiDef_Attr* _add = _internal_add_attr();
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.attr)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef_Attr >&
ApiDef::attr() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.attr)
  return _impl_.attr_;
}

// string summary = 7;
inline void ApiDef::clear_summary() {
  _impl_.summary_.ClearToEmpty();
}
inline const std::string& ApiDef::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.summary)
  return _internal_summary();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef::set_summary(ArgT0&& arg0, ArgT... args) {
 
 _impl_.summary_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.summary)
}
inline std::string* ApiDef::mutable_summary() {
  std::string* _s = _internal_mutable_summary();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.summary)
  return _s;
}
inline const std::string& ApiDef::_internal_summary() const {
  return _impl_.summary_.Get();
}
inline void ApiDef::_internal_set_summary(const std::string& value) {
  
  _impl_.summary_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef::_internal_mutable_summary() {
  
  return _impl_.summary_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.summary)
  return _impl_.summary_.Release();
}
inline void ApiDef::set_allocated_summary(std::string* summary) {
  if (summary != nullptr) {
    
  } else {
    
  }
  _impl_.summary_.SetAllocated(summary, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.summary_.IsDefault()) {
    _impl_.summary_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.summary)
}

// string description = 8;
inline void ApiDef::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& ApiDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.description)
}
inline std::string* ApiDef::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.description)
  return _s;
}
inline const std::string& ApiDef::_internal_description() const {
  return _impl_.description_.Get();
}
inline void ApiDef::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.description)
  return _impl_.description_.Release();
}
inline void ApiDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.description)
}

// string description_prefix = 9;
inline void ApiDef::clear_description_prefix() {
  _impl_.description_prefix_.ClearToEmpty();
}
inline const std::string& ApiDef::description_prefix() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.description_prefix)
  return _internal_description_prefix();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef::set_description_prefix(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_prefix_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.description_prefix)
}
inline std::string* ApiDef::mutable_description_prefix() {
  std::string* _s = _internal_mutable_description_prefix();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.description_prefix)
  return _s;
}
inline const std::string& ApiDef::_internal_description_prefix() const {
  return _impl_.description_prefix_.Get();
}
inline void ApiDef::_internal_set_description_prefix(const std::string& value) {
  
  _impl_.description_prefix_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef::_internal_mutable_description_prefix() {
  
  return _impl_.description_prefix_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef::release_description_prefix() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.description_prefix)
  return _impl_.description_prefix_.Release();
}
inline void ApiDef::set_allocated_description_prefix(std::string* description_prefix) {
  if (description_prefix != nullptr) {
    
  } else {
    
  }
  _impl_.description_prefix_.SetAllocated(description_prefix, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_prefix_.IsDefault()) {
    _impl_.description_prefix_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.description_prefix)
}

// string description_suffix = 10;
inline void ApiDef::clear_description_suffix() {
  _impl_.description_suffix_.ClearToEmpty();
}
inline const std::string& ApiDef::description_suffix() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.description_suffix)
  return _internal_description_suffix();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApiDef::set_description_suffix(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_suffix_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.description_suffix)
}
inline std::string* ApiDef::mutable_description_suffix() {
  std::string* _s = _internal_mutable_description_suffix();
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.description_suffix)
  return _s;
}
inline const std::string& ApiDef::_internal_description_suffix() const {
  return _impl_.description_suffix_.Get();
}
inline void ApiDef::_internal_set_description_suffix(const std::string& value) {
  
  _impl_.description_suffix_.Set(value, GetArenaForAllocation());
}
inline std::string* ApiDef::_internal_mutable_description_suffix() {
  
  return _impl_.description_suffix_.Mutable(GetArenaForAllocation());
}
inline std::string* ApiDef::release_description_suffix() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.description_suffix)
  return _impl_.description_suffix_.Release();
}
inline void ApiDef::set_allocated_description_suffix(std::string* description_suffix) {
  if (description_suffix != nullptr) {
    
  } else {
    
  }
  _impl_.description_suffix_.SetAllocated(description_suffix, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_suffix_.IsDefault()) {
    _impl_.description_suffix_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.description_suffix)
}

// -------------------------------------------------------------------

// ApiDefs

// repeated .tensorflow.ApiDef op = 1;
inline int ApiDefs::_internal_op_size() const {
  return _impl_.op_.size();
}
inline int ApiDefs::op_size() const {
  return _internal_op_size();
}
inline void ApiDefs::clear_op() {
  _impl_.op_.Clear();
}
inline ::tensorflow::ApiDef* ApiDefs::mutable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDefs.op)
  return _impl_.op_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef >*
ApiDefs::mutable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDefs.op)
  return &_impl_.op_;
}
inline const ::tensorflow::ApiDef& ApiDefs::_internal_op(int index) const {
  return _impl_.op_.Get(index);
}
inline const ::tensorflow::ApiDef& ApiDefs::op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDefs.op)
  return _internal_op(index);
}
inline ::tensorflow::ApiDef* ApiDefs::_internal_add_op() {
  return _impl_.op_.Add();
}
inline ::tensorflow::ApiDef* ApiDefs::add_op() {
  ::tensorflow::ApiDef* _add = _internal_add_op();
  // @@protoc_insertion_point(field_add:tensorflow.ApiDefs.op)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ApiDef >&
ApiDefs::op() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDefs.op)
  return _impl_.op_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::ApiDef_Visibility> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::ApiDef_Visibility>() {
  return ::tensorflow::ApiDef_Visibility_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
