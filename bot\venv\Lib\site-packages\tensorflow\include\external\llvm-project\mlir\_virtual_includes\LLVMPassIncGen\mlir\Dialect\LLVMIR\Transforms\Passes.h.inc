/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_DISCOPEFORLLVMFUNCOP
#define GEN_PASS_DECL_LLVMLEGALIZEFOREXPORT
#define GEN_PASS_DECL_LLVMREQUESTCWRAPPERS
#define GEN_PASS_DECL_NVVMOPTIMIZEFORTARGET
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// DIScopeForLLVMFuncOp
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DISCOPEFORLLVMFUNCOP
#undef GEN_PASS_DECL_DISCOPEFORLLVMFUNCOP
#endif // GEN_PASS_DECL_DISCOPEFORLLVMFUNCOP
#ifdef GEN_PASS_DEF_DISCOPEFORLLVMFUNCOP
namespace impl {

template <typename DerivedT>
class DIScopeForLLVMFuncOpBase : public ::mlir::OperationPass<LLVM::LLVMFuncOp> {
public:
  using Base = DIScopeForLLVMFuncOpBase;

  DIScopeForLLVMFuncOpBase() : ::mlir::OperationPass<LLVM::LLVMFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DIScopeForLLVMFuncOpBase(const DIScopeForLLVMFuncOpBase &other) : ::mlir::OperationPass<LLVM::LLVMFuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("ensure-debug-info-scope-on-llvm-func");
  }
  ::llvm::StringRef getArgument() const override { return "ensure-debug-info-scope-on-llvm-func"; }

  ::llvm::StringRef getDescription() const override { return "Materialize LLVM debug info subprogram attribute on every LLVMFuncOp"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DIScopeForLLVMFuncOp");
  }
  ::llvm::StringRef getName() const override { return "DIScopeForLLVMFuncOp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DIScopeForLLVMFuncOpBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DISCOPEFORLLVMFUNCOP
#endif // GEN_PASS_DEF_DISCOPEFORLLVMFUNCOP

//===----------------------------------------------------------------------===//
// LLVMLegalizeForExport
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LLVMLEGALIZEFOREXPORT
#undef GEN_PASS_DECL_LLVMLEGALIZEFOREXPORT
#endif // GEN_PASS_DECL_LLVMLEGALIZEFOREXPORT
#ifdef GEN_PASS_DEF_LLVMLEGALIZEFOREXPORT
namespace impl {

template <typename DerivedT>
class LLVMLegalizeForExportBase : public ::mlir::OperationPass<> {
public:
  using Base = LLVMLegalizeForExportBase;

  LLVMLegalizeForExportBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LLVMLegalizeForExportBase(const LLVMLegalizeForExportBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-legalize-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-legalize-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Legalize LLVM dialect to be convertible to LLVM IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LLVMLegalizeForExport");
  }
  ::llvm::StringRef getName() const override { return "LLVMLegalizeForExport"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LLVMLegalizeForExportBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LLVMLEGALIZEFOREXPORT
#endif // GEN_PASS_DEF_LLVMLEGALIZEFOREXPORT

//===----------------------------------------------------------------------===//
// LLVMRequestCWrappers
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LLVMREQUESTCWRAPPERS
#undef GEN_PASS_DECL_LLVMREQUESTCWRAPPERS
#endif // GEN_PASS_DECL_LLVMREQUESTCWRAPPERS
#ifdef GEN_PASS_DEF_LLVMREQUESTCWRAPPERS
namespace impl {

template <typename DerivedT>
class LLVMRequestCWrappersBase : public ::mlir::OperationPass<::mlir::func::FuncOp> {
public:
  using Base = LLVMRequestCWrappersBase;

  LLVMRequestCWrappersBase() : ::mlir::OperationPass<::mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LLVMRequestCWrappersBase(const LLVMRequestCWrappersBase &other) : ::mlir::OperationPass<::mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-request-c-wrappers");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-request-c-wrappers"; }

  ::llvm::StringRef getDescription() const override { return "Request C wrapper emission for all functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LLVMRequestCWrappers");
  }
  ::llvm::StringRef getName() const override { return "LLVMRequestCWrappers"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LLVMRequestCWrappersBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LLVMREQUESTCWRAPPERS
#endif // GEN_PASS_DEF_LLVMREQUESTCWRAPPERS

//===----------------------------------------------------------------------===//
// NVVMOptimizeForTarget
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_NVVMOPTIMIZEFORTARGET
#undef GEN_PASS_DECL_NVVMOPTIMIZEFORTARGET
#endif // GEN_PASS_DECL_NVVMOPTIMIZEFORTARGET
#ifdef GEN_PASS_DEF_NVVMOPTIMIZEFORTARGET
namespace impl {

template <typename DerivedT>
class NVVMOptimizeForTargetBase : public ::mlir::OperationPass<> {
public:
  using Base = NVVMOptimizeForTargetBase;

  NVVMOptimizeForTargetBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  NVVMOptimizeForTargetBase(const NVVMOptimizeForTargetBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-optimize-for-nvvm-target");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-optimize-for-nvvm-target"; }

  ::llvm::StringRef getDescription() const override { return "Optimize NVVM IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NVVMOptimizeForTarget");
  }
  ::llvm::StringRef getName() const override { return "NVVMOptimizeForTarget"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NVVMOptimizeForTargetBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_NVVMOPTIMIZEFORTARGET
#endif // GEN_PASS_DEF_NVVMOPTIMIZEFORTARGET
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// DIScopeForLLVMFuncOp Registration
//===----------------------------------------------------------------------===//

inline void registerDIScopeForLLVMFuncOp() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::LLVM::createDIScopeForLLVMFuncOpPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDIScopeForLLVMFuncOpPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::LLVM::createDIScopeForLLVMFuncOpPass();
  });
}

//===----------------------------------------------------------------------===//
// LLVMLegalizeForExport Registration
//===----------------------------------------------------------------------===//

inline void registerLLVMLegalizeForExport() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::LLVM::createLegalizeForExportPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLLVMLegalizeForExportPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::LLVM::createLegalizeForExportPass();
  });
}

//===----------------------------------------------------------------------===//
// LLVMRequestCWrappers Registration
//===----------------------------------------------------------------------===//

inline void registerLLVMRequestCWrappers() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::LLVM::createRequestCWrappersPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLLVMRequestCWrappersPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::LLVM::createRequestCWrappersPass();
  });
}

//===----------------------------------------------------------------------===//
// NVVMOptimizeForTarget Registration
//===----------------------------------------------------------------------===//

inline void registerNVVMOptimizeForTarget() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::NVVM::createOptimizeForTargetPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerNVVMOptimizeForTargetPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::NVVM::createOptimizeForTargetPass();
  });
}

//===----------------------------------------------------------------------===//
// LLVM Registration
//===----------------------------------------------------------------------===//

inline void registerLLVMPasses() {
  registerDIScopeForLLVMFuncOp();
  registerLLVMLegalizeForExport();
  registerLLVMRequestCWrappers();
  registerNVVMOptimizeForTarget();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class DIScopeForLLVMFuncOpBase : public ::mlir::OperationPass<LLVM::LLVMFuncOp> {
public:
  using Base = DIScopeForLLVMFuncOpBase;

  DIScopeForLLVMFuncOpBase() : ::mlir::OperationPass<LLVM::LLVMFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DIScopeForLLVMFuncOpBase(const DIScopeForLLVMFuncOpBase &other) : ::mlir::OperationPass<LLVM::LLVMFuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("ensure-debug-info-scope-on-llvm-func");
  }
  ::llvm::StringRef getArgument() const override { return "ensure-debug-info-scope-on-llvm-func"; }

  ::llvm::StringRef getDescription() const override { return "Materialize LLVM debug info subprogram attribute on every LLVMFuncOp"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DIScopeForLLVMFuncOp");
  }
  ::llvm::StringRef getName() const override { return "DIScopeForLLVMFuncOp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DIScopeForLLVMFuncOpBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LLVMLegalizeForExportBase : public ::mlir::OperationPass<> {
public:
  using Base = LLVMLegalizeForExportBase;

  LLVMLegalizeForExportBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LLVMLegalizeForExportBase(const LLVMLegalizeForExportBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-legalize-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-legalize-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Legalize LLVM dialect to be convertible to LLVM IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LLVMLegalizeForExport");
  }
  ::llvm::StringRef getName() const override { return "LLVMLegalizeForExport"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LLVMLegalizeForExportBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LLVMRequestCWrappersBase : public ::mlir::OperationPass<::mlir::func::FuncOp> {
public:
  using Base = LLVMRequestCWrappersBase;

  LLVMRequestCWrappersBase() : ::mlir::OperationPass<::mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LLVMRequestCWrappersBase(const LLVMRequestCWrappersBase &other) : ::mlir::OperationPass<::mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-request-c-wrappers");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-request-c-wrappers"; }

  ::llvm::StringRef getDescription() const override { return "Request C wrapper emission for all functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LLVMRequestCWrappers");
  }
  ::llvm::StringRef getName() const override { return "LLVMRequestCWrappers"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LLVMRequestCWrappersBase<DerivedT>)

protected:
};

template <typename DerivedT>
class NVVMOptimizeForTargetBase : public ::mlir::OperationPass<> {
public:
  using Base = NVVMOptimizeForTargetBase;

  NVVMOptimizeForTargetBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  NVVMOptimizeForTargetBase(const NVVMOptimizeForTargetBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-optimize-for-nvvm-target");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-optimize-for-nvvm-target"; }

  ::llvm::StringRef getDescription() const override { return "Optimize NVVM IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NVVMOptimizeForTarget");
  }
  ::llvm::StringRef getName() const override { return "NVVMOptimizeForTarget"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NVVMOptimizeForTargetBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
